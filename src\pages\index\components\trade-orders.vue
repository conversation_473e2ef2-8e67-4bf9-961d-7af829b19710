<template>
  <no-data v-if="tradeTotal === 0" msg="未查询到成交价格信息" />
  <div v-else>
    <trade-item
      v-for="item in tradeOrders"
      :key="item.id"
      :trade="item"
      @click="onTradeClick(item)"
    ></trade-item>
  </div>
</template>

<script>
import TradeItem from '../../../components/TradeItem.vue'
import router from '../../../router'
import priceManager from '../../../api/priceManager'

export default {
  components: {
    TradeItem
  },
  data() {
    return {
      pageIndex: 0,
      pageSize: 20,
      tradeTotal: -1,
      tradeOrders: []
    }
  },
  created() {},
  methods: {
    loadData() {
      return priceManager.queryHomeTradeOrders(this.pageIndex, this.pageSize).then(res => {
        this.tradeTotal = res.total
        this.tradeOrders = res.list
      })
    },
    onTradeClick(item) {
      router.startTradeOrderDetail(item)
    }
  }
}
</script>
