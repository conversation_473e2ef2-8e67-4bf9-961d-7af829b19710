<template>
  <view class="my_info_root">
    <view class="info_wrapper">
      <div class="info-avatar">
        <div class="info-avatar-container">
          <button
            class="info-avatar-container-btn"
            open-type="chooseAvatar"
            @chooseavatar="onChooseAvatar"
          >
            <image :src="user.avatar || defaultAvatar" t-class="avatarUrl" mode="aspectFill" />
          </button>
          <zui-svg-icon class="icon-camera" icon="camera" width="16px" />
        </div>
        <div class="info-avatar-desc" v-if="user.type === 1">
          <icon type="info" size="16" style="margin-right: 4px" />建议上传你与车子的合影
        </div>
      </div>
      <div class="info-list" style="padding: 11px">
        <view class="info-list-label">我是</view>
        <div class="info-list-container">
          <template v-if="[100, 20, 10].includes(user.type)">
            <view class="text"> {{ ADMIN_TYPES[user.type] }} </view>
          </template>
          <view v-else class="select_box">
            <view
              v-for="(item, key) in COMMON_USER_TYPES"
              :key="key"
              class="option"
              :class="{ checked: user.type === Number(key) }"
              @click="setUserType(Number(key))"
            >
              <view class="mark"></view>
              <view class="text">{{ item }}</view>
            </view>
          </view>
        </div>
      </div>
      <div class="info-list">
        <view class="info-list-label">{{ user.type === 3 ? '企业名称' : '姓名' }}</view>
        <view class="info-list-container">
          <input
            v-model="user.name"
            :placeholder="`请输入${user.type === 3 ? '企业名称' : '姓名'}`"
            type="text"
            maxlength="30"
          />
        </view>
      </div>
      <div class="info-list">
        <view class="info-list-label">手机</view>
        <view class="info-list-container flex flex-v-center flex-between">
          <input
            v-model="user.mobile"
            placeholder="请授权或输入手机号码"
            type="number"
            maxlength="11"
          />
          <button class="info-list-btn" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber">
            授权获取
          </button>
        </view>
      </div>
      <template v-if="user.type === 3">
        <div class="info-list">
          <view class="info-list-label" style="flex: 1 1 100%">在售产品</view>
          <view class="info-list-container" style="margin-left: 0">
            <!-- <checkbox-group class="checkbox-group" @change="selectChange($event, 'saleProducts')">
              <label
                class="uni-list-cell uni-list-cell-pd"
                v-for="(item, key) in productOptions"
                :key="key"
              >
                <checkbox
                  :class="{ actived: item.checked }"
                  :checked="item.checked"
                  :value="item.name"
                />{{ item.name }}
              </label>
            </checkbox-group> -->
            <view class="tags-container">
              <view v-for="(item, index) in extraProducts" :key="index" class="product-tag">
                {{ item }}
                <text class="tag-close" @click="handleRemoveItem('extraProducts', index)">×</text>
              </view>
            </view>
            <div class="input-group" v-if="extraProducts.length <= 20">
              <input
                v-model="currentProduct"
                class="extra_product"
                placeholder="请输入产品设备名称"
                :maxlength="20"
                confirm-type="done"
                confirm-hold
                style="border-radius: 36px 0 0 36px; border-right: none"
                @bindconfirm="
                  handleAddItem(
                    currentProduct,
                    'extraProducts',
                    'currentProduct',
                    '请输入产品设备名称'
                  )
                "
              />
              <button
                class="confirm-btn"
                :disabled="!currentProduct"
                @click="
                  handleAddItem(
                    currentProduct,
                    'extraProducts',
                    'currentProduct',
                    '请输入产品设备名称'
                  )
                "
              >
                添加
              </button>
            </div>
          </view>
        </div>
      </template>
      <template v-if="user.type === 1 || user.type === 2 || user.type === 3">
        <div class="info-list">
          <view class="info-list-label" style="flex: 1 1 100%">{{
            user.type === 3 ? '企业/产品图册' : '图片介绍'
          }}</view>
          <view class="info-list-container upload-container">
            <view v-for="(image, index) in uploadedImages" :key="index" class="uploaded-image">
              <image :src="image" mode="aspectFill" @click="onPhotoPreview(index)" />
              <view class="close_icon delete-btn" @tap.native.stop="deleteImage(index)"></view>
            </view>
            <view v-if="uploadedImages.length < 9" class="upload-box" @click="uploadImages">
              <view class="plus-icon">+</view>
            </view>
          </view>
        </div>
      </template>

      <div class="info-list">
        <view class="info-list-label flex flex-v-center flex-between" style="flex: 1 1 100%"
          >{{ user.type === 1 ? '收购' : '联系' }}地址
          <button class="info-list-btn" @click="onPickAddress">
            <zui-svg-icon icon="choose-add" width="16px" style="display: inline-flex" />选择地址
          </button>
        </view>
        <view
          v-if="user.latitude && user.longitude"
          class="info-list-container"
          style="margin-left: 0"
        >
          <textarea v-model="user.address" placeholder="请输入详细地址" />
        </view>
      </div>
      <template v-if="user.type === 1">
        <div class="info-list">
          <view class="info-list-label" style="flex: 1 1 100%">收购品类（可多选） </view>
          <view class="info-list-container" style="margin-left: 0">
            <checkbox-group class="checkbox-group" @change="selectChange($event, 'purchaseCrops')">
              <label class="uni-list-cell uni-list-cell-pd" v-for="(item, key) in crops" :key="key">
                <checkbox
                  :class="{ actived: item.checked }"
                  :checked="item.checked"
                  :value="item.id"
                />{{ item.name }}
              </label>
            </checkbox-group>
            <view class="tags-container">
              <view v-for="(item, index) in extraPurchaseCrops" :key="index" class="product-tag">
                {{ item }}
                <text class="tag-close" @click="handleRemoveItem('extraPurchaseCrops', index)"
                  >×</text
                >
              </view>
            </view>
            <div class="input-group" v-if="extraPurchaseCrops.length <= 20">
              <input
                v-model="currentCrop"
                class="extra_product"
                placeholder="请输入收购品类"
                :maxlength="20"
                confirm-type="done"
                confirm-hold
                style="border-radius: 36px 0 0 36px; border-right: none"
                @bindconfirm="
                  handleAddItem(currentCrop, 'extraPurchaseCrops', 'currentCrop', '请输入收购品类')
                "
              />
              <button
                class="confirm-btn"
                :disabled="!currentCrop"
                @click="
                  handleAddItem(currentCrop, 'extraPurchaseCrops', 'currentCrop', '请输入收购品类')
                "
              >
                添加
              </button>
            </div>
          </view>
        </div>
        <div class="info-list">
          <view class="info-list-label" style="flex: 1 1 100%">运输车辆（可多选） </view>
          <view class="info-list-container" style="margin-left: 0">
            <checkbox-group class="checkbox-group" @change="selectChange($event, 'transportCars')">
              <label
                class="uni-list-cell uni-list-cell-pd"
                v-for="(item, key) in transportOptions"
                :key="key"
              >
                <checkbox
                  :class="{ actived: item.checked }"
                  :checked="item.checked"
                  :value="item.name"
                />{{ item.name }}
              </label>
            </checkbox-group>
            <view class="tags-container">
              <view v-for="(item, index) in extraCars" :key="index" class="product-tag">
                {{ item }}
                <text class="tag-close" @click="handleRemoveItem('extraCars', index)">×</text>
              </view>
            </view>
            <div class="input-group" v-if="extraCars.length <= 20">
              <input
                v-model="currentCar"
                class="extra_product"
                placeholder="请输入运输车辆"
                :maxlength="20"
                confirm-type="done"
                confirm-hold
                style="border-radius: 36px 0 0 36px; border-right: none"
                @bindconfirm="
                  handleAddItem(currentCar, 'extraCars', 'currentCar', '请输入运输车辆')
                "
              />
              <button
                class="confirm-btn"
                :disabled="!currentCar"
                @click="handleAddItem(currentCar, 'extraCars', 'currentCar', '请输入运输车辆')"
              >
                添加
              </button>
            </div>
          </view>
        </div>
      </template>
      <div class="info-list">
        <view class="info-list-label" style="flex: 1 1 100%">其它（描述）</view>
        <view class="info-list-container" style="margin-left: 0">
          <textarea
            v-model="user.description"
            :placeholder="descPlaceholderLabels[user.type] || '填写描述信息，非必填'"
          />
        </view>
      </div>
    </view>
    <view class="fix_bottom_btn" @click="save">保 存</view>
  </view>
</template>

<script>
const QQMapWX = require('../../utils/qqmap-wx-jssdk')
const mapSdk = new QQMapWX({ key: '7C6BZ-6SLKU-OPUVH-2HYAV-JCJSZ-TFB2M' })
import userManager from '../../api/userManager'
import orderManager from '../../api/orderManager'
import configManager from '../../api/configManager'
import {
  ADMIN_TYPES,
  COMMON_USER_TYPES,
  TRANSPORT_VEHICLES,
  EQUIPMENT_PRODUCTS
} from './constants/index'

export default {
  data() {
    return {
      ADMIN_TYPES,
      COMMON_USER_TYPES,
      defaultAvatar: '/static/images/user_default.png',
      transportOptions: TRANSPORT_VEHICLES,
      productOptions: EQUIPMENT_PRODUCTS,
      user: {
        type: 0,
        name: '',
        avatar: '',
        gender: 0,
        mobile: '',
        address: '',
        latitude: 0,
        longitude: 0,
        cityCode: 0,
        purchaseCrops: [],
        transportCars: [],
        description: '',
        images: '',
        saleProducts: []
      },
      crops: [],
      currentCar: '',
      extraCars: [],
      currentProduct: '',
      extraProducts: [],
      currentCrop: '',
      extraPurchaseCrops: [],
      descPlaceholderLabels: {
        1: '可填写收购要求、几个人合伙等，非必填'
      },
      uploadedImages: []
    }
  },
  async onLoad() {
    uni.showLoading({ title: '加载中...', mask: true })
    try {
      const [res, cropsInfo] = await Promise.all([
        userManager.refreshUser(),
        orderManager.fetchCrops()
      ])
      const processStringList = (str, processor = v => v) =>
        (str || '').split(',').filter(Boolean).map(processor)

      const [purchaseCrops, transportCars, saleProducts] = [
        processStringList(res.purchaseCrops, item => {
          const num = Number(item)
          if (!isNaN(num) && /^[-+]?\d+(\.\d+)?$/.test(item.trim())) {
            return num
          }
          return item
        }),
        processStringList(res.transportCars),
        processStringList(res.saleProducts)
      ]
      this.uploadedImages = processStringList(res.images)

      this.extraProducts = saleProducts

      this.crops = cropsInfo.crops
        .filter(item => item.parentId === 0)
        .map(item => ({
          ...item,
          checked: purchaseCrops.includes(item.id)
        }))
      this.extraPurchaseCrops = purchaseCrops.filter(
        item => !this.crops.some(crop => crop.id === item)
      )
      this.transportOptions = this.transportOptions.map(item => ({
        ...item,
        checked: transportCars.includes(item.name)
      }))
      this.extraCars = transportCars.filter(
        item => !this.transportOptions.some(car => car.name === item)
      )

      // this.productOptions = this.productOptions.map(item => ({
      //   ...item,
      //   checked: saleProducts.includes(item.name)
      // }))
      // this.extraProduct = saleProducts
      //   .filter(item => !this.productOptions.some(product => product.name === item))
      //   .join(',')
      this.user = {
        ...res,
        purchaseCrops: purchaseCrops.filter(item => this.crops.some(crop => crop.id === item)),
        transportCars: transportCars.filter(item =>
          this.transportOptions.some(car => car.name === item)
        ),
        saleProducts
      }
    } catch (err) {
      console.error('Error in onLoad:', err)
    } finally {
      uni.hideLoading()
    }
  },

  methods: {
    onPickAddress() {
      uni.chooseLocation({
        success: res => {
          const { latitude, longitude, address } = res
          this.user.latitude = latitude
          this.user.longitude = longitude
          this.user.address = address
          this.getCityCode(latitude, longitude)
        },
        fail: err => {
          if (err.errMsg === 'chooseLocation:fail auth deny') {
            uni.showModal({
              title: '提示',
              content: '请在设置中打开位置权限',
              showCancel: false
            })
          } else if (err.errMsg === 'chooseLocation:fail cancel') {
            return
          }
          uni.showModal({
            title: '提示',
            content: '位置信息获取失败',
            showCancel: false
          })
        }
      })
    },
    getCityCode(latitude, longitude) {
      mapSdk.reverseGeocoder({
        location: { latitude, longitude },
        success: res => {
          let { adcode } = res.result.ad_info
          this.user.cityCode = adcode * 1000000
        }
      })
    },
    setUserType(type) {
      this.user.type = type
      // if (type === 3) {
      //   uni.showModal({
      //     title: '温馨提示',
      //     content: '注册为设备商并获得曝光，请联系平台运营人员',
      //     showCancel: false
      //   })
      // }
    },

    async onChooseAvatar(e) {
      try {
        uni.showLoading({ title: '更新中...', mask: true })
        const { avatarUrl } = e.detail
        const { url: avatar } = await configManager.uploadWxFile(avatarUrl, 'images/user-avatar')
        this.user = { ...this.user, avatar }
      } catch (error) {
        throw error
      } finally {
        uni.hideLoading()
      }
    },
    getPhoneNumber(e) {
      const { code, errMsg } = e.detail
      if (errMsg === 'getPhoneNumber:ok') {
        uni.showLoading({ title: '获取中...' })
        userManager
          .getWxPhone(code)
          .then(data => {
            const { phoneNumber, purePhoneNumber } = data
            const mobile = phoneNumber || purePhoneNumber
            this.user.mobile = mobile
          })
          .catch(err => {
            uni.showToast({ title: '手机号码获取失败，请手动输入！', icon: 'none' })
          })
          .then(() => {
            uni.hideLoading()
          })
      } else {
        uni.showToast({ title: '您未同意授权获取手机号，请手动输入！', icon: 'none' })
      }
    },

    handleAddItem(currentValue, arrayName, fieldName, errorMsg) {
      const val = currentValue.replace(/[\s\u3000]/g, '')
      if (!val) {
        uni.showToast({ title: errorMsg || '请输入内容', icon: 'none' })
        return false
      }
      this[arrayName].push(val)
      this[fieldName] = ''
      return true
    },

    handleRemoveItem(arrayName, index) {
      this[arrayName].splice(index, 1)
    },
    selectChange(evt, key) {
      this.user[key] = evt.detail.value
    },
    checkData() {
      return new Promise((resolve, reject) => {
        const validations = [
          { key: 'avatar', message: `请上传头像` },
          { key: 'name', message: `请输入${this.user.type === 3 ? '企业名称' : '姓名'}` },
          { key: 'mobile', message: '请输入手机号', regex: /^1[3-9]\d{9}$/ }
        ]
        if (!this.user.latitude || !this.user.longitude || !this.user.address) {
          validations.push({ key: 'address', message: `请点击“选择地址”按钮获取地址` })
        }
        if (this.user.type === 1) {
          validations.push(
            { key: 'purchaseCrops', message: `请选择收购品类` },
            { key: 'transportCars', message: `请选择运输车辆` }
          )
        }
        if (this.user.type === 3) {
          validations.push({ key: 'saleProducts', message: `请选择在售产品` })
        }
        for (const validation of validations) {
          const value = this.user[validation.key]
          if (
            value === undefined ||
            value === null ||
            value === '' ||
            (Array.isArray(value) && value.length === 0)
          ) {
            throw new Error(validation.message)
          }
          if (validation.regex && !validation.regex.test(value)) {
            throw new Error(`${validation.key} 格式不正确`)
          }
        }
        resolve(true)
      })
    },
    async save() {
      uni.showLoading({ title: '保存中', mask: true })
      try {
        if (this.uploadedImages.length > 0) {
          const { urls, success } = await this.uploadUserImages()
          if (success === 0) throw new Error('所有图片上传失败')
          this.user.images = urls.filter(Boolean).join(',')
        }

        if (this.extraPurchaseCrops.length > 0) {
          this.user.purchaseCrops = [...this.user.purchaseCrops, ...this.extraPurchaseCrops]
        }
        if (this.extraCars.length > 0) {
          this.user.transportCars = [...this.user.transportCars, ...this.extraCars]
        }
        if (this.extraProducts.length > 0) {
          this.user.saleProducts = this.extraProducts.join(',')
        }
        await this.checkData()
        if (this.user.type === 3) {
          const { isPaid } = this.user
          this.user.isPaid = typeof isPaid === 'number' ? ([0, 1].includes(isPaid) ? isPaid : 1) : 1
        }
        await userManager.updateMyProfile(this.user)
        uni.showToast({
          title: '更新成功',
          icon: 'success',
          duration: 2000
        })
        setTimeout(() => uni.navigateBack(), 2000)
      } catch (error) {
        uni.showModal({
          title: '提示',
          content: error.message || '保存失败，请重试',
          showCancel: false
        })
      } finally {
        uni.hideLoading()
      }
    },

    async uploadUserImages() {
      try {
        const results = await Promise.allSettled(
          this.uploadedImages.map(async image => {
            try {
              if (
                image.startsWith('https://') ||
                (image.startsWith('http://') && !image.startsWith('http://tmp'))
              ) {
                console.log('跳过远程图片上传:', image)
                return image
              }
              const { url } = await configManager.uploadWxFile(image, 'images/equipment')
              return url
            } catch (error) {
              console.error('图片上传失败:', image, error)
              return null
            }
          })
        )

        const validUrls = results.filter(r => r.status === 'fulfilled' && r.value).map(r => r.value)
        return { urls: validUrls, success: validUrls.length, total: this.uploadedImages.length }
      } catch (error) {
        throw new Error('图片上传服务异常，请稍后重试')
      }
    },

    uploadImages() {
      uni.chooseImage({
        count: 9,
        sizeType: ['original', 'compressed'],
        sourceType: ['album', 'camera'],
        success: res => {
          this.uploadedImages = this.uploadedImages.concat(res.tempFilePaths)
        }
      })
    },
    onPhotoPreview(index) {
      const urls = this.uploadedImages
      const current = urls[index]
      uni.previewImage({ urls, current })
    },
    deleteImage(index) {
      this.uploadedImages.splice(index, 1)
    }
  }
}
</script>

<style lang="scss">
.flex {
  display: flex;
}

.flex-v-center {
  align-items: center;
}

.flex-between {
  justify-content: space-between;
}

.my_info_root {
  min-height: 100vh;
  background: #f6f6f6;
  padding-bottom: 1px;
}

.info_wrapper {
  background: #fff;
  margin-bottom: 90px;
}

.info-list {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  padding: 10px 16px;
  border-bottom: 10px solid #f6f6f6;
  &-label {
    color: #666;
  }
  &-container {
    flex: 1;
    margin-left: 8px;
  }
  input {
    padding: 6px;
  }
  textarea {
    margin-top: 10px;
    background-color: #f6f6f6;
    font-size: 16px;
    line-height: 24px;
    padding: 10px;
    border-radius: 6px;
    width: 100%;
    box-sizing: border-box;
    height: 80px;
  }
}
.info-list-btn {
  display: inline-flex;
  align-items: center;
  font-size: 13px;
  width: auto !important;
  font-weight: 500;
  padding: 0 10px;
  height: 28px;
  line-height: 28px;
  border-radius: 28px;
  border: 1px solid #ff6a01;
  color: #ff6a01;
  background-color: #fff;
  margin: 0 !important;
}

.info-avatar {
  padding: 10px 16px;
  border-bottom: 10px solid #f6f6f6;
  .info-avatar-container {
    position: relative;
    margin-bottom: 20px;
    &-btn {
      width: fit-content !important;
      padding: 0;
      margin: 0 auto !important;
      image {
        display: block;
        width: 80px;
        height: 80px;
      }
    }
    .icon-camera {
      display: inline-flex;
      position: absolute;
      bottom: -10px;
      right: calc(50% - 50px);
      background-color: #d6d6d6;
      padding: 6px;
      border-radius: 50%;
      border: 1px solid #fff;
      box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
    }
  }

  &-desc {
    font-size: 13px;
    color: #999;
    text-align: center;
    margin-top: 20px;
  }
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin: 6px 0;
}

.product-tag {
  border-color: #ff6a01;
  color: #fff;
  // font-weight: 700;
  background-color: #ff6a01;
  padding: 4px 4px 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  position: relative;
}

.tag-close {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  flex: 1;
  width: 16px;
  height: 16px;
  background: #eee;
  border-radius: 50%;
  margin-left: 8px;
  color: #666;
  cursor: pointer;
}

.input-group {
  display: flex;
  align-items: center;
}

.extra_product {
  flex: 1;
  border-radius: 36px 0 0 36px !important;
  border: 1px solid #ccc;
  border-right: none;
  padding: 0 12px !important;
  font-size: 13px;
  height: 36px;
  line-height: 36px;
  box-sizing: border-box;
}

.confirm-btn {
  width: 80px !important;
  background: #ff6a01 !important;
  color: white !important;
  border-radius: 0 36px 36px 0;
  border: 1px solid #ccc;
  border-left: none;
  font-size: 14px;
  height: 36px;
  line-height: 36px;
  padding: 0;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
}

.confirm-btn[disabled] {
  opacity: 0.6;
  background: #ff9d55;
  border-color: #ff9d55;
}

.select_box {
  .option {
    padding-left: 24px;
    font-size: 16px;
    position: relative;
    line-height: 2;
    display: inline-block;
    margin-right: 15px;
    &:last-child {
      margin-right: 0;
    }
    .mark {
      width: 18px;
      height: 18px;
      border: 1px solid #999;
      border-radius: 50%;
      margin-top: -10px;
      position: absolute;
      top: 50%;
      left: 0;
    }
    &.checked {
      .mark {
        border-color: #ff6a01;
        &::after {
          content: '';
          width: 10px;
          height: 10px;
          background-color: #ff6a01;
          border-radius: 50%;
          position: absolute;
          top: 4px;
          left: 4px;
        }
      }
    }
  }
}
.checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin: 6px 0;
  align-items: center;
  overflow-x: auto;
  word-break: keep-all;
  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari 和 Opera */
  }
  -ms-overflow-style: none; /* IE 和 Edge */
  scrollbar-width: none; /* Firefox */
  label {
    border: 1px solid #ccc;
    border-radius: 16px;
    padding: 4px 12px;
    font-size: 12px;
    &[aria-checked='true'] {
      border-color: #ff6a01;
      color: #fff;
      background-color: #ff6a01;
    }
    checkbox {
      display: none;
    }
  }
}
.extra_car {
  border: 1px solid #ccc;
  border-radius: 36px;
  padding: 0 12px !important;
  margin-top: 6px;
  font-size: 13px;
  height: 36px;
  line-height: 36px;
}

.upload-container {
  display: flex;
  flex-wrap: wrap;
  margin: 5px -5px 0;
}

.upload-box {
  width: 77px;
  height: 77px;
  border: 1px dashed #ccc;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 5px;
  position: relative;
  cursor: pointer;
}

.upload-box .plus-icon {
  font-size: 24px;
  color: #ccc;
}

.uploaded-image {
  width: 77px;
  height: 77px;
  margin: 5px;
  position: relative;
  border-radius: 4px;
  overflow: hidden;
  image {
    width: 100%;
    height: 100%;
  }
  .delete-btn {
    width: 20px;
    height: 20px;
    position: absolute;
    top: 2px;
    right: 2px;
  }
}
</style>
