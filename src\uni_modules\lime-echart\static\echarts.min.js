!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).echarts={})}(this,function(t){"use strict";var _=function(t,e){return(_=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(t,e){t.__proto__=e}:function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])}))(t,e)};function u(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}_(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}var x=function(){this.firefox=!1,this.ie=!1,this.edge=!1,this.newEdge=!1,this.weChat=!1},b=new function(){this.browser=new x,this.node=!1,this.wxa=!1,this.worker=!1,this.svgSupported=!1,this.touchEventsSupported=!1,this.pointerEventsSupported=!1,this.domSupported=!1,this.transformSupported=!1,this.transform3dSupported=!1,this.hasGlobalWindow="undefined"!=typeof window};"object"==typeof wx&&"function"==typeof wx.getSystemInfoSync?(b.wxa=!0,b.touchEventsSupported=!0):"undefined"==typeof document&&"undefined"!=typeof self?b.worker=!0:"undefined"==typeof navigator?(b.node=!0,b.svgSupported=!0):(J=navigator.userAgent,ne=(Ft=b).browser,rt=J.match(/Firefox\/([\d.]+)/),W=J.match(/MSIE\s([\d.]+)/)||J.match(/Trident\/.+?rv:(([\d.]+))/),Q=J.match(/Edge?\/([\d.]+)/),J=/micromessenger/i.test(J),rt&&(ne.firefox=!0,ne.version=rt[1]),W&&(ne.ie=!0,ne.version=W[1]),Q&&(ne.edge=!0,ne.version=Q[1],ne.newEdge=18<+Q[1].split(".")[0]),J&&(ne.weChat=!0),Ft.svgSupported="undefined"!=typeof SVGRect,Ft.touchEventsSupported="ontouchstart"in window&&!ne.ie&&!ne.edge,Ft.pointerEventsSupported="onpointerdown"in window&&(ne.edge||ne.ie&&11<=+ne.version),Ft.domSupported="undefined"!=typeof document,rt=document.documentElement.style,Ft.transform3dSupported=(ne.ie&&"transition"in rt||ne.edge||"WebKitCSSMatrix"in window&&"m11"in new WebKitCSSMatrix||"MozPerspective"in rt)&&!("OTransition"in rt),Ft.transformSupported=Ft.transform3dSupported||ne.ie&&9<=+ne.version);var K="12px sans-serif";var w,T,C=function(t){var e={};if("undefined"!=typeof JSON)for(var n=0;n<t.length;n++){var i=String.fromCharCode(n+32),r=(t.charCodeAt(n)-20)/100;e[i]=r}return e}("007LLmW'55;N0500LLLLLLLLLL00NNNLzWW\\\\WQb\\0FWLg\\bWb\\WQ\\WrWWQ000CL5LLFLL0LL**F*gLLLL5F0LF\\FFF5.5N"),H={createCanvas:function(){return"undefined"!=typeof document&&document.createElement("canvas")},measureText:function(t,e){if(w||(n=H.createCanvas(),w=n&&n.getContext("2d")),w)return T!==e&&(T=w.font=e||K),w.measureText(t);t=t||"",e=e||K;var n=/^([0-9]*?)px$/.exec(e),i=+(n&&n[1])||12,r=0;if(0<=e.indexOf("mono"))r=i*t.length;else for(var o=0;o<t.length;o++){var a=C[t[o]];r+=null==a?i:a*i}return{width:r}},loadImage:function(t,e,n){var i=new Image;return i.onload=e,i.onerror=n,i.src=t,i}};function D(t){for(var e in H)t[e]&&(H[e]=t[e])}var A=lt(["Function","RegExp","Date","Error","CanvasGradient","CanvasPattern","Image","Canvas"],function(t,e){return t["[object "+e+"]"]=!0,t},{}),P=lt(["Int8","Uint8","Uint8Clamped","Int16","Uint16","Int32","Uint32","Float32","Float64"],function(t,e){return t["[object "+e+"Array]"]=!0,t},{}),G=Object.prototype.toString,W=Array.prototype,U=W.forEach,q=W.filter,Z=W.slice,$=W.map,Q=function(){}.constructor,J=Q?Q.prototype:null,tt="__proto__",et=2311;function nt(){return et++}function it(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];"undefined"!=typeof console&&console.error.apply(console,t)}function y(t){if(null==t||"object"!=typeof t)return t;var e=t,n=G.call(t);if("[object Array]"===n){if(!At(t))for(var e=[],i=0,r=t.length;i<r;i++)e[i]=y(t[i])}else if(P[n]){if(!At(t)){var o=t.constructor;if(o.from)e=o.from(t);else{e=new o(t.length);for(i=0,r=t.length;i<r;i++)e[i]=t[i]}}}else if(!A[n]&&!At(t)&&!mt(t))for(var a in e={},t)t.hasOwnProperty(a)&&a!==tt&&(e[a]=y(t[a]));return e}function d(t,e,n){if(!R(e)||!R(t))return n?y(e):t;for(var i in e){var r,o;e.hasOwnProperty(i)&&i!==tt&&(r=t[i],!R(o=e[i])||!R(r)||F(o)||F(r)||mt(o)||mt(r)||gt(o)||gt(r)||At(o)||At(r)?!n&&i in t||(t[i]=y(e[i])):d(r,o,n))}return t}function L(t,e){if(Object.assign)Object.assign(t,e);else for(var n in e)e.hasOwnProperty(n)&&n!==tt&&(t[n]=e[n]);return t}function E(t,e,n){for(var i=ht(e),r=0;r<i.length;r++){var o=i[r];(n?null!=e[o]:null==t[o])&&(t[o]=e[o])}return t}var rt=H.createCanvas;function I(t,e){if(t){if(t.indexOf)return t.indexOf(e);for(var n=0,i=t.length;n<i;n++)if(t[n]===e)return n}return-1}function ot(t,e){var n,i=t.prototype;function r(){}for(n in r.prototype=e.prototype,t.prototype=new r,i)i.hasOwnProperty(n)&&(t.prototype[n]=i[n]);(t.prototype.constructor=t).superClass=e}function at(t,e,n){if(t="prototype"in t?t.prototype:t,e="prototype"in e?e.prototype:e,Object.getOwnPropertyNames)for(var i=Object.getOwnPropertyNames(e),r=0;r<i.length;r++){var o=i[r];"constructor"!==o&&(n?null!=e[o]:null==t[o])&&(t[o]=e[o])}else E(t,e,n)}function st(t){return!!t&&"string"!=typeof t&&"number"==typeof t.length}function O(t,e,n){if(t&&e)if(t.forEach&&t.forEach===U)t.forEach(e,n);else if(t.length===+t.length)for(var i=0,r=t.length;i<r;i++)e.call(n,t[i],i,t);else for(var o in t)t.hasOwnProperty(o)&&e.call(n,t[o],o,t)}function B(t,e,n){if(!t)return[];if(!e)return Mt(t);if(t.map&&t.map===$)return t.map(e,n);for(var i=[],r=0,o=t.length;r<o;r++)i.push(e.call(n,t[r],r,t));return i}function lt(t,e,n,i){if(t&&e){for(var r=0,o=t.length;r<o;r++)n=e.call(i,n,t[r],r,t);return n}}function ut(t,e,n){if(!t)return[];if(!e)return Mt(t);if(t.filter&&t.filter===q)return t.filter(e,n);for(var i=[],r=0,o=t.length;r<o;r++)e.call(n,t[r],r,t)&&i.push(t[r]);return i}function ht(t){if(!t)return[];if(Object.keys)return Object.keys(t);var e,n=[];for(e in t)t.hasOwnProperty(e)&&n.push(e);return n}var ct=J&&S(J.bind)?J.call.bind(J.bind):function(t,e){for(var n=[],i=2;i<arguments.length;i++)n[i-2]=arguments[i];return function(){return t.apply(e,n.concat(Z.call(arguments)))}};function pt(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];return function(){return t.apply(this,e.concat(Z.call(arguments)))}}function F(t){return Array.isArray?Array.isArray(t):"[object Array]"===G.call(t)}function S(t){return"function"==typeof t}function V(t){return"string"==typeof t}function dt(t){return"[object String]"===G.call(t)}function ft(t){return"number"==typeof t}function R(t){var e=typeof t;return"function"==e||!!t&&"object"==e}function gt(t){return!!A[G.call(t)]}function yt(t){return!!P[G.call(t)]}function mt(t){return"object"==typeof t&&"number"==typeof t.nodeType&&"object"==typeof t.ownerDocument}function vt(t){return null!=t.colorStops}function _t(t){return null!=t.image}function xt(t){return"[object RegExp]"===G.call(t)}function wt(t){return t!=t}function bt(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];for(var n=0,i=t.length;n<i;n++)if(null!=t[n])return t[n]}function N(t,e){return null!=t?t:e}function St(t,e,n){return null!=t?t:null!=e?e:n}function Mt(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];return Z.apply(t,e)}function Tt(t){var e;return"number"==typeof t?[t,t,t,t]:2===(e=t.length)?[t[0],t[1],t[0],t[1]]:3===e?[t[0],t[1],t[2],t[1]]:t}function Ct(t,e){if(!t)throw new Error(e)}function It(t){return null==t?null:"function"==typeof t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}var kt="__ec_primitive__";function Dt(t){t[kt]=!0}function At(t){return t[kt]}Lt.prototype.get=function(t){return this.data.hasOwnProperty(t)?this.data[t]:null},Lt.prototype.set=function(t,e){return this.data[t]=e},Lt.prototype.each=function(t,e){for(var n in this.data)this.data.hasOwnProperty(n)&&t.call(e,this.data[n],n)},Lt.prototype.keys=function(){return ht(this.data)},Lt.prototype.removeKey=function(t){delete this.data[t]};var Pt=Lt;function Lt(t){this.data={};var n=F(t),i=(this.data={},this);function e(t,e){n?i.set(t,e):i.set(e,t)}t instanceof Lt?t.each(e):t&&O(t,e)}function z(t){return new Pt(t)}function Ot(t,e){for(var n=new t.constructor(t.length+e.length),i=0;i<t.length;i++)n[i]=t[i];for(var r=t.length,i=0;i<e.length;i++)n[i+r]=e[i];return n}function Rt(t,e){var n,t=Object.create?Object.create(t):((n=function(){}).prototype=t,new n);return e&&L(t,e),t}function Nt(t){t=t.style;t.webkitUserSelect="none",t.userSelect="none",t.webkitTapHighlightColor="rgba(0,0,0,0)",t["-webkit-touch-callout"]="none"}function zt(t,e){return t.hasOwnProperty(e)}function Et(){}var Bt=180/Math.PI,Ft=Object.freeze({__proto__:null,HashMap:Pt,RADIAN_TO_DEGREE:Bt,assert:Ct,bind:ct,clone:y,concatArray:Ot,createCanvas:rt,createHashMap:z,createObject:Rt,curry:pt,defaults:E,disableUserSelect:Nt,each:O,eqNaN:wt,extend:L,filter:ut,find:function(t,e,n){if(t&&e)for(var i=0,r=t.length;i<r;i++)if(e.call(n,t[i],i,t))return t[i]},guid:nt,hasOwn:zt,indexOf:I,inherits:ot,isArray:F,isArrayLike:st,isBuiltInObject:gt,isDom:mt,isFunction:S,isGradientObject:vt,isImagePatternObject:_t,isNumber:ft,isObject:R,isPrimitive:At,isRegExp:xt,isString:V,isStringSafe:dt,isTypedArray:yt,keys:ht,logError:it,map:B,merge:d,mergeAll:function(t,e){for(var n=t[0],i=1,r=t.length;i<r;i++)n=d(n,t[i],e);return n},mixin:at,noop:Et,normalizeCssArray:Tt,reduce:lt,retrieve:bt,retrieve2:N,retrieve3:St,setAsPrimitive:Dt,slice:Mt,trim:It});function Vt(t,e){return[t=null==t?0:t,e=null==e?0:e]}function Ht(t){return[t[0],t[1]]}function Gt(t,e,n){return t[0]=e[0]+n[0],t[1]=e[1]+n[1],t}function Wt(t,e,n){return t[0]=e[0]-n[0],t[1]=e[1]-n[1],t}function Ut(t){return Math.sqrt(Xt(t))}function Xt(t){return t[0]*t[0]+t[1]*t[1]}function Yt(t,e,n){return t[0]=e[0]*n,t[1]=e[1]*n,t}function qt(t,e){var n=Ut(e);return 0===n?(t[0]=0,t[1]=0):(t[0]=e[0]/n,t[1]=e[1]/n),t}function Zt(t,e){return Math.sqrt((t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1]))}var jt=Zt;function Kt(t,e){return(t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1])}var $t=Kt;function Qt(t,e,n,i){return t[0]=e[0]+i*(n[0]-e[0]),t[1]=e[1]+i*(n[1]-e[1]),t}function Jt(t,e,n){var i=e[0],e=e[1];return t[0]=n[0]*i+n[2]*e+n[4],t[1]=n[1]*i+n[3]*e+n[5],t}function te(t,e,n){return t[0]=Math.min(e[0],n[0]),t[1]=Math.min(e[1],n[1]),t}function ee(t,e,n){return t[0]=Math.max(e[0],n[0]),t[1]=Math.max(e[1],n[1]),t}var ne=Object.freeze({__proto__:null,add:Gt,applyTransform:Jt,clone:Ht,copy:function(t,e){return t[0]=e[0],t[1]=e[1],t},create:Vt,dist:jt,distSquare:$t,distance:Zt,distanceSquare:Kt,div:function(t,e,n){return t[0]=e[0]/n[0],t[1]=e[1]/n[1],t},dot:function(t,e){return t[0]*e[0]+t[1]*e[1]},len:Ut,lenSquare:Xt,length:Ut,lengthSquare:Xt,lerp:Qt,max:ee,min:te,mul:function(t,e,n){return t[0]=e[0]*n[0],t[1]=e[1]*n[1],t},negate:function(t,e){return t[0]=-e[0],t[1]=-e[1],t},normalize:qt,scale:Yt,scaleAndAdd:function(t,e,n,i){return t[0]=e[0]+n[0]*i,t[1]=e[1]+n[1]*i,t},set:function(t,e,n){return t[0]=e,t[1]=n,t},sub:Wt}),ie=function(t,e){this.target=t,this.topTarget=e&&e.topTarget},re=(oe.prototype._dragStart=function(t){for(var e=t.target;e&&!e.draggable;)e=e.parent||e.__hostTarget;e&&((this._draggingTarget=e).dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.handler.dispatchToElement(new ie(e,t),"dragstart",t.event))},oe.prototype._drag=function(t){var e,n,i,r,o=this._draggingTarget;o&&(e=t.offsetX,n=t.offsetY,i=e-this._x,r=n-this._y,this._x=e,this._y=n,o.drift(i,r,t),this.handler.dispatchToElement(new ie(o,t),"drag",t.event),i=this.handler.findHover(e,n,o).target,r=this._dropTarget,o!==(this._dropTarget=i))&&(r&&i!==r&&this.handler.dispatchToElement(new ie(r,t),"dragleave",t.event),i)&&i!==r&&this.handler.dispatchToElement(new ie(i,t),"dragenter",t.event)},oe.prototype._dragEnd=function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.handler.dispatchToElement(new ie(e,t),"dragend",t.event),this._dropTarget&&this.handler.dispatchToElement(new ie(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null},oe);function oe(t){(this.handler=t).on("mousedown",this._dragStart,this),t.on("mousemove",this._drag,this),t.on("mouseup",this._dragEnd,this)}se.prototype.on=function(t,e,n,i){this._$handlers||(this._$handlers={});var r=this._$handlers;if("function"==typeof e&&(i=n,n=e,e=null),n&&t){var o=this._$eventProcessor;null!=e&&o&&o.normalizeQuery&&(e=o.normalizeQuery(e)),r[t]||(r[t]=[]);for(var a=0;a<r[t].length;a++)if(r[t][a].h===n)return this;o={h:n,query:e,ctx:i||this,callAtLast:n.zrEventfulCallAtLast},e=r[t].length-1,i=r[t][e];i&&i.callAtLast?r[t].splice(e,0,o):r[t].push(o)}return this},se.prototype.isSilent=function(t){var e=this._$handlers;return!e||!e[t]||!e[t].length},se.prototype.off=function(t,e){var n=this._$handlers;if(n)if(t)if(e){if(n[t]){for(var i=[],r=0,o=n[t].length;r<o;r++)n[t][r].h!==e&&i.push(n[t][r]);n[t]=i}n[t]&&0===n[t].length&&delete n[t]}else delete n[t];else this._$handlers={};return this},se.prototype.trigger=function(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];if(this._$handlers){var i=this._$handlers[t],r=this._$eventProcessor;if(i)for(var o=e.length,a=i.length,s=0;s<a;s++){var l=i[s];if(!r||!r.filter||null==l.query||r.filter(t,l.query))switch(o){case 0:l.h.call(l.ctx);break;case 1:l.h.call(l.ctx,e[0]);break;case 2:l.h.call(l.ctx,e[0],e[1]);break;default:l.h.apply(l.ctx,e)}}r&&r.afterTrigger&&r.afterTrigger(t)}return this},se.prototype.triggerWithContext=function(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];if(this._$handlers){var i=this._$handlers[t],r=this._$eventProcessor;if(i)for(var o=e.length,a=e[o-1],s=i.length,l=0;l<s;l++){var u=i[l];if(!r||!r.filter||null==u.query||r.filter(t,u.query))switch(o){case 0:u.h.call(a);break;case 1:u.h.call(a,e[0]);break;case 2:u.h.call(a,e[0],e[1]);break;default:u.h.apply(a,e.slice(1,o-1))}}r&&r.afterTrigger&&r.afterTrigger(t)}return this};var ae=se;function se(t){t&&(this._$eventProcessor=t)}var le=Math.log(2);function ue(t,e,n,i,r,o){var a,s=i+"-"+r,l=t.length;if(o.hasOwnProperty(s))return o[s];if(1===e)return a=Math.round(Math.log((1<<l)-1&~r)/le),t[n][a];for(var u=i|1<<n,h=n+1;i&1<<h;)h++;for(var c=0,p=0,d=0;p<l;p++){var f=1<<p;f&r||(c+=(d%2?-1:1)*t[n][p]*ue(t,e-1,h,u,r|f,o),d++)}return o[s]=c}function he(t,e){var n=[[t[0],t[1],1,0,0,0,-e[0]*t[0],-e[0]*t[1]],[0,0,0,t[0],t[1],1,-e[1]*t[0],-e[1]*t[1]],[t[2],t[3],1,0,0,0,-e[2]*t[2],-e[2]*t[3]],[0,0,0,t[2],t[3],1,-e[3]*t[2],-e[3]*t[3]],[t[4],t[5],1,0,0,0,-e[4]*t[4],-e[4]*t[5]],[0,0,0,t[4],t[5],1,-e[5]*t[4],-e[5]*t[5]],[t[6],t[7],1,0,0,0,-e[6]*t[6],-e[6]*t[7]],[0,0,0,t[6],t[7],1,-e[7]*t[6],-e[7]*t[7]]],i={},r=ue(n,8,0,0,0,i);if(0!==r){for(var o=[],a=0;a<8;a++)for(var s=0;s<8;s++)null==o[s]&&(o[s]=0),o[s]+=((a+s)%2?-1:1)*ue(n,7,0===a?1:0,1<<a,1<<s,i)/r*e[a];return function(t,e,n){var i=e*o[6]+n*o[7]+1;t[0]=(e*o[0]+n*o[1]+o[2])/i,t[1]=(e*o[3]+n*o[4]+o[5])/i}}}var ce="___zrEVENTSAVED",pe=[];function de(t,e,n,i,r){if(e.getBoundingClientRect&&b.domSupported&&!fe(e)){var o=e[ce]||(e[ce]={}),e=function(t,e,n){for(var i=n?"invTrans":"trans",r=e[i],o=e.srcCoords,a=[],s=[],l=!0,u=0;u<4;u++){var h=t[u].getBoundingClientRect(),c=2*u,p=h.left,h=h.top;a.push(p,h),l=l&&o&&p===o[c]&&h===o[1+c],s.push(t[u].offsetLeft,t[u].offsetTop)}return l&&r?r:(e.srcCoords=a,e[i]=n?he(s,a):he(a,s))}(function(t,e){var n=e.markers;if(!n){n=e.markers=[];for(var i=["left","right"],r=["top","bottom"],o=0;o<4;o++){var a=document.createElement("div"),s=a.style,l=o%2,u=(o>>1)%2;s.cssText=["position: absolute","visibility: hidden","padding: 0","margin: 0","border-width: 0","user-select: none","width:0","height:0",i[l]+":0",r[u]+":0",i[1-l]+":auto",r[1-u]+":auto",""].join("!important;"),t.appendChild(a),n.push(a)}}return n}(e,o),o,r);if(e)return e(t,n,i),!0}return!1}function fe(t){return"CANVAS"===t.nodeName.toUpperCase()}var ge=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,ye=[],me=b.browser.firefox&&+b.browser.version.split(".")[0]<39;function ve(t,e,n,i){return n=n||{},i?_e(t,e,n):me&&null!=e.layerX&&e.layerX!==e.offsetX?(n.zrX=e.layerX,n.zrY=e.layerY):null!=e.offsetX?(n.zrX=e.offsetX,n.zrY=e.offsetY):_e(t,e,n),n}function _e(t,e,n){if(b.domSupported&&t.getBoundingClientRect){var i,r=e.clientX,e=e.clientY;if(fe(t))return i=t.getBoundingClientRect(),n.zrX=r-i.left,void(n.zrY=e-i.top);if(de(ye,t,r,e))return n.zrX=ye[0],void(n.zrY=ye[1])}n.zrX=n.zrY=0}function xe(t){return t||window.event}function we(t,e,n){var i;return null==(e=xe(e)).zrX&&((i=e.type)&&0<=i.indexOf("touch")?(i=("touchend"!==i?e.targetTouches:e.changedTouches)[0])&&ve(t,i,e,n):(ve(t,e,e,n),i=function(t){var e=t.wheelDelta;if(e)return e;var n=t.deltaX,t=t.deltaY;return null!=n&&null!=t?3*(0!==t?Math.abs(t):Math.abs(n))*(0<t||!(t<0)&&0<n?-1:1):e}(e),e.zrDelta=i?i/120:-(e.detail||0)/3),t=e.button,null==e.which&&void 0!==t&&ge.test(e.type))&&(e.which=1&t?1:2&t?3:4&t?2:0),e}var be=function(t){t.preventDefault(),t.stopPropagation(),t.cancelBubble=!0},Se=(Me.prototype.recognize=function(t,e,n){return this._doTrack(t,e,n),this._recognize(t)},Me.prototype.clear=function(){return this._track.length=0,this},Me.prototype._doTrack=function(t,e,n){var i=t.touches;if(i){for(var r={points:[],touches:[],target:e,event:t},o=0,a=i.length;o<a;o++){var s=i[o],l=ve(n,s,{});r.points.push([l.zrX,l.zrY]),r.touches.push(s)}this._track.push(r)}},Me.prototype._recognize=function(t){for(var e in Ce)if(Ce.hasOwnProperty(e)){e=Ce[e](this._track,t);if(e)return e}},Me);function Me(){this._track=[]}function Te(t){var e=t[1][0]-t[0][0],t=t[1][1]-t[0][1];return Math.sqrt(e*e+t*t)}var Ce={pinch:function(t,e){var n,i=t.length;if(i)return n=(t[i-1]||{}).points,(i=(t[i-2]||{}).points||n)&&1<i.length&&n&&1<n.length?(i=Te(n)/Te(i),isFinite(i)||(i=1),e.pinchScale=i,i=[(n[0][0]+n[1][0])/2,(n[0][1]+n[1][1])/2],e.pinchX=i[0],e.pinchY=i[1],{type:"pinch",target:t[0].target,event:e}):void 0}};function Ie(){be(this.event)}u(Ae,ke=ae),Ae.prototype.dispose=function(){},Ae.prototype.setCursor=function(){};var ke,De=Ae;function Ae(){var t=null!==ke&&ke.apply(this,arguments)||this;return t.handler=null,t}var Pe,Le=function(t,e){this.x=t,this.y=e},Oe=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],Re=(u(Ne,Pe=ae),Ne.prototype.setHandlerProxy=function(e){this.proxy&&this.proxy.dispose(),e&&(O(Oe,function(t){e.on&&e.on(t,this[t],this)},this),e.handler=this),this.proxy=e},Ne.prototype.mousemove=function(t){var e=t.zrX,n=t.zrY,i=ze(this,e,n),r=this._hovered,o=r.target,i=(o&&!o.__zr&&(o=(r=this.findHover(r.x,r.y)).target),this._hovered=i?new Le(e,n):this.findHover(e,n)),e=i.target,n=this.proxy;n.setCursor&&n.setCursor(e?e.cursor:"default"),o&&e!==o&&this.dispatchToElement(r,"mouseout",t),this.dispatchToElement(i,"mousemove",t),e&&e!==o&&this.dispatchToElement(i,"mouseover",t)},Ne.prototype.mouseout=function(t){var e=t.zrEventControl;"only_globalout"!==e&&this.dispatchToElement(this._hovered,"mouseout",t),"no_globalout"!==e&&this.trigger("globalout",{type:"globalout",event:t})},Ne.prototype.resize=function(){this._hovered=new Le(0,0)},Ne.prototype.dispatch=function(t,e){t=this[t];t&&t.call(this,e)},Ne.prototype.dispose=function(){this.proxy.dispose(),this.storage=null,this.proxy=null,this.painter=null},Ne.prototype.setCursorStyle=function(t){var e=this.proxy;e.setCursor&&e.setCursor(t)},Ne.prototype.dispatchToElement=function(t,e,n){var i=(t=t||{}).target;if(!i||!i.silent){for(var r="on"+e,o={type:e,event:n,target:(t=t).target,topTarget:t.topTarget,cancelBubble:!1,offsetX:n.zrX,offsetY:n.zrY,gestureEvent:n.gestureEvent,pinchX:n.pinchX,pinchY:n.pinchY,pinchScale:n.pinchScale,wheelDelta:n.zrDelta,zrByTouch:n.zrByTouch,which:n.which,stop:Ie};i&&(i[r]&&(o.cancelBubble=!!i[r].call(i,o)),i.trigger(e,o),i=i.__hostTarget||i.parent,!o.cancelBubble););o.cancelBubble||(this.trigger(e,o),this.painter&&this.painter.eachOtherLayer&&this.painter.eachOtherLayer(function(t){"function"==typeof t[r]&&t[r].call(t,o),t.trigger&&t.trigger(e,o)}))}},Ne.prototype.findHover=function(t,e,n){for(var i=this.storage.getDisplayList(),r=new Le(t,e),o=i.length-1;0<=o;o--){var a=void 0;if(i[o]!==n&&!i[o].ignore&&(a=function(t,e,n){if(t[t.rectHover?"rectContain":"contain"](e,n)){for(var i=t,r=void 0,o=!1;i;){if(!(o=i.ignoreClip?!0:o)){var a=i.getClipPath();if(a&&!a.contain(e,n))return!1;i.silent&&(r=!0)}a=i.__hostTarget;i=a||i.parent}return!r||"silent"}return!1}(i[o],t,e))&&(r.topTarget||(r.topTarget=i[o]),"silent"!==a)){r.target=i[o];break}}return r},Ne.prototype.processGesture=function(t,e){this._gestureMgr||(this._gestureMgr=new Se);var n=this._gestureMgr,i=("start"===e&&n.clear(),n.recognize(t,this.findHover(t.zrX,t.zrY,null).target,this.proxy.dom));"end"===e&&n.clear(),i&&(e=i.type,t.gestureEvent=e,(n=new Le).target=i.target,this.dispatchToElement(n,e,i.event))},Ne);function Ne(t,e,n,i){var r=Pe.call(this)||this;return r._hovered=new Le(0,0),r.storage=t,r.painter=e,r.painterRoot=i,n=n||new De,r.proxy=null,r.setHandlerProxy(n),r._draggingMgr=new re(r),r}function ze(t,e,n){t=t.painter;return e<0||e>t.getWidth()||n<0||n>t.getHeight()}O(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],function(a){Re.prototype[a]=function(t){var e,n,i=t.zrX,r=t.zrY,o=ze(this,i,r);if("mouseup"===a&&o||(n=(e=this.findHover(i,r)).target),"mousedown"===a)this._downEl=n,this._downPoint=[t.zrX,t.zrY],this._upEl=n;else if("mouseup"===a)this._upEl=n;else if("click"===a){if(this._downEl!==this._upEl||!this._downPoint||4<jt(this._downPoint,[t.zrX,t.zrY]))return;this._downPoint=null}this.dispatchToElement(e,a,t)}});var Ee=32,Be=7;function Fe(t,e,n,i){var r=e+1;if(r===n)return 1;if(i(t[r++],t[e])<0){for(;r<n&&i(t[r],t[r-1])<0;)r++;var o=t,a=e,s=r;for(s--;a<s;){var l=o[a];o[a++]=o[s],o[s--]=l}}else for(;r<n&&0<=i(t[r],t[r-1]);)r++;return r-e}function Ve(t,e,n,i,r){for(i===e&&i++;i<n;i++){for(var o,a=t[i],s=e,l=i;s<l;)r(a,t[o=s+l>>>1])<0?l=o:s=1+o;var u=i-s;switch(u){case 3:t[s+3]=t[s+2];case 2:t[s+2]=t[s+1];case 1:t[s+1]=t[s];break;default:for(;0<u;)t[s+u]=t[s+u-1],u--}t[s]=a}}function He(t,e,n,i,r,o){var a=0,s=0,l=1;if(0<o(t,e[n+r])){for(s=i-r;l<s&&0<o(t,e[n+r+l]);)(l=1+((a=l)<<1))<=0&&(l=s);s<l&&(l=s),a+=r,l+=r}else{for(s=r+1;l<s&&o(t,e[n+r-l])<=0;)(l=1+((a=l)<<1))<=0&&(l=s);i=a,a=r-(l=s<l?s:l),l=r-i}for(a++;a<l;){var u=a+(l-a>>>1);0<o(t,e[n+u])?a=u+1:l=u}return l}function Ge(t,e,n,i,r,o){var a=0,s=0,l=1;if(o(t,e[n+r])<0){for(s=r+1;l<s&&o(t,e[n+r-l])<0;)(l=1+((a=l)<<1))<=0&&(l=s);var u=a,a=r-(l=s<l?s:l),l=r-u}else{for(s=i-r;l<s&&0<=o(t,e[n+r+l]);)(l=1+((a=l)<<1))<=0&&(l=s);s<l&&(l=s),a+=r,l+=r}for(a++;a<l;){var h=a+(l-a>>>1);o(t,e[n+h])<0?l=h:a=h+1}return l}function We(A,P){var L,O,R=Be,N=0,z=(A.length,[]);function e(t){var e=L[t],n=O[t],i=L[t+1],r=O[t+1],t=(O[t]=n+r,t===N-3&&(L[t+1]=L[t+2],O[t+1]=O[t+2]),N--,Ge(A[i],A,e,n,0,P));if(e+=t,0!=(n-=t)&&0!==(r=He(A[e+n-1],A,i,r,r-1,P)))if(n<=r){var o=e,a=n,t=i,s=r,l=0;for(l=0;l<a;l++)z[l]=A[o+l];var u=0,h=t,c=o;if(A[c++]=A[h++],0==--s)for(l=0;l<a;l++)A[c+l]=z[u+l];else if(1===a){for(l=0;l<s;l++)A[c+l]=A[h+l];A[c+s]=z[u]}else{for(var p,d,f,g=R;;){d=p=0,f=!1;do{if(P(A[h],z[u])<0){if(A[c++]=A[h++],d++,(p=0)==--s){f=!0;break}}else if(A[c++]=z[u++],p++,d=0,1==--a){f=!0;break}}while((p|d)<g);if(f)break;do{if(0!==(p=Ge(A[h],z,u,a,0,P))){for(l=0;l<p;l++)A[c+l]=z[u+l];if(c+=p,u+=p,(a-=p)<=1){f=!0;break}}if(A[c++]=A[h++],0==--s){f=!0;break}if(0!==(d=He(z[u],A,h,s,0,P))){for(l=0;l<d;l++)A[c+l]=A[h+l];if(c+=d,h+=d,0===(s-=d)){f=!0;break}}if(A[c++]=z[u++],1==--a){f=!0;break}}while(g--,Be<=p||Be<=d);if(f)break;g<0&&(g=0),g+=2}if((R=g)<1&&(R=1),1===a){for(l=0;l<s;l++)A[c+l]=A[h+l];A[c+s]=z[u]}else{if(0===a)throw new Error;for(l=0;l<a;l++)A[c+l]=z[u+l]}}}else{var y=e,m=n,v=i,_=r,x=0;for(x=0;x<_;x++)z[x]=A[v+x];var w=y+m-1,b=_-1,S=v+_-1,M=0,T=0;if(A[S--]=A[w--],0==--m)for(M=S-(_-1),x=0;x<_;x++)A[M+x]=z[x];else if(1===_){for(T=(S-=m)+1,M=(w-=m)+1,x=m-1;0<=x;x--)A[T+x]=A[M+x];A[S]=z[b]}else{for(var C=R;;){var I=0,k=0,D=!1;do{if(P(z[b],A[w])<0){if(A[S--]=A[w--],I++,(k=0)==--m){D=!0;break}}else if(A[S--]=z[b--],k++,I=0,1==--_){D=!0;break}}while((I|k)<C);if(D)break;do{if(0!==(I=m-Ge(z[b],A,y,m,m-1,P))){for(m-=I,T=(S-=I)+1,M=(w-=I)+1,x=I-1;0<=x;x--)A[T+x]=A[M+x];if(0===m){D=!0;break}}if(A[S--]=z[b--],1==--_){D=!0;break}if(0!==(k=_-He(A[w],z,0,_,_-1,P))){for(_-=k,T=(S-=k)+1,M=(b-=k)+1,x=0;x<k;x++)A[T+x]=z[M+x];if(_<=1){D=!0;break}}if(A[S--]=A[w--],0==--m){D=!0;break}}while(C--,Be<=I||Be<=k);if(D)break;C<0&&(C=0),C+=2}if((R=C)<1&&(R=1),1===_){for(T=(S-=m)+1,M=(w-=m)+1,x=m-1;0<=x;x--)A[T+x]=A[M+x];A[S]=z[b]}else{if(0===_)throw new Error;for(M=S-(_-1),x=0;x<_;x++)A[M+x]=z[x]}}}}return L=[],O=[],{mergeRuns:function(){for(;1<N;){var t=N-2;if(1<=t&&O[t-1]<=O[t]+O[t+1]||2<=t&&O[t-2]<=O[t]+O[t-1])O[t-1]<O[t+1]&&t--;else if(O[t]>O[t+1])break;e(t)}},forceMergeRuns:function(){for(;1<N;){var t=N-2;0<t&&O[t-1]<O[t+1]&&t--,e(t)}},pushRun:function(t,e){L[N]=t,O[N]=e,N+=1}}}function Ue(t,e,n,i){var r=(i=i||t.length)-(n=n||0);if(!(r<2)){var o=0;if(r<Ee)Ve(t,n,i,n+(o=Fe(t,n,i,e)),e);else{var a,s=We(t,e),l=function(t){for(var e=0;Ee<=t;)e|=1&t,t>>=1;return t+e}(r);do{}while((o=Fe(t,n,i,e))<l&&(Ve(t,n,n+(a=l<(a=r)?l:r),n+o,e),o=a),s.pushRun(n,o),s.mergeRuns(),n+=o,0!==(r-=o));s.forceMergeRuns()}}}var Xe=1,Ye=4,qe=!1;function Ze(){qe||(qe=!0,console.warn("z / z2 / zlevel of displayable is invalid, which may cause unexpected errors"))}function je(t,e){return t.zlevel===e.zlevel?t.z===e.z?t.z2-e.z2:t.z-e.z:t.zlevel-e.zlevel}$e.prototype.traverse=function(t,e){for(var n=0;n<this._roots.length;n++)this._roots[n].traverse(t,e)},$e.prototype.getDisplayList=function(t,e){e=e||!1;var n=this._displayList;return!t&&n.length||this.updateDisplayList(e),n},$e.prototype.updateDisplayList=function(t){this._displayListLen=0;for(var e=this._roots,n=this._displayList,i=0,r=e.length;i<r;i++)this._updateAndAddDisplayable(e[i],null,t);n.length=this._displayListLen,Ue(n,je)},$e.prototype._updateAndAddDisplayable=function(t,e,n){if(!t.ignore||n){t.beforeUpdate(),t.update(),t.afterUpdate();var i=t.getClipPath();if(t.ignoreClip)e=null;else if(i){e=e?e.slice():[];for(var r=i,o=t;r;)r.parent=o,r.updateTransform(),e.push(r),r=(o=r).getClipPath()}if(t.childrenRef){for(var a=t.childrenRef(),s=0;s<a.length;s++){var l=a[s];t.__dirty&&(l.__dirty|=Xe),this._updateAndAddDisplayable(l,e,n)}t.__dirty=0}else{i=t;e&&e.length?i.__clipPaths=e:i.__clipPaths&&0<i.__clipPaths.length&&(i.__clipPaths=[]),isNaN(i.z)&&(Ze(),i.z=0),isNaN(i.z2)&&(Ze(),i.z2=0),isNaN(i.zlevel)&&(Ze(),i.zlevel=0),this._displayList[this._displayListLen++]=i}i=t.getDecalElement&&t.getDecalElement(),i=(i&&this._updateAndAddDisplayable(i,e,n),t.getTextGuideLine()),i=(i&&this._updateAndAddDisplayable(i,e,n),t.getTextContent());i&&this._updateAndAddDisplayable(i,e,n)}},$e.prototype.addRoot=function(t){t.__zr&&t.__zr.storage===this||this._roots.push(t)},$e.prototype.delRoot=function(t){if(t instanceof Array)for(var e=0,n=t.length;e<n;e++)this.delRoot(t[e]);else{var i=I(this._roots,t);0<=i&&this._roots.splice(i,1)}},$e.prototype.delAllRoots=function(){this._roots=[],this._displayList=[],this._displayListLen=0},$e.prototype.getRoots=function(){return this._roots},$e.prototype.dispose=function(){this._displayList=null,this._roots=null};var Ke=$e;function $e(){this._roots=[],this._displayList=[],this._displayListLen=0,this.displayableSortFunc=je}var Qe=b.hasGlobalWindow&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(t){return setTimeout(t,16)},Je={linear:function(t){return t},quadraticIn:function(t){return t*t},quadraticOut:function(t){return t*(2-t)},quadraticInOut:function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)},cubicIn:function(t){return t*t*t},cubicOut:function(t){return--t*t*t+1},cubicInOut:function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)},quarticIn:function(t){return t*t*t*t},quarticOut:function(t){return 1- --t*t*t*t},quarticInOut:function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)},quinticIn:function(t){return t*t*t*t*t},quinticOut:function(t){return--t*t*t*t*t+1},quinticInOut:function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)},sinusoidalIn:function(t){return 1-Math.cos(t*Math.PI/2)},sinusoidalOut:function(t){return Math.sin(t*Math.PI/2)},sinusoidalInOut:function(t){return.5*(1-Math.cos(Math.PI*t))},exponentialIn:function(t){return 0===t?0:Math.pow(1024,t-1)},exponentialOut:function(t){return 1===t?1:1-Math.pow(2,-10*t)},exponentialInOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?.5*Math.pow(1024,t-1):.5*(2-Math.pow(2,-10*(t-1)))},circularIn:function(t){return 1-Math.sqrt(1-t*t)},circularOut:function(t){return Math.sqrt(1- --t*t)},circularInOut:function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)},elasticIn:function(t){var e,n=.1;return 0===t?0:1===t?1:(e=!n||n<1?(n=1,.1):.4*Math.asin(1/n)/(2*Math.PI),-(n*Math.pow(2,10*--t)*Math.sin((t-e)*(2*Math.PI)/.4)))},elasticOut:function(t){var e,n=.1;return 0===t?0:1===t?1:(e=!n||n<1?(n=1,.1):.4*Math.asin(1/n)/(2*Math.PI),n*Math.pow(2,-10*t)*Math.sin((t-e)*(2*Math.PI)/.4)+1)},elasticInOut:function(t){var e,n=.1;return 0===t?0:1===t?1:(e=!n||n<1?(n=1,.1):.4*Math.asin(1/n)/(2*Math.PI),(t*=2)<1?n*Math.pow(2,10*--t)*Math.sin((t-e)*(2*Math.PI)/.4)*-.5:n*Math.pow(2,-10*--t)*Math.sin((t-e)*(2*Math.PI)/.4)*.5+1)},backIn:function(t){return t*t*(2.70158*t-1.70158)},backOut:function(t){return--t*t*(2.70158*t+1.70158)+1},backInOut:function(t){var e=2.5949095;return(t*=2)<1?t*t*((1+e)*t-e)*.5:.5*((t-=2)*t*((1+e)*t+e)+2)},bounceIn:function(t){return 1-Je.bounceOut(1-t)},bounceOut:function(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},bounceInOut:function(t){return t<.5?.5*Je.bounceIn(2*t):.5*Je.bounceOut(2*t-1)+.5}},tn=Math.pow,en=Math.sqrt,nn=1e-8,rn=en(3),on=1/3,an=Vt(),sn=Vt(),ln=Vt();function un(t){return-nn<t&&t<nn}function hn(t){return nn<t||t<-nn}function cn(t,e,n,i,r){var o=1-r;return o*o*(o*t+3*r*e)+r*r*(r*i+3*o*n)}function pn(t,e,n,i,r){var o=1-r;return 3*(((e-t)*o+2*(n-e)*r)*o+(i-n)*r*r)}function dn(t,e,n,i,r,o){var a,s,i=i+3*(e-n)-t,n=3*(n-2*e+t),e=3*(e-t),t=t-r,r=n*n-3*i*e,l=n*e-9*i*t,t=e*e-3*n*t,u=0;return un(r)&&un(l)?un(n)?o[0]=0:0<=(a=-e/n)&&a<=1&&(o[u++]=a):un(e=l*l-4*r*t)?(s=-(t=l/r)/2,0<=(a=-n/i+t)&&a<=1&&(o[u++]=a),0<=s&&s<=1&&(o[u++]=s)):0<e?(e=r*n+1.5*i*(-l-(t=en(e))),0<=(a=(-n-((t=(t=r*n+1.5*i*(-l+t))<0?-tn(-t,on):tn(t,on))+(e=e<0?-tn(-e,on):tn(e,on))))/(3*i))&&a<=1&&(o[u++]=a)):(t=(2*r*n-3*i*l)/(2*en(r*r*r)),e=Math.acos(t)/3,a=(-n-2*(l=en(r))*(t=Math.cos(e)))/(3*i),s=(-n+l*(t+rn*Math.sin(e)))/(3*i),r=(-n+l*(t-rn*Math.sin(e)))/(3*i),0<=a&&a<=1&&(o[u++]=a),0<=s&&s<=1&&(o[u++]=s),0<=r&&r<=1&&(o[u++]=r)),u}function fn(t,e,n,i,r){var o,a=6*n-12*e+6*t,i=9*e+3*i-3*t-9*n,n=3*e-3*t,e=0;return un(i)?hn(a)&&0<=(o=-n/a)&&o<=1&&(r[e++]=o):un(t=a*a-4*i*n)?r[0]=-a/(2*i):0<t&&(t=(-a-(n=en(t)))/(2*i),0<=(o=(-a+n)/(2*i))&&o<=1&&(r[e++]=o),0<=t)&&t<=1&&(r[e++]=t),e}function gn(t,e,n,i,r,o){var a=(e-t)*r+t,e=(n-e)*r+e,n=(i-n)*r+n,s=(e-a)*r+a,e=(n-e)*r+e,r=(e-s)*r+s;o[0]=t,o[1]=a,o[2]=s,o[3]=r,o[4]=r,o[5]=e,o[6]=n,o[7]=i}function yn(t,e,n,i,r,o,a,s,l,u,h){var c,p,d,f,g=.005,y=1/0;an[0]=l,an[1]=u;for(var m=0;m<1;m+=.05)sn[0]=cn(t,n,r,a,m),sn[1]=cn(e,i,o,s,m),(d=$t(an,sn))<y&&(c=m,y=d);for(var y=1/0,v=0;v<32&&!(g<1e-4);v++)p=c+g,sn[0]=cn(t,n,r,a,f=c-g),sn[1]=cn(e,i,o,s,f),d=$t(sn,an),0<=f&&d<y?(c=f,y=d):(ln[0]=cn(t,n,r,a,p),ln[1]=cn(e,i,o,s,p),f=$t(ln,an),p<=1&&f<y?(c=p,y=f):g*=.5);return h&&(h[0]=cn(t,n,r,a,c),h[1]=cn(e,i,o,s,c)),en(y)}function mn(t,e,n,i){var r=1-i;return r*(r*t+2*i*e)+i*i*n}function vn(t,e,n,i){return 2*((1-i)*(e-t)+i*(n-e))}function _n(t,e,n){n=t+n-2*e;return 0==n?.5:(t-e)/n}function xn(t,e,n,i,r){var o=(e-t)*i+t,e=(n-e)*i+e,i=(e-o)*i+o;r[0]=t,r[1]=o,r[2]=i,r[3]=i,r[4]=e,r[5]=n}function wn(t,e,n,i,r,o,a,s,l){var u,h=.005,c=1/0;an[0]=a,an[1]=s;for(var p=0;p<1;p+=.05)sn[0]=mn(t,n,r,p),sn[1]=mn(e,i,o,p),(y=$t(an,sn))<c&&(u=p,c=y);for(var c=1/0,d=0;d<32&&!(h<1e-4);d++){var f=u-h,g=u+h,y=(sn[0]=mn(t,n,r,f),sn[1]=mn(e,i,o,f),$t(sn,an));0<=f&&y<c?(u=f,c=y):(ln[0]=mn(t,n,r,g),ln[1]=mn(e,i,o,g),f=$t(ln,an),g<=1&&f<c?(u=g,c=f):h*=.5)}return l&&(l[0]=mn(t,n,r,u),l[1]=mn(e,i,o,u)),en(c)}var bn=/cubic-bezier\(([0-9,\.e ]+)\)/;function Sn(t){t=t&&bn.exec(t);if(t){var e,t=t[1].split(","),n=+It(t[0]),i=+It(t[1]),r=+It(t[2]),o=+It(t[3]);if(!isNaN(n+i+r+o))return e=[],function(t){return t<=0?0:1<=t?1:dn(0,n,r,1,t,e)&&cn(0,i,o,1,e[0])}}}Tn.prototype.step=function(t,e){if(this._inited||(this._startTime=t+this._delay,this._inited=!0),!this._paused){var n=this._life,i=t-this._startTime-this._pausedTime,r=i/n,o=(r<0&&(r=0),r=Math.min(r,1),this.easingFunc),o=o?o(r):r;if(this.onframe(o),1===r){if(!this.loop)return!0;this._startTime=t-i%n,this._pausedTime=0,this.onrestart()}return!1}this._pausedTime+=e},Tn.prototype.pause=function(){this._paused=!0},Tn.prototype.resume=function(){this._paused=!1},Tn.prototype.setEasing=function(t){this.easing=t,this.easingFunc=S(t)?t:Je[t]||Sn(t)};var Mn=Tn;function Tn(t){this._inited=!1,this._startTime=0,this._pausedTime=0,this._paused=!1,this._life=t.life||1e3,this._delay=t.delay||0,this.loop=t.loop||!1,this.onframe=t.onframe||Et,this.ondestroy=t.ondestroy||Et,this.onrestart=t.onrestart||Et,t.easing&&this.setEasing(t.easing)}var Cn=function(t){this.value=t},In=(kn.prototype.insert=function(t){t=new Cn(t);return this.insertEntry(t),t},kn.prototype.insertEntry=function(t){this.head?((this.tail.next=t).prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},kn.prototype.remove=function(t){var e=t.prev,n=t.next;e?e.next=n:this.head=n,n?n.prev=e:this.tail=e,t.next=t.prev=null,this._len--},kn.prototype.len=function(){return this._len},kn.prototype.clear=function(){this.head=this.tail=null,this._len=0},kn);function kn(){this._len=0}An.prototype.put=function(t,e){var n,i,r=this._list,o=this._map,a=null;return null==o[t]&&(i=r.len(),n=this._lastRemovedEntry,i>=this._maxSize&&0<i&&(i=r.head,r.remove(i),delete o[i.key],a=i.value,this._lastRemovedEntry=i),n?n.value=e:n=new Cn(e),n.key=t,r.insertEntry(n),o[t]=n),a},An.prototype.get=function(t){var t=this._map[t],e=this._list;if(null!=t)return t!==e.tail&&(e.remove(t),e.insertEntry(t)),t.value},An.prototype.clear=function(){this._list.clear(),this._map={}},An.prototype.len=function(){return this._list.len()};var Dn=An;function An(t){this._list=new In,this._maxSize=10,this._map={},this._maxSize=t}var Pn={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]};function Ln(t){return(t=Math.round(t))<0?0:255<t?255:t}function On(t){return t<0?0:1<t?1:t}function Rn(t){return t.length&&"%"===t.charAt(t.length-1)?Ln(parseFloat(t)/100*255):Ln(parseInt(t,10))}function Nn(t){return t.length&&"%"===t.charAt(t.length-1)?On(parseFloat(t)/100):On(parseFloat(t))}function zn(t,e,n){return n<0?n+=1:1<n&&--n,6*n<1?t+(e-t)*n*6:2*n<1?e:3*n<2?t+(e-t)*(2/3-n)*6:t}function En(t,e,n){return t+(e-t)*n}function Bn(t,e,n,i,r){return t[0]=e,t[1]=n,t[2]=i,t[3]=r,t}function Fn(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t}var Vn=new Dn(20),Hn=null;function Gn(t,e){Hn&&Fn(Hn,e),Hn=Vn.put(t,Hn||e.slice())}function Wn(t,e){if(t){e=e||[];var n=Vn.get(t);if(n)return Fn(e,n);n=(t+="").replace(/ /g,"").toLowerCase();if(n in Pn)return Fn(e,Pn[n]),Gn(t,e),e;var i=n.length;if("#"===n.charAt(0))return 4===i||5===i?0<=(r=parseInt(n.slice(1,4),16))&&r<=4095?(Bn(e,(3840&r)>>4|(3840&r)>>8,240&r|(240&r)>>4,15&r|(15&r)<<4,5===i?parseInt(n.slice(4),16)/15:1),Gn(t,e),e):void Bn(e,0,0,0,1):7===i||9===i?0<=(r=parseInt(n.slice(1,7),16))&&r<=16777215?(Bn(e,(16711680&r)>>16,(65280&r)>>8,255&r,9===i?parseInt(n.slice(7),16)/255:1),Gn(t,e),e):void Bn(e,0,0,0,1):void 0;var r=n.indexOf("("),o=n.indexOf(")");if(-1!==r&&o+1===i){var i=n.substr(0,r),a=n.substr(r+1,o-(r+1)).split(","),s=1;switch(i){case"rgba":if(4!==a.length)return 3===a.length?Bn(e,+a[0],+a[1],+a[2],1):Bn(e,0,0,0,1);s=Nn(a.pop());case"rgb":return 3!==a.length?void Bn(e,0,0,0,1):(Bn(e,Rn(a[0]),Rn(a[1]),Rn(a[2]),s),Gn(t,e),e);case"hsla":return 4!==a.length?void Bn(e,0,0,0,1):(a[3]=Nn(a[3]),Un(a,e),Gn(t,e),e);case"hsl":return 3!==a.length?void Bn(e,0,0,0,1):(Un(a,e),Gn(t,e),e);default:return}}Bn(e,0,0,0,1)}}function Un(t,e){var n=(parseFloat(t[0])%360+360)%360/360,i=Nn(t[1]),r=Nn(t[2]),i=r<=.5?r*(i+1):r+i-r*i,r=2*r-i;return Bn(e=e||[],Ln(255*zn(r,i,n+1/3)),Ln(255*zn(r,i,n)),Ln(255*zn(r,i,n-1/3)),1),4===t.length&&(e[3]=t[3]),e}function Xn(t,e){var n=Wn(t);if(n){for(var i=0;i<3;i++)n[i]=e<0?n[i]*(1-e)|0:(255-n[i])*e+n[i]|0,255<n[i]?n[i]=255:n[i]<0&&(n[i]=0);return Zn(n,4===n.length?"rgba":"rgb")}}function Yn(t,e,n){var i,r,o;if(e&&e.length&&0<=t&&t<=1)return n=n||[],t=t*(e.length-1),i=Math.floor(t),o=Math.ceil(t),r=e[i],e=e[o],n[0]=Ln(En(r[0],e[0],o=t-i)),n[1]=Ln(En(r[1],e[1],o)),n[2]=Ln(En(r[2],e[2],o)),n[3]=On(En(r[3],e[3],o)),n}function qn(t,e,n){var i,r,o,a;if(e&&e.length&&0<=t&&t<=1)return t=t*(e.length-1),i=Math.floor(t),r=Math.ceil(t),a=Wn(e[i]),e=Wn(e[r]),a=Zn([Ln(En(a[0],e[0],o=t-i)),Ln(En(a[1],e[1],o)),Ln(En(a[2],e[2],o)),On(En(a[3],e[3],o))],"rgba"),n?{color:a,leftIndex:i,rightIndex:r,value:t}:a}function Zn(t,e){var n;if(t&&t.length)return n=t[0]+","+t[1]+","+t[2],"rgba"!==e&&"hsva"!==e&&"hsla"!==e||(n+=","+t[3]),e+"("+n+")"}function jn(t,e){t=Wn(t);return t?(.299*t[0]+.587*t[1]+.114*t[2])*t[3]/255+(1-t[3])*e:0}var Kn=Object.freeze({__proto__:null,fastLerp:Yn,fastMapToColor:Yn,lerp:qn,lift:Xn,lum:jn,mapToColor:qn,modifyAlpha:function(t,e){if((t=Wn(t))&&null!=e)return t[3]=On(e),Zn(t,"rgba")},modifyHSL:function(t,e,n,i){var r=Wn(t);if(t)return r=function(t){var e,n,i,r,o,a,s,l,u,h;if(t)return h=t[0]/255,e=t[1]/255,n=t[2]/255,s=Math.min(h,e,n),r=((i=Math.max(h,e,n))+s)/2,0==(u=i-s)?a=o=0:(a=r<.5?u/(i+s):u/(2-i-s),s=((i-h)/6+u/2)/u,l=((i-e)/6+u/2)/u,u=((i-n)/6+u/2)/u,h===i?o=u-l:e===i?o=1/3+s-u:n===i&&(o=2/3+l-s),o<0&&(o+=1),1<o&&--o),h=[360*o,a,r],null!=t[3]&&h.push(t[3]),h}(r),null!=e&&(r[0]=(t=e,(t=Math.round(t))<0?0:360<t?360:t)),null!=n&&(r[1]=Nn(n)),null!=i&&(r[2]=Nn(i)),Zn(Un(r),"rgba")},parse:Wn,random:function(){return Zn([Math.round(255*Math.random()),Math.round(255*Math.random()),Math.round(255*Math.random())],"rgb")},stringify:Zn,toHex:function(t){if(t=Wn(t))return((1<<24)+(t[0]<<16)+(t[1]<<8)+ +t[2]).toString(16).slice(1)}});b.hasGlobalWindow&&S(window.btoa);var $n=Array.prototype.slice;function Qn(t,e,n){return(e-t)*n+t}function Jn(t,e,n,i){for(var r=e.length,o=0;o<r;o++)t[o]=Qn(e[o],n[o],i);return t}function ti(t,e,n,i){for(var r=e.length,o=0;o<r;o++)t[o]=e[o]+n[o]*i;return t}function ei(t,e,n,i){for(var r=e.length,o=r&&e[0].length,a=0;a<r;a++){t[a]||(t[a]=[]);for(var s=0;s<o;s++)t[a][s]=e[a][s]+n[a][s]*i}return t}function ni(t){if(st(t)){var e=t.length;if(st(t[0])){for(var n=[],i=0;i<e;i++)n.push($n.call(t[i]));return n}return $n.call(t)}return t}function ii(t){return t[0]=Math.floor(t[0])||0,t[1]=Math.floor(t[1])||0,t[2]=Math.floor(t[2])||0,t[3]=null==t[3]?1:t[3],"rgba("+t.join(",")+")"}function ri(t){return 4===t||5===t}function oi(t){return 1===t||2===t}var ai=[0,0,0,0],si=(li.prototype.isFinished=function(){return this._finished},li.prototype.setFinished=function(){this._finished=!0,this._additiveTrack&&this._additiveTrack.setFinished()},li.prototype.needsAnimate=function(){return 1<=this.keyframes.length},li.prototype.getAdditiveTrack=function(){return this._additiveTrack},li.prototype.addKeyframe=function(t,e,n){this._needsSort=!0;var i,r=this.keyframes,o=r.length,a=!1,s=6,l=e,u=(st(e)?(1==(s=i=st((i=e)&&i[0])?2:1)&&!ft(e[0])||2==i&&!ft(e[0][0]))&&(a=!0):ft(e)&&!wt(e)?s=0:V(e)?isNaN(+e)?(i=Wn(e))&&(l=i,s=3):s=0:vt(e)&&((u=L({},l)).colorStops=B(e.colorStops,function(t){return{offset:t.offset,color:Wn(t.color)}}),"linear"===e.type?s=4:"radial"===e.type&&(s=5),l=u),0===o?this.valType=s:s===this.valType&&6!==s||(a=!0),this.discrete=this.discrete||a,{time:t,value:l,rawValue:e,percent:0});return n&&(u.easing=n,u.easingFunc=S(n)?n:Je[n]||Sn(n)),r.push(u),u},li.prototype.prepare=function(t,e){for(var n=this.keyframes,i=(this._needsSort&&n.sort(function(t,e){return t.time-e.time}),this.valType),r=n.length,o=n[r-1],a=this.discrete,s=oi(i),l=ri(i),u=0;u<r;u++){var h=n[u],c=h.value,p=o.value;if(h.percent=h.time/t,!a)if(s&&u!==r-1){x=_=v=m=y=g=f=d=h=void 0;var d=p,f=i,g=h=c,y=d;if(g.push&&y.push){var h=g.length,m=y.length;if(h!==m)if(m<h)g.length=m;else for(var v=h;v<m;v++)g.push(1===f?y[v]:$n.call(y[v]));for(var _=g[0]&&g[0].length,v=0;v<g.length;v++)if(1===f)isNaN(g[v])&&(g[v]=y[v]);else for(var x=0;x<_;x++)isNaN(g[v][x])&&(g[v][x]=y[v][x])}}else if(l){T=M=S=b=w=h=d=void 0;for(var d=c.colorStops,h=p.colorStops,w=d.length,b=h.length,S=b<w?h:d,h=Math.min(w,b),M=S[h-1]||{color:[0,0,0,0],offset:0},T=h;T<Math.max(w,b);T++)S.push({offset:M.offset,color:M.color.slice()})}}if(!a&&5!==i&&e&&this.needsAnimate()&&e.needsAnimate()&&i===e.valType&&!e._finished){this._additiveTrack=e;for(var C=n[0].value,u=0;u<r;u++)0===i?n[u].additiveValue=n[u].value-C:3===i?n[u].additiveValue=ti([],n[u].value,C,-1):oi(i)&&(n[u].additiveValue=(1===i?ti:ei)([],n[u].value,C,-1))}},li.prototype.step=function(t,e){if(!this._finished){this._additiveTrack&&this._additiveTrack._finished&&(this._additiveTrack=null);var n,i,r,o,a=null!=this._additiveTrack,s=a?"additiveValue":"value",l=this.valType,u=this.keyframes,h=u.length,c=this.propName,p=3===l,d=this._lastFr,f=Math.min;if(1===h)n=i=u[0];else{if(e<0)g=0;else if(e<this._lastFrP){for(var g=f(d+1,h-1);0<=g&&!(u[g].percent<=e);g--);g=f(g,h-2)}else{for(g=d;g<h&&!(u[g].percent>e);g++);g=f(g-1,h-2)}i=u[g+1],n=u[g]}n&&i&&(this._lastFr=g,this._lastFrP=e,d=i.percent-n.percent,r=0==d?1:f((e-n.percent)/d,1),i.easingFunc&&(r=i.easingFunc(r)),f=a?this._additiveValue:p?ai:t[c],(oi(l)||p)&&(f=f||(this._additiveValue=[])),this.discrete?t[c]=(r<1?n:i).rawValue:oi(l)?(1===l?Jn:function(t,e,n,i){for(var r=e.length,o=r&&e[0].length,a=0;a<r;a++){t[a]||(t[a]=[]);for(var s=0;s<o;s++)t[a][s]=Qn(e[a][s],n[a][s],i)}})(f,n[s],i[s],r):ri(l)?(d=n[s],o=i[s],t[c]={type:(l=4===l)?"linear":"radial",x:Qn(d.x,o.x,r),y:Qn(d.y,o.y,r),colorStops:B(d.colorStops,function(t,e){e=o.colorStops[e];return{offset:Qn(t.offset,e.offset,r),color:ii(Jn([],t.color,e.color,r))}}),global:o.global},l?(t[c].x2=Qn(d.x2,o.x2,r),t[c].y2=Qn(d.y2,o.y2,r)):t[c].r=Qn(d.r,o.r,r)):p?(Jn(f,n[s],i[s],r),a||(t[c]=ii(f))):(l=Qn(n[s],i[s],r),a?this._additiveValue=l:t[c]=l),a)&&this._addToTarget(t)}},li.prototype._addToTarget=function(t){var e=this.valType,n=this.propName,i=this._additiveValue;0===e?t[n]=t[n]+i:3===e?(Wn(t[n],ai),ti(ai,ai,i,1),t[n]=ii(ai)):1===e?ti(t[n],t[n],i,1):2===e&&ei(t[n],t[n],i,1)},li);function li(t){this.keyframes=[],this.discrete=!1,this._invalid=!1,this._needsSort=!1,this._lastFr=0,this._lastFrP=0,this.propName=t}e.prototype.getMaxTime=function(){return this._maxTime},e.prototype.getDelay=function(){return this._delay},e.prototype.getLoop=function(){return this._loop},e.prototype.getTarget=function(){return this._target},e.prototype.changeTarget=function(t){this._target=t},e.prototype.when=function(t,e,n){return this.whenWithKeys(t,e,ht(e),n)},e.prototype.whenWithKeys=function(t,e,n,i){for(var r=this._tracks,o=0;o<n.length;o++){var a=n[o];if(!(l=r[a])){var s,l=r[a]=new si(a),u=void 0,h=this._getAdditiveTrack(a);if(h?(u=(s=(s=h.keyframes)[s.length-1])&&s.value,3===h.valType&&(u=u&&ii(u))):u=this._target[a],null==u)continue;0<t&&l.addKeyframe(0,ni(u),i),this._trackKeys.push(a)}l.addKeyframe(t,ni(e[a]),i)}return this._maxTime=Math.max(this._maxTime,t),this},e.prototype.pause=function(){this._clip.pause(),this._paused=!0},e.prototype.resume=function(){this._clip.resume(),this._paused=!1},e.prototype.isPaused=function(){return!!this._paused},e.prototype.duration=function(t){return this._maxTime=t,this._force=!0,this},e.prototype._doneCallback=function(){this._setTracksFinished(),this._clip=null;var t=this._doneCbs;if(t)for(var e=t.length,n=0;n<e;n++)t[n].call(this)},e.prototype._abortedCallback=function(){this._setTracksFinished();var t=this.animation,e=this._abortedCbs;if(t&&t.removeClip(this._clip),this._clip=null,e)for(var n=0;n<e.length;n++)e[n].call(this)},e.prototype._setTracksFinished=function(){for(var t=this._tracks,e=this._trackKeys,n=0;n<e.length;n++)t[e[n]].setFinished()},e.prototype._getAdditiveTrack=function(t){var e,n=this._additiveAnimators;if(n)for(var i=0;i<n.length;i++){var r=n[i].getTrack(t);r&&(e=r)}return e},e.prototype.start=function(t){if(!(0<this._started)){this._started=1;for(var e,o=this,a=[],n=this._maxTime||0,i=0;i<this._trackKeys.length;i++){var r=this._trackKeys[i],s=this._tracks[r],r=this._getAdditiveTrack(r),l=s.keyframes,u=l.length;s.prepare(n,r),s.needsAnimate()&&(!this._allowDiscrete&&s.discrete?((r=l[u-1])&&(o._target[s.propName]=r.rawValue),s.setFinished()):a.push(s))}return a.length||this._force?(e=new Mn({life:n,loop:this._loop,delay:this._delay||0,onframe:function(t){o._started=2;var e=o._additiveAnimators;if(e){for(var n=!1,i=0;i<e.length;i++)if(e[i]._clip){n=!0;break}n||(o._additiveAnimators=null)}for(i=0;i<a.length;i++)a[i].step(o._target,t);var r=o._onframeCbs;if(r)for(i=0;i<r.length;i++)r[i](o._target,t)},ondestroy:function(){o._doneCallback()}}),this._clip=e,this.animation&&this.animation.addClip(e),t&&e.setEasing(t)):this._doneCallback(),this}},e.prototype.stop=function(t){var e;this._clip&&(e=this._clip,t&&e.onframe(1),this._abortedCallback())},e.prototype.delay=function(t){return this._delay=t,this},e.prototype.during=function(t){return t&&(this._onframeCbs||(this._onframeCbs=[]),this._onframeCbs.push(t)),this},e.prototype.done=function(t){return t&&(this._doneCbs||(this._doneCbs=[]),this._doneCbs.push(t)),this},e.prototype.aborted=function(t){return t&&(this._abortedCbs||(this._abortedCbs=[]),this._abortedCbs.push(t)),this},e.prototype.getClip=function(){return this._clip},e.prototype.getTrack=function(t){return this._tracks[t]},e.prototype.getTracks=function(){var e=this;return B(this._trackKeys,function(t){return e._tracks[t]})},e.prototype.stopTracks=function(t,e){if(!t.length||!this._clip)return!0;for(var n=this._tracks,i=this._trackKeys,r=0;r<t.length;r++){var o=n[t[r]];o&&!o.isFinished()&&(e?o.step(this._target,1):1===this._started&&o.step(this._target,0),o.setFinished())}for(var a=!0,r=0;r<i.length;r++)if(!n[i[r]].isFinished()){a=!1;break}return a&&this._abortedCallback(),a},e.prototype.saveTo=function(t,e,n){if(t){e=e||this._trackKeys;for(var i=0;i<e.length;i++){var r=e[i],o=this._tracks[r];o&&!o.isFinished()&&(o=(o=o.keyframes)[n?0:o.length-1])&&(t[r]=ni(o.rawValue))}}},e.prototype.__changeFinalValue=function(t,e){e=e||ht(t);for(var n=0;n<e.length;n++){var i,r=e[n],o=this._tracks[r];o&&1<(i=o.keyframes).length&&(i=i.pop(),o.addKeyframe(i.time,t[r]),o.prepare(this._maxTime,o.getAdditiveTrack()))}};var ui=e;function e(t,e,n,i){this._tracks={},this._trackKeys=[],this._maxTime=0,this._started=0,this._clip=null,this._target=t,(this._loop=e)&&i?it("Can' use additive animation on looped animation."):(this._additiveAnimators=i,this._allowDiscrete=n)}function hi(){return(new Date).getTime()}u(di,ci=ae),di.prototype.addClip=function(t){t.animation&&this.removeClip(t),this._head?((this._tail.next=t).prev=this._tail,t.next=null,this._tail=t):this._head=this._tail=t,t.animation=this},di.prototype.addAnimator=function(t){t.animation=this;t=t.getClip();t&&this.addClip(t)},di.prototype.removeClip=function(t){var e,n;t.animation&&(e=t.prev,n=t.next,e?e.next=n:this._head=n,n?n.prev=e:this._tail=e,t.next=t.prev=t.animation=null)},di.prototype.removeAnimator=function(t){var e=t.getClip();e&&this.removeClip(e),t.animation=null},di.prototype.update=function(t){for(var e=hi()-this._pausedTime,n=e-this._time,i=this._head;i;)var r=i.next,i=(i.step(e,n)&&(i.ondestroy(),this.removeClip(i)),r);this._time=e,t||(this.trigger("frame",n),this.stage.update&&this.stage.update())},di.prototype._startLoop=function(){var e=this;this._running=!0,Qe(function t(){e._running&&(Qe(t),e._paused||e.update())})},di.prototype.start=function(){this._running||(this._time=hi(),this._pausedTime=0,this._startLoop())},di.prototype.stop=function(){this._running=!1},di.prototype.pause=function(){this._paused||(this._pauseStart=hi(),this._paused=!0)},di.prototype.resume=function(){this._paused&&(this._pausedTime+=hi()-this._pauseStart,this._paused=!1)},di.prototype.clear=function(){for(var t=this._head;t;){var e=t.next;t.prev=t.next=t.animation=null,t=e}this._head=this._tail=null},di.prototype.isFinished=function(){return null==this._head},di.prototype.animate=function(t,e){e=e||{},this.start();t=new ui(t,e.loop);return this.addAnimator(t),t};var ci,pi=di;function di(t){var e=ci.call(this)||this;return e._running=!1,e._time=0,e._pausedTime=0,e._pauseStart=0,e._paused=!1,e.stage=(t=t||{}).stage||{},e}var fi,gi=b.domSupported,yi=(fi={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},{mouse:Ri=["click","dblclick","mousewheel","wheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],touch:["touchstart","touchend","touchmove"],pointer:B(Ri,function(t){var e=t.replace("mouse","pointer");return fi.hasOwnProperty(e)?e:t})}),mi=["mousemove","mouseup"],vi=["pointermove","pointerup"],_i=!1;function xi(t){t=t.pointerType;return"pen"===t||"touch"===t}function wi(t){t&&(t.zrByTouch=!0)}function bi(t,e){for(var n=e,i=!1;n&&9!==n.nodeType&&!(i=n.domBelongToZr||n!==e&&n===t.painterRoot);)n=n.parentNode;return i}var Si=function(t,e){this.stopPropagation=Et,this.stopImmediatePropagation=Et,this.preventDefault=Et,this.type=e.type,this.target=this.currentTarget=t.dom,this.pointerType=e.pointerType,this.clientX=e.clientX,this.clientY=e.clientY},Mi={mousedown:function(t){t=we(this.dom,t),this.__mayPointerCapture=[t.zrX,t.zrY],this.trigger("mousedown",t)},mousemove:function(t){t=we(this.dom,t);var e=this.__mayPointerCapture;!e||t.zrX===e[0]&&t.zrY===e[1]||this.__togglePointerCapture(!0),this.trigger("mousemove",t)},mouseup:function(t){t=we(this.dom,t),this.__togglePointerCapture(!1),this.trigger("mouseup",t)},mouseout:function(t){bi(this,(t=we(this.dom,t)).toElement||t.relatedTarget)||(this.__pointerCapturing&&(t.zrEventControl="no_globalout"),this.trigger("mouseout",t))},wheel:function(t){_i=!0,t=we(this.dom,t),this.trigger("mousewheel",t)},mousewheel:function(t){_i||(t=we(this.dom,t),this.trigger("mousewheel",t))},touchstart:function(t){wi(t=we(this.dom,t)),this.__lastTouchMoment=new Date,this.handler.processGesture(t,"start"),Mi.mousemove.call(this,t),Mi.mousedown.call(this,t)},touchmove:function(t){wi(t=we(this.dom,t)),this.handler.processGesture(t,"change"),Mi.mousemove.call(this,t)},touchend:function(t){wi(t=we(this.dom,t)),this.handler.processGesture(t,"end"),Mi.mouseup.call(this,t),+new Date-+this.__lastTouchMoment<300&&Mi.click.call(this,t)},pointerdown:function(t){Mi.mousedown.call(this,t)},pointermove:function(t){xi(t)||Mi.mousemove.call(this,t)},pointerup:function(t){Mi.mouseup.call(this,t)},pointerout:function(t){xi(t)||Mi.mouseout.call(this,t)}},Ti=(O(["click","dblclick","contextmenu"],function(e){Mi[e]=function(t){t=we(this.dom,t),this.trigger(e,t)}}),{pointermove:function(t){xi(t)||Ti.mousemove.call(this,t)},pointerup:function(t){Ti.mouseup.call(this,t)},mousemove:function(t){this.trigger("mousemove",t)},mouseup:function(t){var e=this.__pointerCapturing;this.__togglePointerCapture(!1),this.trigger("mouseup",t),e&&(t.zrEventControl="only_globalout",this.trigger("mouseout",t))}});function Ci(i,r){var o=r.domHandlers;b.pointerEventsSupported?O(yi.pointer,function(e){ki(r,e,function(t){o[e].call(i,t)})}):(b.touchEventsSupported&&O(yi.touch,function(n){ki(r,n,function(t){var e;o[n].call(i,t),(e=r).touching=!0,null!=e.touchTimer&&(clearTimeout(e.touchTimer),e.touchTimer=null),e.touchTimer=setTimeout(function(){e.touching=!1,e.touchTimer=null},700)})}),O(yi.mouse,function(e){ki(r,e,function(t){t=xe(t),r.touching||o[e].call(i,t)})}))}function Ii(i,r){function t(n){ki(r,n,function(t){var e;t=xe(t),bi(i,t.target)||(e=t,t=we(i.dom,new Si(i,e),!0),r.domHandlers[n].call(i,t))},{capture:!0})}b.pointerEventsSupported?O(vi,t):b.touchEventsSupported||O(mi,t)}function ki(t,e,n,i){t.mounted[e]=n,t.listenerOpts[e]=i,t.domTarget.addEventListener(e,n,i)}function Di(t){var e,n,i,r,o,a=t.mounted;for(e in a)a.hasOwnProperty(e)&&(n=t.domTarget,r=a[i=e],o=t.listenerOpts[e],n.removeEventListener(i,r,o));t.mounted={}}var Ai,Pi=function(t,e){this.mounted={},this.listenerOpts={},this.touching=!1,this.domTarget=t,this.domHandlers=e},Li=(u(Oi,Ai=ae),Oi.prototype.dispose=function(){Di(this._localHandlerScope),gi&&Di(this._globalHandlerScope)},Oi.prototype.setCursor=function(t){this.dom.style&&(this.dom.style.cursor=t||"default")},Oi.prototype.__togglePointerCapture=function(t){var e;this.__mayPointerCapture=null,gi&&+this.__pointerCapturing^+t&&(this.__pointerCapturing=t,e=this._globalHandlerScope,t?Ii(this,e):Di(e))},Oi);function Oi(t,e){var n=Ai.call(this)||this;return n.__pointerCapturing=!1,n.dom=t,n.painterRoot=e,n._localHandlerScope=new Pi(t,Mi),gi&&(n._globalHandlerScope=new Pi(document,Ti)),Ci(n,n._localHandlerScope),n}var Ri=1,Ni=Ri=b.hasGlobalWindow?Math.max(window.devicePixelRatio||window.screen&&window.screen.deviceXDPI/window.screen.logicalXDPI||1,1):Ri,zi="#333",Ei="#ccc";function Bi(){return[1,0,0,1,0,0]}function Fi(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t}function Vi(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t}function Hi(t,e,n){var i=e[0]*n[0]+e[2]*n[1],r=e[1]*n[0]+e[3]*n[1],o=e[0]*n[2]+e[2]*n[3],a=e[1]*n[2]+e[3]*n[3],s=e[0]*n[4]+e[2]*n[5]+e[4],n=e[1]*n[4]+e[3]*n[5]+e[5];return t[0]=i,t[1]=r,t[2]=o,t[3]=a,t[4]=s,t[5]=n,t}function Gi(t,e,n){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4]+n[0],t[5]=e[5]+n[1],t}function Wi(t,e,n){var i=e[0],r=e[2],o=e[4],a=e[1],s=e[3],e=e[5],l=Math.sin(n),n=Math.cos(n);return t[0]=i*n+a*l,t[1]=-i*l+a*n,t[2]=r*n+s*l,t[3]=-r*l+n*s,t[4]=n*o+l*e,t[5]=n*e-l*o,t}function Ui(t,e,n){var i=n[0],n=n[1];return t[0]=e[0]*i,t[1]=e[1]*n,t[2]=e[2]*i,t[3]=e[3]*n,t[4]=e[4]*i,t[5]=e[5]*n,t}function Xi(t,e){var n=e[0],i=e[2],r=e[4],o=e[1],a=e[3],e=e[5],s=n*a-o*i;return s?(t[0]=a*(s=1/s),t[1]=-o*s,t[2]=-i*s,t[3]=n*s,t[4]=(i*e-a*r)*s,t[5]=(o*r-n*e)*s,t):null}var Ri=Object.freeze({__proto__:null,clone:function(t){var e=Bi();return Vi(e,t),e},copy:Vi,create:Bi,identity:Fi,invert:Xi,mul:Hi,rotate:Wi,scale:Ui,translate:Gi}),Yi=Fi;function qi(t){return 5e-5<t||t<-5e-5}var Zi=[],ji=[],Ki=Bi(),$i=Math.abs,Qi=(Ji.prototype.getLocalTransform=function(t){return Ji.getLocalTransform(this,t)},Ji.prototype.setPosition=function(t){this.x=t[0],this.y=t[1]},Ji.prototype.setScale=function(t){this.scaleX=t[0],this.scaleY=t[1]},Ji.prototype.setSkew=function(t){this.skewX=t[0],this.skewY=t[1]},Ji.prototype.setOrigin=function(t){this.originX=t[0],this.originY=t[1]},Ji.prototype.needLocalTransform=function(){return qi(this.rotation)||qi(this.x)||qi(this.y)||qi(this.scaleX-1)||qi(this.scaleY-1)||qi(this.skewX)||qi(this.skewY)},Ji.prototype.updateTransform=function(){var t=this.parent&&this.parent.transform,e=this.needLocalTransform(),n=this.transform;e||t?(n=n||Bi(),e?this.getLocalTransform(n):Yi(n),t&&(e?Hi(n,t,n):Vi(n,t)),this.transform=n,this._resolveGlobalScaleRatio(n)):n&&Yi(n)},Ji.prototype._resolveGlobalScaleRatio=function(t){var e,n,i=this.globalScaleRatio;null!=i&&1!==i&&(this.getGlobalScale(Zi),n=((Zi[1]-(n=Zi[1]<0?-1:1))*i+n)/Zi[1]||0,t[0]*=i=((Zi[0]-(e=Zi[0]<0?-1:1))*i+e)/Zi[0]||0,t[1]*=i,t[2]*=n,t[3]*=n),this.invTransform=this.invTransform||Bi(),Xi(this.invTransform,t)},Ji.prototype.getComputedTransform=function(){for(var t=this,e=[];t;)e.push(t),t=t.parent;for(;t=e.pop();)t.updateTransform();return this.transform},Ji.prototype.setLocalTransform=function(t){var e,n,i,r;t&&(r=t[0]*t[0]+t[1]*t[1],i=t[2]*t[2]+t[3]*t[3],e=Math.atan2(t[1],t[0]),n=Math.PI/2+e-Math.atan2(t[3],t[2]),i=Math.sqrt(i)*Math.cos(n),r=Math.sqrt(r),this.skewX=n,this.skewY=0,this.rotation=-e,this.x=+t[4],this.y=+t[5],this.scaleX=r,this.scaleY=i,this.originX=0,this.originY=0)},Ji.prototype.decomposeTransform=function(){var t,e,n;this.transform&&(e=this.parent,t=this.transform,e&&e.transform&&(Hi(ji,e.invTransform,t),t=ji),e=this.originX,n=this.originY,(e||n)&&(Ki[4]=e,Ki[5]=n,Hi(ji,t,Ki),ji[4]-=e,ji[5]-=n,t=ji),this.setLocalTransform(t))},Ji.prototype.getGlobalScale=function(t){var e=this.transform;return t=t||[],e?(t[0]=Math.sqrt(e[0]*e[0]+e[1]*e[1]),t[1]=Math.sqrt(e[2]*e[2]+e[3]*e[3]),e[0]<0&&(t[0]=-t[0]),e[3]<0&&(t[1]=-t[1])):(t[0]=1,t[1]=1),t},Ji.prototype.transformCoordToLocal=function(t,e){t=[t,e],e=this.invTransform;return e&&Jt(t,t,e),t},Ji.prototype.transformCoordToGlobal=function(t,e){t=[t,e],e=this.transform;return e&&Jt(t,t,e),t},Ji.prototype.getLineScale=function(){var t=this.transform;return t&&1e-10<$i(t[0]-1)&&1e-10<$i(t[3]-1)?Math.sqrt($i(t[0]*t[3]-t[2]*t[1])):1},Ji.prototype.copyTransform=function(t){for(var e=this,n=t,i=0;i<tr.length;i++){var r=tr[i];e[r]=n[r]}},Ji.getLocalTransform=function(t,e){e=e||[];var n=t.originX||0,i=t.originY||0,r=t.scaleX,o=t.scaleY,a=t.anchorX,s=t.anchorY,l=t.rotation||0,u=t.x,h=t.y,c=t.skewX?Math.tan(t.skewX):0,t=t.skewY?Math.tan(-t.skewY):0;return n||i||a||s?(e[4]=-(a=n+a)*r-c*(s=i+s)*o,e[5]=-s*o-t*a*r):e[4]=e[5]=0,e[0]=r,e[3]=o,e[1]=t*r,e[2]=c*o,l&&Wi(e,e,l),e[4]+=n+u,e[5]+=i+h,e},Ji.initDefaultProps=((i=Ji.prototype).scaleX=i.scaleY=i.globalScaleRatio=1,void(i.x=i.y=i.originX=i.originY=i.skewX=i.skewY=i.rotation=i.anchorX=i.anchorY=0)),Ji);function Ji(){}var tr=["x","y","originX","originY","anchorX","anchorY","rotation","scaleX","scaleY","skewX","skewY"];n.prototype.copy=function(t){return this.x=t.x,this.y=t.y,this},n.prototype.clone=function(){return new n(this.x,this.y)},n.prototype.set=function(t,e){return this.x=t,this.y=e,this},n.prototype.equal=function(t){return t.x===this.x&&t.y===this.y},n.prototype.add=function(t){return this.x+=t.x,this.y+=t.y,this},n.prototype.scale=function(t){this.x*=t,this.y*=t},n.prototype.scaleAndAdd=function(t,e){this.x+=t.x*e,this.y+=t.y*e},n.prototype.sub=function(t){return this.x-=t.x,this.y-=t.y,this},n.prototype.dot=function(t){return this.x*t.x+this.y*t.y},n.prototype.len=function(){return Math.sqrt(this.x*this.x+this.y*this.y)},n.prototype.lenSquare=function(){return this.x*this.x+this.y*this.y},n.prototype.normalize=function(){var t=this.len();return this.x/=t,this.y/=t,this},n.prototype.distance=function(t){var e=this.x-t.x,t=this.y-t.y;return Math.sqrt(e*e+t*t)},n.prototype.distanceSquare=function(t){var e=this.x-t.x,t=this.y-t.y;return e*e+t*t},n.prototype.negate=function(){return this.x=-this.x,this.y=-this.y,this},n.prototype.transform=function(t){var e,n;if(t)return e=this.x,n=this.y,this.x=t[0]*e+t[2]*n+t[4],this.y=t[1]*e+t[3]*n+t[5],this},n.prototype.toArray=function(t){return t[0]=this.x,t[1]=this.y,t},n.prototype.fromArray=function(t){this.x=t[0],this.y=t[1]},n.set=function(t,e,n){t.x=e,t.y=n},n.copy=function(t,e){t.x=e.x,t.y=e.y},n.len=function(t){return Math.sqrt(t.x*t.x+t.y*t.y)},n.lenSquare=function(t){return t.x*t.x+t.y*t.y},n.dot=function(t,e){return t.x*e.x+t.y*e.y},n.add=function(t,e,n){t.x=e.x+n.x,t.y=e.y+n.y},n.sub=function(t,e,n){t.x=e.x-n.x,t.y=e.y-n.y},n.scale=function(t,e,n){t.x=e.x*n,t.y=e.y*n},n.scaleAndAdd=function(t,e,n,i){t.x=e.x+n.x*i,t.y=e.y+n.y*i},n.lerp=function(t,e,n,i){var r=1-i;t.x=r*e.x+i*n.x,t.y=r*e.y+i*n.y};var M=n;function n(t,e){this.x=t||0,this.y=e||0}var er=Math.min,nr=Math.max,ir=new M,rr=new M,or=new M,ar=new M,sr=new M,lr=new M,X=(ur.prototype.union=function(t){var e=er(t.x,this.x),n=er(t.y,this.y);isFinite(this.x)&&isFinite(this.width)?this.width=nr(t.x+t.width,this.x+this.width)-e:this.width=t.width,isFinite(this.y)&&isFinite(this.height)?this.height=nr(t.y+t.height,this.y+this.height)-n:this.height=t.height,this.x=e,this.y=n},ur.prototype.applyTransform=function(t){ur.applyTransform(this,this,t)},ur.prototype.calculateTransform=function(t){var e=t.width/this.width,n=t.height/this.height,i=Bi();return Gi(i,i,[-this.x,-this.y]),Ui(i,i,[e,n]),Gi(i,i,[t.x,t.y]),i},ur.prototype.intersect=function(t,e){if(!t)return!1;t instanceof ur||(t=ur.create(t));var n,i,r,o,a,s,l,u,h=this,c=h.x,p=h.x+h.width,d=h.y,h=h.y+h.height,f=t.x,g=t.x+t.width,y=t.y,t=t.y+t.height,m=!(p<f||g<c||h<y||t<d);return e&&(n=1/0,i=0,r=Math.abs(p-f),o=Math.abs(g-c),a=Math.abs(h-y),s=Math.abs(t-d),l=Math.min(r,o),u=Math.min(a,s),p<f||g<c?i<l&&(i=l,r<o?M.set(lr,-r,0):M.set(lr,o,0)):l<n&&(n=l,r<o?M.set(sr,r,0):M.set(sr,-o,0)),h<y||t<d?i<u&&(i=u,a<s?M.set(lr,0,-a):M.set(lr,0,s)):l<n&&(n=l,a<s?M.set(sr,0,a):M.set(sr,0,-s))),e&&M.copy(e,m?sr:lr),m},ur.prototype.contain=function(t,e){var n=this;return t>=n.x&&t<=n.x+n.width&&e>=n.y&&e<=n.y+n.height},ur.prototype.clone=function(){return new ur(this.x,this.y,this.width,this.height)},ur.prototype.copy=function(t){ur.copy(this,t)},ur.prototype.plain=function(){return{x:this.x,y:this.y,width:this.width,height:this.height}},ur.prototype.isFinite=function(){return isFinite(this.x)&&isFinite(this.y)&&isFinite(this.width)&&isFinite(this.height)},ur.prototype.isZero=function(){return 0===this.width||0===this.height},ur.create=function(t){return new ur(t.x,t.y,t.width,t.height)},ur.copy=function(t,e){t.x=e.x,t.y=e.y,t.width=e.width,t.height=e.height},ur.applyTransform=function(t,e,n){var i,r,o,a;n?n[1]<1e-5&&-1e-5<n[1]&&n[2]<1e-5&&-1e-5<n[2]?(i=n[0],r=n[3],o=n[4],a=n[5],t.x=e.x*i+o,t.y=e.y*r+a,t.width=e.width*i,t.height=e.height*r,t.width<0&&(t.x+=t.width,t.width=-t.width),t.height<0&&(t.y+=t.height,t.height=-t.height)):(ir.x=or.x=e.x,ir.y=ar.y=e.y,rr.x=ar.x=e.x+e.width,rr.y=or.y=e.y+e.height,ir.transform(n),ar.transform(n),rr.transform(n),or.transform(n),t.x=er(ir.x,rr.x,or.x,ar.x),t.y=er(ir.y,rr.y,or.y,ar.y),o=nr(ir.x,rr.x,or.x,ar.x),a=nr(ir.y,rr.y,or.y,ar.y),t.width=o-t.x,t.height=a-t.y):t!==e&&ur.copy(t,e)},ur);function ur(t,e,n,i){n<0&&(t+=n,n=-n),i<0&&(e+=i,i=-i),this.x=t,this.y=e,this.width=n,this.height=i}var hr={};function cr(t,e){var n=hr[e=e||K],i=(n=n||(hr[e]=new Dn(500))).get(t);return null==i&&(i=H.measureText(t,e).width,n.put(t,i)),i}function pr(t,e,n,i){t=cr(t,e),e=yr(e),n=fr(0,t,n),i=gr(0,e,i);return new X(n,i,t,e)}function dr(t,e,n,i){var r=((t||"")+"").split("\n");if(1===r.length)return pr(r[0],e,n,i);for(var o=new X(0,0,0,0),a=0;a<r.length;a++){var s=pr(r[a],e,n,i);0===a?o.copy(s):o.union(s)}return o}function fr(t,e,n){return"right"===n?t-=e:"center"===n&&(t-=e/2),t}function gr(t,e,n){return"middle"===n?t-=e/2:"bottom"===n&&(t-=e),t}function yr(t){return cr("国",t)}function mr(t,e){return"string"==typeof t?0<=t.lastIndexOf("%")?parseFloat(t)/100*e:parseFloat(t):t}function vr(t,e,n){var i=e.position||"inside",r=null!=e.distance?e.distance:5,o=n.height,a=n.width,s=o/2,l=n.x,u=n.y,h="left",c="top";if(i instanceof Array)l+=mr(i[0],n.width),u+=mr(i[1],n.height),c=h=null;else switch(i){case"left":l-=r,u+=s,h="right",c="middle";break;case"right":l+=r+a,u+=s,c="middle";break;case"top":l+=a/2,u-=r,h="center",c="bottom";break;case"bottom":l+=a/2,u+=o+r,h="center";break;case"inside":l+=a/2,u+=s,h="center",c="middle";break;case"insideLeft":l+=r,u+=s,c="middle";break;case"insideRight":l+=a-r,u+=s,h="right",c="middle";break;case"insideTop":l+=a/2,u+=r,h="center";break;case"insideBottom":l+=a/2,u+=o-r,h="center",c="bottom";break;case"insideTopLeft":l+=r,u+=r;break;case"insideTopRight":l+=a-r,u+=r,h="right";break;case"insideBottomLeft":l+=r,u+=o-r,c="bottom";break;case"insideBottomRight":l+=a-r,u+=o-r,h="right",c="bottom"}return(t=t||{}).x=l,t.y=u,t.align=h,t.verticalAlign=c,t}var _r,xr="__zr_normal__",wr=tr.concat(["ignore"]),br=lt(tr,function(t,e){return t[e]=!0,t},{ignore:!1}),Sr={},Mr=new X(0,0,0,0),i=(r.prototype._init=function(t){this.attr(t)},r.prototype.drift=function(t,e,n){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0}var i=this.transform;(i=i||(this.transform=[1,0,0,1,0,0]))[4]+=t,i[5]+=e,this.decomposeTransform(),this.markRedraw()},r.prototype.beforeUpdate=function(){},r.prototype.afterUpdate=function(){},r.prototype.update=function(){this.updateTransform(),this.__dirty&&this.updateInnerText()},r.prototype.updateInnerText=function(t){var e,n,i,r,o,a,s,l,u,h,c=this._textContent;!c||c.ignore&&!t||(this.textConfig||(this.textConfig={}),l=(t=this.textConfig).local,i=n=void 0,r=!1,(e=c.innerTransformable).parent=l?this:null,h=!1,e.copyTransform(c),null!=t.position&&(u=Mr,t.layoutRect?u.copy(t.layoutRect):u.copy(this.getBoundingRect()),l||u.applyTransform(this.transform),this.calculateTextPosition?this.calculateTextPosition(Sr,t,u):vr(Sr,t,u),e.x=Sr.x,e.y=Sr.y,n=Sr.align,i=Sr.verticalAlign,o=t.origin)&&null!=t.rotation&&(s=a=void 0,s="center"===o?(a=.5*u.width,.5*u.height):(a=mr(o[0],u.width),mr(o[1],u.height)),h=!0,e.originX=-e.x+a+(l?0:u.x),e.originY=-e.y+s+(l?0:u.y)),null!=t.rotation&&(e.rotation=t.rotation),(o=t.offset)&&(e.x+=o[0],e.y+=o[1],h||(e.originX=-o[0],e.originY=-o[1])),a=null==t.inside?"string"==typeof t.position&&0<=t.position.indexOf("inside"):t.inside,s=this._innerTextDefaultStyle||(this._innerTextDefaultStyle={}),h=u=l=void 0,a&&this.canBeInsideText()?(l=t.insideFill,u=t.insideStroke,null!=l&&"auto"!==l||(l=this.getInsideTextFill()),null!=u&&"auto"!==u||(u=this.getInsideTextStroke(l),h=!0)):(l=t.outsideFill,u=t.outsideStroke,null!=l&&"auto"!==l||(l=this.getOutsideFill()),null!=u&&"auto"!==u||(u=this.getOutsideStroke(l),h=!0)),(l=l||"#000")===s.fill&&u===s.stroke&&h===s.autoStroke&&n===s.align&&i===s.verticalAlign||(r=!0,s.fill=l,s.stroke=u,s.autoStroke=h,s.align=n,s.verticalAlign=i,c.setDefaultTextStyle(s)),c.__dirty|=Xe,r&&c.dirtyStyle(!0))},r.prototype.canBeInsideText=function(){return!0},r.prototype.getInsideTextFill=function(){return"#fff"},r.prototype.getInsideTextStroke=function(t){return"#000"},r.prototype.getOutsideFill=function(){return this.__zr&&this.__zr.isDarkMode()?Ei:zi},r.prototype.getOutsideStroke=function(t){for(var e=this.__zr&&this.__zr.getBackgroundColor(),n="string"==typeof e&&Wn(e),i=(n=n||[255,255,255,1])[3],r=this.__zr.isDarkMode(),o=0;o<3;o++)n[o]=n[o]*i+(r?0:255)*(1-i);return n[3]=1,Zn(n,"rgba")},r.prototype.traverse=function(t,e){},r.prototype.attrKV=function(t,e){"textConfig"===t?this.setTextConfig(e):"textContent"===t?this.setTextContent(e):"clipPath"===t?this.setClipPath(e):"extra"===t?(this.extra=this.extra||{},L(this.extra,e)):this[t]=e},r.prototype.hide=function(){this.ignore=!0,this.markRedraw()},r.prototype.show=function(){this.ignore=!1,this.markRedraw()},r.prototype.attr=function(t,e){if("string"==typeof t)this.attrKV(t,e);else if(R(t))for(var n=ht(t),i=0;i<n.length;i++){var r=n[i];this.attrKV(r,t[r])}return this.markRedraw(),this},r.prototype.saveCurrentToNormalState=function(t){this._innerSaveToNormal(t);for(var e=this._normalState,n=0;n<this.animators.length;n++){var i=this.animators[n],r=i.__fromStateTransition;i.getLoop()||r&&r!==xr||(r=(r=i.targetName)?e[r]:e,i.saveTo(r))}},r.prototype._innerSaveToNormal=function(t){var e=(e=this._normalState)||(this._normalState={});t.textConfig&&!e.textConfig&&(e.textConfig=this.textConfig),this._savePrimaryToNormal(t,e,wr)},r.prototype._savePrimaryToNormal=function(t,e,n){for(var i=0;i<n.length;i++){var r=n[i];null==t[r]||r in e||(e[r]=this[r])}},r.prototype.hasState=function(){return 0<this.currentStates.length},r.prototype.getState=function(t){return this.states[t]},r.prototype.ensureState=function(t){var e=this.states;return e[t]||(e[t]={}),e[t]},r.prototype.clearStates=function(t){this.useState(xr,!1,t)},r.prototype.useState=function(t,e,n,i){var r=t===xr,o=this.hasState();if(o||!r){var a,o=this.currentStates,s=this.stateTransition;if(!(0<=I(o,t))||!e&&1!==o.length){if((a=(a=this.stateProxy&&!r?this.stateProxy(t):a)||this.states&&this.states[t])||r)return r||this.saveCurrentToNormalState(a),(o=!!(a&&a.hoverLayer||i))&&this._toggleHoverLayerFlag(!0),this._applyStateObj(t,a,this._normalState,e,!n&&!this.__inHover&&s&&0<s.duration,s),i=this._textContent,s=this._textGuide,i&&i.useState(t,e,n,o),s&&s.useState(t,e,n,o),r?(this.currentStates=[],this._normalState={}):e?this.currentStates.push(t):this.currentStates=[t],this._updateAnimationTargets(),this.markRedraw(),!o&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~Xe),a;it("State "+t+" not exists.")}}},r.prototype.useStates=function(t,e,n){if(t.length){var i=[],r=this.currentStates,o=t.length,a=o===r.length;if(a)for(var s=0;s<o;s++)if(t[s]!==r[s]){a=!1;break}if(!a){for(s=0;s<o;s++){var l=t[s],u=void 0;(u=(u=this.stateProxy?this.stateProxy(l,t):u)||this.states[l])&&i.push(u)}var h=i[o-1],h=!!(h&&h.hoverLayer||n),n=(h&&this._toggleHoverLayerFlag(!0),this._mergeStates(i)),c=this.stateTransition,n=(this.saveCurrentToNormalState(n),this._applyStateObj(t.join(","),n,this._normalState,!1,!e&&!this.__inHover&&c&&0<c.duration,c),this._textContent),c=this._textGuide;n&&n.useStates(t,e,h),c&&c.useStates(t,e,h),this._updateAnimationTargets(),this.currentStates=t.slice(),this.markRedraw(),!h&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~Xe)}}else this.clearStates()},r.prototype._updateAnimationTargets=function(){for(var t=0;t<this.animators.length;t++){var e=this.animators[t];e.targetName&&e.changeTarget(this[e.targetName])}},r.prototype.removeState=function(t){var e,t=I(this.currentStates,t);0<=t&&((e=this.currentStates.slice()).splice(t,1),this.useStates(e))},r.prototype.replaceState=function(t,e,n){var i=this.currentStates.slice(),t=I(i,t),r=0<=I(i,e);0<=t?r?i.splice(t,1):i[t]=e:n&&!r&&i.push(e),this.useStates(i)},r.prototype.toggleState=function(t,e){e?this.useState(t,!0):this.removeState(t)},r.prototype._mergeStates=function(t){for(var e,n={},i=0;i<t.length;i++){var r=t[i];L(n,r),r.textConfig&&L(e=e||{},r.textConfig)}return e&&(n.textConfig=e),n},r.prototype._applyStateObj=function(t,e,n,i,r,o){for(var a=!(e&&i),s=(e&&e.textConfig?(this.textConfig=L({},(i?this:n).textConfig),L(this.textConfig,e.textConfig)):a&&n.textConfig&&(this.textConfig=n.textConfig),{}),l=!1,u=0;u<wr.length;u++){var h=wr[u],c=r&&br[h];e&&null!=e[h]?c?(l=!0,s[h]=e[h]):this[h]=e[h]:a&&null!=n[h]&&(c?(l=!0,s[h]=n[h]):this[h]=n[h])}if(!r)for(u=0;u<this.animators.length;u++){var p=this.animators[u],d=p.targetName;p.getLoop()||p.__changeFinalValue(d?(e||n)[d]:e||n)}l&&this._transitionState(t,s,o)},r.prototype._attachComponent=function(t){var e;t.__zr&&!t.__hostTarget||t!==this&&((e=this.__zr)&&t.addSelfToZr(e),t.__zr=e,t.__hostTarget=this)},r.prototype._detachComponent=function(t){t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__hostTarget=null},r.prototype.getClipPath=function(){return this._clipPath},r.prototype.setClipPath=function(t){this._clipPath&&this._clipPath!==t&&this.removeClipPath(),this._attachComponent(t),this._clipPath=t,this.markRedraw()},r.prototype.removeClipPath=function(){var t=this._clipPath;t&&(this._detachComponent(t),this._clipPath=null,this.markRedraw())},r.prototype.getTextContent=function(){return this._textContent},r.prototype.setTextContent=function(t){var e=this._textContent;e!==t&&(e&&e!==t&&this.removeTextContent(),t.innerTransformable=new Qi,this._attachComponent(t),this._textContent=t,this.markRedraw())},r.prototype.setTextConfig=function(t){this.textConfig||(this.textConfig={}),L(this.textConfig,t),this.markRedraw()},r.prototype.removeTextConfig=function(){this.textConfig=null,this.markRedraw()},r.prototype.removeTextContent=function(){var t=this._textContent;t&&(t.innerTransformable=null,this._detachComponent(t),this._textContent=null,this._innerTextDefaultStyle=null,this.markRedraw())},r.prototype.getTextGuideLine=function(){return this._textGuide},r.prototype.setTextGuideLine=function(t){this._textGuide&&this._textGuide!==t&&this.removeTextGuideLine(),this._attachComponent(t),this._textGuide=t,this.markRedraw()},r.prototype.removeTextGuideLine=function(){var t=this._textGuide;t&&(this._detachComponent(t),this._textGuide=null,this.markRedraw())},r.prototype.markRedraw=function(){this.__dirty|=Xe;var t=this.__zr;t&&(this.__inHover?t.refreshHover():t.refresh()),this.__hostTarget&&this.__hostTarget.markRedraw()},r.prototype.dirty=function(){this.markRedraw()},r.prototype._toggleHoverLayerFlag=function(t){this.__inHover=t;var e=this._textContent,n=this._textGuide;e&&(e.__inHover=t),n&&(n.__inHover=t)},r.prototype.addSelfToZr=function(t){if(this.__zr!==t){this.__zr=t;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.addAnimator(e[n]);this._clipPath&&this._clipPath.addSelfToZr(t),this._textContent&&this._textContent.addSelfToZr(t),this._textGuide&&this._textGuide.addSelfToZr(t)}},r.prototype.removeSelfFromZr=function(t){if(this.__zr){this.__zr=null;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.removeAnimator(e[n]);this._clipPath&&this._clipPath.removeSelfFromZr(t),this._textContent&&this._textContent.removeSelfFromZr(t),this._textGuide&&this._textGuide.removeSelfFromZr(t)}},r.prototype.animate=function(t,e,n){var i=t?this[t]:this,i=new ui(i,e,n);return t&&(i.targetName=t),this.addAnimator(i,t),i},r.prototype.addAnimator=function(n,t){var e=this.__zr,i=this;n.during(function(){i.updateDuringAnimation(t)}).done(function(){var t=i.animators,e=I(t,n);0<=e&&t.splice(e,1)}),this.animators.push(n),e&&e.animation.addAnimator(n),e&&e.wakeUp()},r.prototype.updateDuringAnimation=function(t){this.markRedraw()},r.prototype.stopAnimation=function(t,e){for(var n=this.animators,i=n.length,r=[],o=0;o<i;o++){var a=n[o];t&&t!==a.scope?r.push(a):a.stop(e)}return this.animators=r,this},r.prototype.animateTo=function(t,e,n){Cr(this,t,e,n)},r.prototype.animateFrom=function(t,e,n){Cr(this,t,e,n,!0)},r.prototype._transitionState=function(t,e,n,i){for(var r=Cr(this,e,n,i),o=0;o<r.length;o++)r[o].__fromStateTransition=t},r.prototype.getBoundingRect=function(){return null},r.prototype.getPaintRect=function(){return null},r.initDefaultProps=((_r=r.prototype).type="element",_r.name="",_r.ignore=_r.silent=_r.isGroup=_r.draggable=_r.dragging=_r.ignoreClip=_r.__inHover=!1,_r.__dirty=Xe,void(Object.defineProperty&&(Tr("position","_legacyPos","x","y"),Tr("scale","_legacyScale","scaleX","scaleY"),Tr("origin","_legacyOrigin","originX","originY")))),r);function r(t){this.id=et++,this.animators=[],this.currentStates=[],this.states={},this._init(t)}function Tr(t,e,n,i){function r(e,t){Object.defineProperty(t,0,{get:function(){return e[n]},set:function(t){e[n]=t}}),Object.defineProperty(t,1,{get:function(){return e[i]},set:function(t){e[i]=t}})}Object.defineProperty(_r,t,{get:function(){var t;return this[e]||(t=this[e]=[],r(this,t)),this[e]},set:function(t){this[n]=t[0],this[i]=t[1],this[e]=t,r(this,t)}})}function Cr(t,e,n,i,r){function o(){u=!0,--l<=0&&(u?h&&h():c&&c())}function a(){--l<=0&&(u?h&&h():c&&c())}var s=[],l=(!function t(e,n,i,r,o,a,s,l){var u=ht(r);var h=o.duration;var c=o.delay;var p=o.additive;var d=o.setToFinal;var f=!R(a);var g=e.animators;var y=[];for(var m=0;m<u.length;m++){var v=u[m],_=r[v];null!=_&&null!=i[v]&&(f||a[v])?!R(_)||st(_)||vt(_)?y.push(v):n?l||(i[v]=_,e.updateDuringAnimation(n)):t(e,v,i[v],_,o,a&&a[v],s,l):l||(i[v]=_,e.updateDuringAnimation(n),y.push(v))}var x=y.length;if(!p&&x)for(var w,b=0;b<g.length;b++)(S=g[b]).targetName===n&&S.stopTracks(y)&&(w=I(g,S),g.splice(w,1));o.force||(y=ut(y,function(t){return!Dr(r[t],i[t])}),x=y.length);if(0<x||o.force&&!s.length){var S,M=void 0,T=void 0,C=void 0;if(l){T={},d&&(M={});for(b=0;b<x;b++){v=y[b];T[v]=i[v],d?M[v]=r[v]:i[v]=r[v]}}else if(d){C={};for(b=0;b<x;b++){v=y[b];C[v]=ni(i[v]),kr(i,r,v)}}(S=new ui(i,!1,!1,p?ut(g,function(t){return t.targetName===n}):null)).targetName=n,o.scope&&(S.scope=o.scope),d&&M&&S.whenWithKeys(0,M,y),C&&S.whenWithKeys(0,C,y),S.whenWithKeys(null==h?500:h,l?T:r,y).delay(c||0),e.addAnimator(S,n),s.push(S)}}(t,"",t,e,n=n||{},i,s,r),s.length),u=!1,h=n.done,c=n.aborted;l||h&&h(),0<s.length&&n.during&&s[0].during(function(t,e){n.during(e)});for(var p=0;p<s.length;p++){var d=s[p];d.done(o),d.aborted(a),n.force&&d.duration(n.duration),d.start(n.easing)}return s}function Ir(t,e,n){for(var i=0;i<n;i++)t[i]=e[i]}function kr(t,e,n){if(st(e[n]))if(st(t[n])||(t[n]=[]),yt(e[n])){var i=e[n].length;t[n].length!==i&&(t[n]=new e[n].constructor(i),Ir(t[n],e[n],i))}else{var r=e[n],o=t[n],a=r.length;if(st(r[0]))for(var s=r[0].length,l=0;l<a;l++)o[l]?Ir(o[l],r[l],s):o[l]=Array.prototype.slice.call(r[l]);else Ir(o,r,a);o.length=r.length}else t[n]=e[n]}function Dr(t,e){return t===e||st(t)&&st(e)&&function(t,e){var n=t.length;if(n!==e.length)return!1;for(var i=0;i<n;i++)if(t[i]!==e[i])return!1;return!0}(t,e)}at(i,ae),at(i,Qi);u(Lr,Ar=i),Lr.prototype.childrenRef=function(){return this._children},Lr.prototype.children=function(){return this._children.slice()},Lr.prototype.childAt=function(t){return this._children[t]},Lr.prototype.childOfName=function(t){for(var e=this._children,n=0;n<e.length;n++)if(e[n].name===t)return e[n]},Lr.prototype.childCount=function(){return this._children.length},Lr.prototype.add=function(t){return t&&t!==this&&t.parent!==this&&(this._children.push(t),this._doAdd(t)),this},Lr.prototype.addBefore=function(t,e){var n;return t&&t!==this&&t.parent!==this&&e&&e.parent===this&&0<=(e=(n=this._children).indexOf(e))&&(n.splice(e,0,t),this._doAdd(t)),this},Lr.prototype.replace=function(t,e){t=I(this._children,t);return 0<=t&&this.replaceAt(e,t),this},Lr.prototype.replaceAt=function(t,e){var n=this._children,i=n[e];return t&&t!==this&&t.parent!==this&&t!==i&&(n[e]=t,i.parent=null,(n=this.__zr)&&i.removeSelfFromZr(n),this._doAdd(t)),this},Lr.prototype._doAdd=function(t){t.parent&&t.parent.remove(t);var e=(t.parent=this).__zr;e&&e!==t.__zr&&t.addSelfToZr(e),e&&e.refresh()},Lr.prototype.remove=function(t){var e=this.__zr,n=this._children,i=I(n,t);return i<0||(n.splice(i,1),t.parent=null,e&&t.removeSelfFromZr(e),e&&e.refresh()),this},Lr.prototype.removeAll=function(){for(var t=this._children,e=this.__zr,n=0;n<t.length;n++){var i=t[n];e&&i.removeSelfFromZr(e),i.parent=null}return t.length=0,this},Lr.prototype.eachChild=function(t,e){for(var n=this._children,i=0;i<n.length;i++){var r=n[i];t.call(e,r,i)}return this},Lr.prototype.traverse=function(t,e){for(var n=0;n<this._children.length;n++){var i=this._children[n],r=t.call(e,i);i.isGroup&&!r&&i.traverse(t,e)}return this},Lr.prototype.addSelfToZr=function(t){Ar.prototype.addSelfToZr.call(this,t);for(var e=0;e<this._children.length;e++)this._children[e].addSelfToZr(t)},Lr.prototype.removeSelfFromZr=function(t){Ar.prototype.removeSelfFromZr.call(this,t);for(var e=0;e<this._children.length;e++)this._children[e].removeSelfFromZr(t)},Lr.prototype.getBoundingRect=function(t){for(var e=new X(0,0,0,0),n=t||this._children,i=[],r=null,o=0;o<n.length;o++){var a,s=n[o];s.ignore||s.invisible||(a=s.getBoundingRect(),(s=s.getLocalTransform(i))?(X.applyTransform(e,a,s),(r=r||e.clone()).union(e)):(r=r||a.clone()).union(a))}return r||e};var Ar,Pr=Lr;function Lr(t){var e=Ar.call(this)||this;return e.isGroup=!0,e._children=[],e.attr(t),e}Pr.prototype.type="group";var Or={},Rr={};zr.prototype.add=function(t){t&&(this.storage.addRoot(t),t.addSelfToZr(this),this.refresh())},zr.prototype.remove=function(t){t&&(this.storage.delRoot(t),t.removeSelfFromZr(this),this.refresh())},zr.prototype.configLayer=function(t,e){this.painter.configLayer&&this.painter.configLayer(t,e),this.refresh()},zr.prototype.setBackgroundColor=function(t){this.painter.setBackgroundColor&&this.painter.setBackgroundColor(t),this.refresh(),this._backgroundColor=t,this._darkMode=function(t){if(t){if("string"==typeof t)return jn(t,1)<.4;if(t.colorStops){for(var e=t.colorStops,n=0,i=e.length,r=0;r<i;r++)n+=jn(e[r].color,1);return(n/=i)<.4}}return!1}(t)},zr.prototype.getBackgroundColor=function(){return this._backgroundColor},zr.prototype.setDarkMode=function(t){this._darkMode=t},zr.prototype.isDarkMode=function(){return this._darkMode},zr.prototype.refreshImmediately=function(t){t||this.animation.update(!0),this._needsRefresh=!1,this.painter.refresh(),this._needsRefresh=!1},zr.prototype.refresh=function(){this._needsRefresh=!0,this.animation.start()},zr.prototype.flush=function(){this._flush(!1)},zr.prototype._flush=function(t){var e,n=hi(),t=(this._needsRefresh&&(e=!0,this.refreshImmediately(t)),this._needsRefreshHover&&(e=!0,this.refreshHoverImmediately()),hi());e?(this._stillFrameAccum=0,this.trigger("rendered",{elapsedTime:t-n})):0<this._sleepAfterStill&&(this._stillFrameAccum++,this._stillFrameAccum>this._sleepAfterStill)&&this.animation.stop()},zr.prototype.setSleepAfterStill=function(t){this._sleepAfterStill=t},zr.prototype.wakeUp=function(){this.animation.start(),this._stillFrameAccum=0},zr.prototype.refreshHover=function(){this._needsRefreshHover=!0},zr.prototype.refreshHoverImmediately=function(){this._needsRefreshHover=!1,this.painter.refreshHover&&"canvas"===this.painter.getType()&&this.painter.refreshHover()},zr.prototype.resize=function(t){this.painter.resize((t=t||{}).width,t.height),this.handler.resize()},zr.prototype.clearAnimation=function(){this.animation.clear()},zr.prototype.getWidth=function(){return this.painter.getWidth()},zr.prototype.getHeight=function(){return this.painter.getHeight()},zr.prototype.setCursorStyle=function(t){this.handler.setCursorStyle(t)},zr.prototype.findHover=function(t,e){return this.handler.findHover(t,e)},zr.prototype.on=function(t,e,n){return this.handler.on(t,e,n),this},zr.prototype.off=function(t,e){this.handler.off(t,e)},zr.prototype.trigger=function(t,e){this.handler.trigger(t,e)},zr.prototype.clear=function(){for(var t=this.storage.getRoots(),e=0;e<t.length;e++)t[e]instanceof Pr&&t[e].removeSelfFromZr(this);this.storage.delAllRoots(),this.painter.clear()},zr.prototype.dispose=function(){var t;this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,t=this.id,delete Rr[t]};var Nr=zr;function zr(t,e,n){var i=this,r=(this._sleepAfterStill=10,this._stillFrameAccum=0,this._needsRefresh=!0,this._needsRefreshHover=!0,this._darkMode=!1,n=n||{},this.dom=e,this.id=t,new Ke),o=n.renderer||"canvas",o=(Or[o]||(o=ht(Or)[0]),n.useDirtyRect=null!=n.useDirtyRect&&n.useDirtyRect,new Or[o](e,r,n,t)),e=n.ssr||o.ssrOnly,t=(this.storage=r,this.painter=o,b.node||b.worker||e?null:new Li(o.getViewportRoot(),o.root));this.handler=new Re(r,o,t,o.root),this.animation=new pi({stage:{update:e?null:function(){return i._flush(!0)}}}),e||this.animation.start()}function Er(t,e){t=new Nr(et++,t,e);return Rr[t.id]=t}function Br(t,e){Or[t]=e}var Fr=Object.freeze({__proto__:null,dispose:function(t){t.dispose()},disposeAll:function(){for(var t in Rr)Rr.hasOwnProperty(t)&&Rr[t].dispose();Rr={}},getInstance:function(t){return Rr[t]},init:Er,registerPainter:Br,version:"5.3.0"}),Vr=20;function Hr(t,e,n,i){var r=e[0],e=e[1],o=n[0],n=n[1],a=e-r,s=n-o;if(0==a)return 0==s?o:(o+n)/2;if(i)if(0<a){if(t<=r)return o;if(e<=t)return n}else{if(r<=t)return o;if(t<=e)return n}else{if(t===r)return o;if(t===e)return n}return(t-r)/a*s+o}function Gr(t,e){switch(t){case"center":case"middle":t="50%";break;case"left":case"top":t="0%";break;case"right":case"bottom":t="100%"}return V(t)?t.replace(/^\s+|\s+$/g,"").match(/%$/)?parseFloat(t)/100*e:parseFloat(t):null==t?NaN:+t}function Wr(t,e,n){return null==e&&(e=10),e=Math.min(Math.max(0,e),Vr),t=(+t).toFixed(e),n?t:+t}function Ur(t){if(t=+t,isNaN(t))return 0;if(1e-14<t)for(var e=1,n=0;n<15;n++,e*=10)if(Math.round(t*e)/e===t)return n;return Xr(t)}function Xr(t){var t=t.toString().toLowerCase(),e=t.indexOf("e"),n=0<e?+t.slice(e+1):0,e=0<e?e:t.length,t=t.indexOf(".");return Math.max(0,(t<0?0:e-1-t)-n)}function Yr(t,e){var n=Math.log,i=Math.LN10,t=Math.floor(n(t[1]-t[0])/i),n=Math.round(n(Math.abs(e[1]-e[0]))/i),e=Math.min(Math.max(-t+n,0),20);return isFinite(e)?e:20}function qr(t){var e=2*Math.PI;return(t%e+e)%e}function Zr(t){return-1e-4<t&&t<1e-4}var jr=/^(?:(\d{4})(?:[-\/](\d{1,2})(?:[-\/](\d{1,2})(?:[T ](\d{1,2})(?::(\d{1,2})(?::(\d{1,2})(?:[.,](\d+))?)?)?(Z|[\+\-]\d\d:?\d\d)?)?)?)?)?$/;function Kr(t){var e,n;return t instanceof Date?t:V(t)?(e=jr.exec(t))?e[8]?(n=+e[4]||0,"Z"!==e[8].toUpperCase()&&(n-=+e[8].slice(0,3)),new Date(Date.UTC(+e[1],+(e[2]||1)-1,+e[3]||1,n,+(e[5]||0),+e[6]||0,e[7]?+e[7].substring(0,3):0))):new Date(+e[1],+(e[2]||1)-1,+e[3]||1,+e[4]||0,+(e[5]||0),+e[6]||0,e[7]?+e[7].substring(0,3):0):new Date(NaN):null==t?new Date(NaN):new Date(Math.round(t))}function $r(t){return Math.pow(10,Qr(t))}function Qr(t){var e;return 0===t?0:(e=Math.floor(Math.log(t)/Math.LN10),10<=t/Math.pow(10,e)&&e++,e)}function Jr(t,e){var n=Qr(t),i=Math.pow(10,n),r=t/i,e=e?r<1.5?1:r<2.5?2:r<4?3:r<7?5:10:r<1?1:r<2?2:r<3?3:r<5?5:10;return t=e*i,-20<=n?+t.toFixed(n<0?-n:0):t}function to(t){var e=parseFloat(t);return e==t&&(0!==e||!V(t)||t.indexOf("x")<=0)?e:NaN}function eo(t){return!isNaN(to(t))}function no(){return Math.round(9*Math.random())}function io(t,e){return null==t?e:null==e?t:t*e/function t(e,n){return 0===n?e:t(n,e%n)}(t,e)}function f(t){throw new Error(t)}function ro(t,e,n){return(e-t)*n+t}var oo="series\0";function ao(t){return t instanceof Array?t:null==t?[]:[t]}function so(t,e,n){if(t){t[e]=t[e]||{},t.emphasis=t.emphasis||{},t.emphasis[e]=t.emphasis[e]||{};for(var i=0,r=n.length;i<r;i++){var o=n[i];!t.emphasis[e].hasOwnProperty(o)&&t[e].hasOwnProperty(o)&&(t.emphasis[e][o]=t[e][o])}}}var lo=["fontStyle","fontWeight","fontSize","fontFamily","rich","tag","color","textBorderColor","textBorderWidth","width","height","lineHeight","align","verticalAlign","baseline","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY","backgroundColor","borderColor","borderWidth","borderRadius","padding"];function uo(t){return!R(t)||F(t)||t instanceof Date?t:t.value}function ho(t,n,e){var o,a,s,l,r,u,i,h,c,p,d="normalMerge"===e,f="replaceMerge"===e,g="replaceAll"===e,y=(t=t||[],n=(n||[]).slice(),z()),e=(O(n,function(t,e){R(t)||(n[e]=null)}),function(t,e,n){var i=[];if("replaceAll"!==n)for(var r=0;r<t.length;r++){var o=t[r];o&&null!=o.id&&e.set(o.id,r),i.push({existing:"replaceMerge"===n||yo(o)?null:o,newOption:null,keyInfo:null,brandNew:null})}return i}(t,y,e));return(d||f)&&(o=e,a=t,s=y,O(l=n,function(t,e){var n,i,r;t&&null!=t.id&&(n=po(t.id),null!=(i=s.get(n)))&&(Ct(!(r=o[i]).newOption,'Duplicated option on id "'+n+'".'),r.newOption=t,r.existing=a[i],l[e]=null)})),d&&(r=e,O(u=n,function(t,e){if(t&&null!=t.name)for(var n=0;n<r.length;n++){var i=r[n].existing;if(!r[n].newOption&&i&&(null==i.id||null==t.id)&&!yo(t)&&!yo(i)&&co("name",i,t))return r[n].newOption=t,void(u[e]=null)}})),d||f?(h=e,c=f,O(n,function(t){if(t){for(var e,n=0;(e=h[n])&&(e.newOption||yo(e.existing)||e.existing&&null!=t.id&&!co("id",t,e.existing));)n++;e?(e.newOption=t,e.brandNew=c):h.push({newOption:t,brandNew:c,existing:null,keyInfo:null}),n++}})):g&&(i=e,O(n,function(t){i.push({newOption:t,brandNew:!0,existing:null,keyInfo:null})})),t=e,p=z(),O(t,function(t){var e=t.existing;e&&p.set(e.id,t)}),O(t,function(t){var e=t.newOption;Ct(!e||null==e.id||!p.get(e.id)||p.get(e.id)===t,"id duplicates: "+(e&&e.id)),e&&null!=e.id&&p.set(e.id,t),t.keyInfo||(t.keyInfo={})}),O(t,function(t,e){var n=t.existing,i=t.newOption,r=t.keyInfo;if(R(i)){if(r.name=null!=i.name?po(i.name):n?n.name:oo+e,n)r.id=po(n.id);else if(null!=i.id)r.id=po(i.id);else for(var o=0;r.id="\0"+r.name+"\0"+o++,p.get(r.id););p.set(r.id,t)}}),e}function co(t,e,n){e=fo(e[t],null),n=fo(n[t],null);return null!=e&&null!=n&&e===n}function po(t){return fo(t,"")}function fo(t,e){return null==t?e:V(t)?t:ft(t)||dt(t)?t+"":e}function go(t){t=t.name;return!(!t||!t.indexOf(oo))}function yo(t){return t&&null!=t.id&&0===po(t.id).indexOf("\0_ec_\0")}function mo(t,r,o){O(t,function(t){var e,n,i=t.newOption;R(i)&&(t.keyInfo.mainType=r,t.keyInfo.subType=(e=r,i=i,t=t.existing,n=o,i.type||(t?t.subType:n.determineSubType(e,i))))})}function vo(e,t){return null!=t.dataIndexInside?t.dataIndexInside:null!=t.dataIndex?F(t.dataIndex)?B(t.dataIndex,function(t){return e.indexOfRawIndex(t)}):e.indexOfRawIndex(t.dataIndex):null!=t.name?F(t.name)?B(t.name,function(t){return e.indexOfName(t)}):e.indexOfName(t.name):void 0}function _o(){var e="__ec_inner_"+xo++;return function(t){return t[e]||(t[e]={})}}var xo=no();function wo(n,t,i){var t=bo(t,i),e=t.mainTypeSpecified,r=t.queryOptionMap,o=t.others,a=i?i.defaultMainType:null;return!e&&a&&r.set(a,{}),r.each(function(t,e){t=Mo(n,e,t,{useDefault:a===e,enableAll:!i||null==i.enableAll||i.enableAll,enableNone:!i||null==i.enableNone||i.enableNone});o[e+"Models"]=t.models,o[e+"Model"]=t.models[0]}),o}function bo(t,i){var e=V(t)?((e={})[t+"Index"]=0,e):t,r=z(),o={},a=!1;return O(e,function(t,e){var n;"dataIndex"===e||"dataIndexInside"===e?o[e]=t:(n=(e=e.match(/^(\w+)(Index|Id|Name)$/)||[])[1],e=(e[2]||"").toLowerCase(),!n||!e||i&&i.includeMainTypes&&I(i.includeMainTypes,n)<0||(a=a||!!n,(r.get(n)||r.set(n,{}))[e]=t))}),{mainTypeSpecified:a,queryOptionMap:r,others:o}}var So={useDefault:!0,enableAll:!1,enableNone:!1};function Mo(t,e,n,i){i=i||So;var r=n.index,o=n.id,n=n.name,a={models:null,specified:null!=r||null!=o||null!=n};return a.specified?"none"===r||!1===r?(Ct(i.enableNone,'`"none"` or `false` is not a valid value on index option.'),a.models=[]):("all"===r&&(Ct(i.enableAll,'`"all"` is not a valid value on index option.'),r=o=n=null),a.models=t.queryComponents({mainType:e,index:r,id:o,name:n})):(r=void 0,a.models=i.useDefault&&(r=t.getComponent(e))?[r]:[]),a}function To(t,e,n){t.setAttribute?t.setAttribute(e,n):t[e]=n}function Co(t,e,n,i,r){var o=null==e||"auto"===e;if(null==i)return i;if(ft(i))return Wr(p=ro(n||0,i,r),o?Math.max(Ur(n||0),Ur(i)):e);if(V(i))return r<1?n:i;for(var a=[],s=n,l=i,u=Math.max(s?s.length:0,l.length),h=0;h<u;++h){var c,p,d=t.getDimensionInfo(h);d&&"ordinal"===d.type?a[h]=(r<1&&s?s:l)[h]:(p=ro(d=s&&s[h]?s[h]:0,c=l[h],r),a[h]=Wr(p,o?Math.max(Ur(d),Ur(c)):e))}return a}var Io=".",ko="___EC__COMPONENT__CONTAINER___",Do="___EC__EXTENDED_CLASS___";function Ao(t){var e={main:"",sub:""};return t&&(t=t.split(Io),e.main=t[0]||"",e.sub=t[1]||""),e}function Po(t){(t.$constructor=t).extend=function(t){var e,n,i,r=this;function o(){return n.apply(this,arguments)||this}return S(i=r)&&/^class\s/.test(Function.prototype.toString.call(i))?(u(o,n=r),e=o):ot(e=function(){(t.$constructor||r).apply(this,arguments)},this),L(e.prototype,t),e[Do]=!0,e.extend=this.extend,e.superCall=Ro,e.superApply=No,e.superClass=r,e}}function Lo(t,e){t.extend=e.extend}var Oo=Math.round(10*Math.random());function Ro(t,e){for(var n=[],i=2;i<arguments.length;i++)n[i-2]=arguments[i];return this.superClass.prototype[e].apply(t,n)}function No(t,e,n){return this.superClass.prototype[e].apply(t,n)}function zo(t){var r={};t.registerClass=function(t){var e,n=t.type||t.prototype.type;return n&&(Ct(/^[a-zA-Z0-9_]+([.][a-zA-Z0-9_]+)?$/.test(e=n),'componentType "'+e+'" illegal'),(e=Ao(t.prototype.type=n)).sub?e.sub!==ko&&(function(t){var e=r[t.main];e&&e[ko]||(e=r[t.main]={___EC__COMPONENT__CONTAINER___:!0});return e}(e)[e.sub]=t):r[e.main]=t),t},t.getClass=function(t,e,n){var i=r[t];if(i&&i[ko]&&(i=e?i[e]:null),n&&!i)throw new Error(e?"Component "+t+"."+(e||"")+" is used but not imported.":t+".type should be specified.");return i},t.getClassesByMainType=function(t){var t=Ao(t),n=[],t=r[t.main];return t&&t[ko]?O(t,function(t,e){e!==ko&&n.push(t)}):n.push(t),n},t.hasClass=function(t){t=Ao(t);return!!r[t.main]},t.getAllClassMainTypes=function(){var n=[];return O(r,function(t,e){n.push(e)}),n},t.hasSubTypes=function(t){t=Ao(t),t=r[t.main];return t&&t[ko]}}function Eo(a,s){for(var t=0;t<a.length;t++)a[t][1]||(a[t][1]=a[t][0]);return s=s||!1,function(t,e,n){for(var i={},r=0;r<a.length;r++){var o=a[r][1];e&&0<=I(e,o)||n&&I(n,o)<0||null!=(o=t.getShallow(o,s))&&(i[a[r][0]]=o)}return i}}var Bo=Eo([["fill","color"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["opacity"],["shadowColor"]]),Fo=(Vo.prototype.getAreaStyle=function(t,e){return Bo(this,t,e)},Vo);function Vo(){}var Ho=new Dn(50);function Go(t,e,n,i,r){return t?"string"==typeof t?(e&&e.__zrImageSrc===t||!n||(n={hostEl:n,cb:i,cbPayload:r},(i=Ho.get(t))?Uo(e=i.image)||i.pending.push(n):((r=H.loadImage(t,Wo,Wo)).__zrImageSrc=t,Ho.put(t,r.__cachedImgObj={image:r,pending:[n]}))),e):t:e}function Wo(){var t=this.__cachedImgObj;this.onload=this.onerror=this.__cachedImgObj=null;for(var e=0;e<t.pending.length;e++){var n=t.pending[e],i=n.cb;i&&i(this,n.cbPayload),n.hostEl.dirty()}t.pending.length=0}function Uo(t){return t&&t.width&&t.height}var Xo=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g;function Yo(t,e,n,i,r){if(!e)return"";var o=(t+"").split("\n");r=qo(e,n,i,r);for(var a=0,s=o.length;a<s;a++)o[a]=Zo(o[a],r);return o.join("\n")}function qo(t,e,n,i){for(var r=L({},i=i||{}),o=(r.font=e,n=N(n,"..."),r.maxIterations=N(i.maxIterations,2),r.minChar=N(i.minChar,0)),a=(r.cnCharWidth=cr("国",e),r.ascCharWidth=cr("a",e)),s=(r.placeholder=N(i.placeholder,""),t=Math.max(0,t-1)),l=0;l<o&&a<=s;l++)s-=a;i=cr(n,e);return s<i&&(n="",i=0),s=t-i,r.ellipsis=n,r.ellipsisWidth=i,r.contentWidth=s,r.containerWidth=t,r}function Zo(t,e){var n=e.containerWidth,i=e.font,r=e.contentWidth;if(!n)return"";var o=cr(t,i);if(!(o<=n)){for(var a=0;;a++){if(o<=r||a>=e.maxIterations){t+=e.ellipsis;break}var s=0===a?function(t,e,n,i){for(var r=0,o=0,a=t.length;o<a&&r<e;o++){var s=t.charCodeAt(o);r+=0<=s&&s<=127?n:i}return o}(t,r,e.ascCharWidth,e.cnCharWidth):0<o?Math.floor(t.length*r/o):0,o=cr(t=t.substr(0,s),i)}""===t&&(t=e.placeholder)}return t}var jo=function(){},Ko=function(t){this.tokens=[],t&&(this.tokens=t)},$o=function(){this.width=0,this.height=0,this.contentWidth=0,this.contentHeight=0,this.outerWidth=0,this.outerHeight=0,this.lines=[]};function Qo(t,e){var n=new $o;if(null!=t&&(t+=""),t){for(var i,r=e.width,o=e.height,a=e.overflow,s="break"!==a&&"breakAll"!==a||null==r?null:{width:r,accumWidth:0,breakAll:"breakAll"===a},l=Xo.lastIndex=0;null!=(i=Xo.exec(t));){var u=i.index;l<u&&Jo(n,t.substring(l,u),e,s),Jo(n,i[2],e,s,i[1]),l=Xo.lastIndex}l<t.length&&Jo(n,t.substring(l,t.length),e,s);var h,c=[],p=0,d=0,f=e.padding,g="truncate"===a,y="truncate"===e.lineOverflow;t:for(var m=0;m<n.lines.length;m++){for(var v=n.lines[m],_=0,x=0,w=0;w<v.tokens.length;w++){var b=(D=v.tokens[w]).styleName&&e.rich[D.styleName]||{},S=D.textPadding=b.padding,M=S?S[1]+S[3]:0,T=D.font=b.font||e.font,C=(D.contentHeight=yr(T),N(b.height,D.contentHeight));if(D.innerHeight=C,S&&(C+=S[0]+S[2]),D.height=C,D.lineHeight=St(b.lineHeight,e.lineHeight,C),D.align=b&&b.align||e.align,D.verticalAlign=b&&b.verticalAlign||"middle",y&&null!=o&&p+D.lineHeight>o){0<w?(v.tokens=v.tokens.slice(0,w),P(v,x,_),n.lines=n.lines.slice(0,m+1)):n.lines=n.lines.slice(0,m);break t}var I,S=b.width,k=null==S||"auto"===S;"string"==typeof S&&"%"===S.charAt(S.length-1)?(D.percentWidth=S,c.push(D),D.contentWidth=cr(D.text,T)):(k&&(S=(S=b.backgroundColor)&&S.image)&&(I=void 0,Uo(S="string"==typeof(h=S)?(I=Ho.get(h))&&I.image:h))&&(D.width=Math.max(D.width,S.width*C/S.height)),null!=(I=g&&null!=r?r-x:null)&&I<D.width?!k||I<M?(D.text="",D.width=D.contentWidth=0):(D.text=Yo(D.text,I-M,T,e.ellipsis,{minChar:e.truncateMinChar}),D.width=D.contentWidth=cr(D.text,T)):D.contentWidth=cr(D.text,T)),D.width+=M,x+=D.width,b&&(_=Math.max(_,D.lineHeight))}P(v,x,_)}n.outerWidth=n.width=N(r,d),n.outerHeight=n.height=N(o,p),n.contentHeight=p,n.contentWidth=d,f&&(n.outerWidth+=f[1]+f[3],n.outerHeight+=f[0]+f[2]);for(m=0;m<c.length;m++){var D,A=(D=c[m]).percentWidth;D.width=parseInt(A,10)/100*n.width}}return n;function P(t,e,n){t.width=e,t.lineHeight=n,p+=n,d=Math.max(d,e)}}function Jo(t,e,n,i,r){var o,a,s=""===e,l=r&&n.rich[r]||{},u=t.lines,h=l.font||n.font,c=!1;i?(n=(t=l.padding)?t[1]+t[3]:0,null!=l.width&&"auto"!==l.width?(t=mr(l.width,i.width)+n,0<u.length&&t+i.accumWidth>i.width&&(o=e.split("\n"),c=!0),i.accumWidth=t):(t=ea(e,h,i.width,i.breakAll,i.accumWidth),i.accumWidth=t.accumWidth+n,a=t.linesWidths,o=t.lines)):o=e.split("\n");for(var p=0;p<o.length;p++){var d,f,g=o[p],y=new jo;y.styleName=r,y.text=g,y.isLineHolder=!g&&!s,"number"==typeof l.width?y.width=l.width:y.width=a?a[p]:cr(g,h),p||c?u.push(new Ko([y])):1===(f=(d=(u[u.length-1]||(u[0]=new Ko)).tokens).length)&&d[0].isLineHolder?d[0]=y:!g&&f&&!s||d.push(y)}}var ta=lt(",&?/;] ".split(""),function(t,e){return t[e]=!0,t},{});function ea(t,e,n,i,r){for(var o,a=[],s=[],l="",u="",h=0,c=0,p=0;p<t.length;p++){var d,f,g=t.charAt(p);"\n"===g?(u&&(l+=u,c+=h),a.push(l),s.push(c),u=l="",c=h=0):(d=cr(g,e),f=!i&&(f=void 0,!!(33<=(f=(f=o=g).charCodeAt(0))&&f<=383&&!ta[o])),(a.length?n<c+d:n<r+c+d)?c?(l||u)&&(c=f?(l||(l=u,u="",c=h=0),a.push(l),s.push(c-h),u+=g,l="",h+=d):(u&&(l+=u,u="",h=0),a.push(l),s.push(c),l=g,d)):f?(a.push(u),s.push(h),u=g,h=d):(a.push(g),s.push(d)):(c+=d,f?(u+=g,h+=d):(u&&(l+=u,u="",h=0),l+=g)))}return a.length||l||(l=t,u="",h=0),u&&(l+=u),l&&(a.push(l),s.push(c)),1===a.length&&(c+=r),{accumWidth:c,lines:a,linesWidths:s}}var na,ia="__zr_style_"+Math.round(10*Math.random()),ra={shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,shadowColor:"#000",opacity:1,blend:"source-over"},oa={style:{shadowBlur:!0,shadowOffsetX:!0,shadowOffsetY:!0,shadowColor:!0,opacity:!0}},aa=(ra[ia]=!0,["z","z2","invisible"]),sa=["invisible"],i=(u(o,na=i),o.prototype._init=function(t){for(var e=ht(t),n=0;n<e.length;n++){var i=e[n];"style"===i?this.useStyle(t[i]):na.prototype.attrKV.call(this,i,t[i])}this.style||this.useStyle({})},o.prototype.beforeBrush=function(){},o.prototype.afterBrush=function(){},o.prototype.innerBeforeBrush=function(){},o.prototype.innerAfterBrush=function(){},o.prototype.shouldBePainted=function(t,e,n,i){var r=this.transform;if(this.ignore||this.invisible||0===this.style.opacity||this.culling&&function(t,e,n){la.copy(t.getBoundingRect()),t.transform&&la.applyTransform(t.transform);return ua.width=e,ua.height=n,!la.intersect(ua)}(this,t,e)||r&&!r[0]&&!r[3])return!1;if(n&&this.__clipPaths)for(var o=0;o<this.__clipPaths.length;++o)if(this.__clipPaths[o].isZeroArea())return!1;if(i&&this.parent)for(var a=this.parent;a;){if(a.ignore)return!1;a=a.parent}return!0},o.prototype.contain=function(t,e){return this.rectContain(t,e)},o.prototype.traverse=function(t,e){t.call(e,this)},o.prototype.rectContain=function(t,e){t=this.transformCoordToLocal(t,e);return this.getBoundingRect().contain(t[0],t[1])},o.prototype.getPaintRect=function(){var t,e,n,i,r,o=this._paintRect;return this._paintRect&&!this.__dirty||(r=this.transform,t=this.getBoundingRect(),e=(i=this.style).shadowBlur||0,n=i.shadowOffsetX||0,i=i.shadowOffsetY||0,o=this._paintRect||(this._paintRect=new X(0,0,0,0)),r?X.applyTransform(o,t,r):o.copy(t),(e||n||i)&&(o.width+=2*e+Math.abs(n),o.height+=2*e+Math.abs(i),o.x=Math.min(o.x,o.x+n-e),o.y=Math.min(o.y,o.y+i-e)),r=this.dirtyRectTolerance,o.isZero())||(o.x=Math.floor(o.x-r),o.y=Math.floor(o.y-r),o.width=Math.ceil(o.width+1+2*r),o.height=Math.ceil(o.height+1+2*r)),o},o.prototype.setPrevPaintRect=function(t){t?(this._prevPaintRect=this._prevPaintRect||new X(0,0,0,0),this._prevPaintRect.copy(t)):this._prevPaintRect=null},o.prototype.getPrevPaintRect=function(){return this._prevPaintRect},o.prototype.animateStyle=function(t){return this.animate("style",t)},o.prototype.updateDuringAnimation=function(t){"style"===t?this.dirtyStyle():this.markRedraw()},o.prototype.attrKV=function(t,e){"style"!==t?na.prototype.attrKV.call(this,t,e):this.style?this.setStyle(e):this.useStyle(e)},o.prototype.setStyle=function(t,e){return"string"==typeof t?this.style[t]=e:L(this.style,t),this.dirtyStyle(),this},o.prototype.dirtyStyle=function(t){t||this.markRedraw(),this.__dirty|=2,this._rect&&(this._rect=null)},o.prototype.dirty=function(){this.dirtyStyle()},o.prototype.styleChanged=function(){return!!(2&this.__dirty)},o.prototype.styleUpdated=function(){this.__dirty&=-3},o.prototype.createStyle=function(t){return Rt(ra,t)},o.prototype.useStyle=function(t){t[ia]||(t=this.createStyle(t)),this.__inHover?this.__hoverStyle=t:this.style=t,this.dirtyStyle()},o.prototype.isStyleObject=function(t){return t[ia]},o.prototype._innerSaveToNormal=function(t){na.prototype._innerSaveToNormal.call(this,t);var e=this._normalState;t.style&&!e.style&&(e.style=this._mergeStyle(this.createStyle(),this.style)),this._savePrimaryToNormal(t,e,aa)},o.prototype._applyStateObj=function(t,e,n,i,r,o){na.prototype._applyStateObj.call(this,t,e,n,i,r,o);var a,s=!(e&&i);if(e&&e.style?r?i?a=e.style:(a=this._mergeStyle(this.createStyle(),n.style),this._mergeStyle(a,e.style)):(a=this._mergeStyle(this.createStyle(),(i?this:n).style),this._mergeStyle(a,e.style)):s&&(a=n.style),a)if(r){var l=this.style;if(this.style=this.createStyle(s?{}:l),s)for(var u=ht(l),h=0;h<u.length;h++)(p=u[h])in a&&(a[p]=a[p],this.style[p]=l[p]);for(var c=ht(a),h=0;h<c.length;h++){var p=c[h];this.style[p]=this.style[p]}this._transitionState(t,{style:a},o,this.getAnimationStyleProps())}else this.useStyle(a);for(var d=this.__inHover?sa:aa,h=0;h<d.length;h++){p=d[h];e&&null!=e[p]?this[p]=e[p]:s&&null!=n[p]&&(this[p]=n[p])}},o.prototype._mergeStates=function(t){for(var e,n=na.prototype._mergeStates.call(this,t),i=0;i<t.length;i++){var r=t[i];r.style&&this._mergeStyle(e=e||{},r.style)}return e&&(n.style=e),n},o.prototype._mergeStyle=function(t,e){return L(t,e),t},o.prototype.getAnimationStyleProps=function(){return oa},o.initDefaultProps=((i=o.prototype).type="displayable",i.invisible=!1,i.z=0,i.z2=0,i.zlevel=0,i.culling=!1,i.cursor="pointer",i.rectHover=!1,i.incremental=!1,i._rect=null,i.dirtyRectTolerance=0,void(i.__dirty=2|Xe)),o);function o(t){return na.call(this,t)||this}var la=new X(0,0,0,0),ua=new X(0,0,0,0);var ha=Math.min,ca=Math.max,pa=Math.sin,da=Math.cos,fa=2*Math.PI,ga=Vt(),ya=Vt(),ma=Vt();function va(t,e,n,i,r,o){r[0]=ha(t,n),r[1]=ha(e,i),o[0]=ca(t,n),o[1]=ca(e,i)}var _a=[],xa=[];var Y={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},wa=[],ba=[],Sa=[],Ma=[],Ta=[],Ca=[],Ia=Math.min,ka=Math.max,Da=Math.cos,Aa=Math.sin,Pa=Math.abs,La=Math.PI,Oa=2*La,Ra="undefined"!=typeof Float32Array,Na=[];function za(t){return Math.round(t/La*1e8)/1e8%2*La}a.prototype.increaseVersion=function(){this._version++},a.prototype.getVersion=function(){return this._version},a.prototype.setScale=function(t,e,n){0<(n=n||0)&&(this._ux=Pa(n/Ni/t)||0,this._uy=Pa(n/Ni/e)||0)},a.prototype.setDPR=function(t){this.dpr=t},a.prototype.setContext=function(t){this._ctx=t},a.prototype.getContext=function(){return this._ctx},a.prototype.beginPath=function(){return this._ctx&&this._ctx.beginPath(),this.reset(),this},a.prototype.reset=function(){this._saveData&&(this._len=0),this._pathSegLen&&(this._pathSegLen=null,this._pathLen=0),this._version++},a.prototype.moveTo=function(t,e){return this._drawPendingPt(),this.addData(Y.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},a.prototype.lineTo=function(t,e){var n=Pa(t-this._xi),i=Pa(e-this._yi),r=n>this._ux||i>this._uy;return this.addData(Y.L,t,e),this._ctx&&r&&this._ctx.lineTo(t,e),r?(this._xi=t,this._yi=e,this._pendingPtDist=0):(r=n*n+i*i)>this._pendingPtDist&&(this._pendingPtX=t,this._pendingPtY=e,this._pendingPtDist=r),this},a.prototype.bezierCurveTo=function(t,e,n,i,r,o){return this._drawPendingPt(),this.addData(Y.C,t,e,n,i,r,o),this._ctx&&this._ctx.bezierCurveTo(t,e,n,i,r,o),this._xi=r,this._yi=o,this},a.prototype.quadraticCurveTo=function(t,e,n,i){return this._drawPendingPt(),this.addData(Y.Q,t,e,n,i),this._ctx&&this._ctx.quadraticCurveTo(t,e,n,i),this._xi=n,this._yi=i,this},a.prototype.arc=function(t,e,n,i,r,o){this._drawPendingPt(),Na[0]=i,Na[1]=r,s=o,(l=za((a=Na)[0]))<0&&(l+=Oa),h=l-a[0],u=a[1],u+=h,!s&&Oa<=u-l?u=l+Oa:s&&Oa<=l-u?u=l-Oa:!s&&u<l?u=l+(Oa-za(l-u)):s&&l<u&&(u=l-(Oa-za(u-l))),a[0]=l,a[1]=u;var a,s,l,u,h=(r=Na[1])-(i=Na[0]);return this.addData(Y.A,t,e,n,n,i,h,0,o?0:1),this._ctx&&this._ctx.arc(t,e,n,i,r,o),this._xi=Da(r)*n+t,this._yi=Aa(r)*n+e,this},a.prototype.arcTo=function(t,e,n,i,r){return this._drawPendingPt(),this._ctx&&this._ctx.arcTo(t,e,n,i,r),this},a.prototype.rect=function(t,e,n,i){return this._drawPendingPt(),this._ctx&&this._ctx.rect(t,e,n,i),this.addData(Y.R,t,e,n,i),this},a.prototype.closePath=function(){this._drawPendingPt(),this.addData(Y.Z);var t=this._ctx,e=this._x0,n=this._y0;return t&&t.closePath(),this._xi=e,this._yi=n,this},a.prototype.fill=function(t){t&&t.fill(),this.toStatic()},a.prototype.stroke=function(t){t&&t.stroke(),this.toStatic()},a.prototype.len=function(){return this._len},a.prototype.setData=function(t){var e=t.length;this.data&&this.data.length===e||!Ra||(this.data=new Float32Array(e));for(var n=0;n<e;n++)this.data[n]=t[n];this._len=e},a.prototype.appendPath=function(t){for(var e=(t=t instanceof Array?t:[t]).length,n=0,i=this._len,r=0;r<e;r++)n+=t[r].len();Ra&&this.data instanceof Float32Array&&(this.data=new Float32Array(i+n));for(r=0;r<e;r++)for(var o=t[r].data,a=0;a<o.length;a++)this.data[i++]=o[a];this._len=i},a.prototype.addData=function(t,e,n,i,r,o,a,s,l){if(this._saveData){var u=this.data;this._len+arguments.length>u.length&&(this._expandData(),u=this.data);for(var h=0;h<arguments.length;h++)u[this._len++]=arguments[h]}},a.prototype._drawPendingPt=function(){0<this._pendingPtDist&&(this._ctx&&this._ctx.lineTo(this._pendingPtX,this._pendingPtY),this._pendingPtDist=0)},a.prototype._expandData=function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},a.prototype.toStatic=function(){var t;this._saveData&&(this._drawPendingPt(),(t=this.data)instanceof Array)&&(t.length=this._len,Ra)&&11<this._len&&(this.data=new Float32Array(t))},a.prototype.getBoundingRect=function(){Sa[0]=Sa[1]=Ta[0]=Ta[1]=Number.MAX_VALUE,Ma[0]=Ma[1]=Ca[0]=Ca[1]=-Number.MAX_VALUE;for(var t,e=this.data,n=0,i=0,r=0,o=0,a=0;a<this._len;){var z=e[a++],E=1===a;switch(E&&(r=n=e[a],o=i=e[a+1]),z){case Y.M:n=r=e[a++],i=o=e[a++],Ta[0]=r,Ta[1]=o,Ca[0]=r,Ca[1]=o;break;case Y.L:va(n,i,e[a],e[a+1],Ta,Ca),n=e[a++],i=e[a++];break;case Y.C:G=H=m=y=V=g=f=d=p=c=F=B=h=u=l=s=void 0;var s=n,l=i,u=e[a++],h=e[a++],B=e[a++],F=e[a++],c=e[a],p=e[a+1],d=Ta,f=Ca,g=fn,V=cn,y=g(s,u,B,c,_a);d[0]=1/0,d[1]=1/0,f[0]=-1/0,f[1]=-1/0;for(var m=0;m<y;m++){var H=V(s,u,B,c,_a[m]);d[0]=ha(H,d[0]),f[0]=ca(H,f[0])}for(y=g(l,h,F,p,xa),m=0;m<y;m++){var G=V(l,h,F,p,xa[m]);d[1]=ha(G,d[1]),f[1]=ca(G,f[1])}d[0]=ha(s,d[0]),f[0]=ca(s,f[0]),d[0]=ha(c,d[0]),f[0]=ca(c,f[0]),d[1]=ha(l,d[1]),f[1]=ca(l,f[1]),d[1]=ha(p,d[1]),f[1]=ca(p,f[1]),n=e[a++],i=e[a++];break;case Y.Q:g=n,L=i,M=e[a++],x=e[a++],S=e[a],v=e[a+1],b=Ta,T=Ca,t=w=t=_=void 0,_=mn,t=ca(ha((w=_n)(g,M,S),1),0),w=ca(ha(w(L,x,v),1),0),M=_(g,M,S,t),t=_(L,x,v,w),b[0]=ha(g,S,M),b[1]=ha(L,v,t),T[0]=ca(g,S,M),T[1]=ca(L,v,t),n=e[a++],i=e[a++];break;case Y.A:var v,_=e[a++],x=e[a++],w=e[a++],b=e[a++],S=e[a++],M=e[a++]+S,T=(a+=1,!e[a++]),C=(E&&(r=Da(S)*w+_,o=Aa(S)*b+x),N=v=U=W=R=O=L=P=A=D=k=I=C=void 0,_),I=x,k=w,D=b,A=S,P=M,L=T,O=Ta,R=Ca,W=te,U=ee;if((v=Math.abs(A-P))%fa<1e-4&&1e-4<v)O[0]=C-k,O[1]=I-D,R[0]=C+k,R[1]=I+D;else{ga[0]=da(A)*k+C,ga[1]=pa(A)*D+I,ya[0]=da(P)*k+C,ya[1]=pa(P)*D+I,W(O,ga,ya),U(R,ga,ya),(A%=fa)<0&&(A+=fa),(P%=fa)<0&&(P+=fa),P<A&&!L?P+=fa:A<P&&L&&(A+=fa),L&&(v=P,P=A,A=v);for(var N=0;N<P;N+=Math.PI/2)A<N&&(ma[0]=da(N)*k+C,ma[1]=pa(N)*D+I,W(O,ma,O),U(R,ma,R))}n=Da(M)*w+_,i=Aa(M)*b+x;break;case Y.R:va(r=n=e[a++],o=i=e[a++],r+e[a++],o+e[a++],Ta,Ca);break;case Y.Z:n=r,i=o}te(Sa,Sa,Ta),ee(Ma,Ma,Ca)}return 0===a&&(Sa[0]=Sa[1]=Ma[0]=Ma[1]=0),new X(Sa[0],Sa[1],Ma[0]-Sa[0],Ma[1]-Sa[1])},a.prototype._calculateLength=function(){for(var t=this.data,e=this._len,n=this._ux,i=this._uy,r=0,o=0,a=0,s=0,l=(this._pathSegLen||(this._pathSegLen=[]),this._pathSegLen),u=0,h=0,c=0;c<e;){var p=t[c++],d=1===c,f=(d&&(a=r=t[c],s=o=t[c+1]),-1);switch(p){case Y.M:r=a=t[c++],o=s=t[c++];break;case Y.L:var g=t[c++],y=(_=t[c++])-o;(Pa(I=g-r)>n||Pa(y)>i||c===e-1)&&(f=Math.sqrt(I*I+y*y),r=g,o=_);break;case Y.C:var m=t[c++],v=t[c++],g=t[c++],_=t[c++],x=t[c++],w=t[c++],f=function(t,e,n,i,r,o,a,s,l){for(var u=t,h=e,c=0,p=1/l,d=1;d<=l;d++){var f=d*p,g=cn(t,n,r,a,f),f=cn(e,i,o,s,f),y=g-u,m=f-h;c+=Math.sqrt(y*y+m*m),u=g,h=f}return c}(r,o,m,v,g,_,x,w,10),r=x,o=w;break;case Y.Q:f=function(t,e,n,i,r,o,a){for(var s=t,l=e,u=0,h=1/a,c=1;c<=a;c++){var p=c*h,d=mn(t,n,r,p),p=mn(e,i,o,p),f=d-s,g=p-l;u+=Math.sqrt(f*f+g*g),s=d,l=p}return u}(r,o,m=t[c++],v=t[c++],g=t[c++],_=t[c++],10),r=g,o=_;break;case Y.A:var x=t[c++],w=t[c++],b=t[c++],S=t[c++],M=t[c++],T=t[c++],C=T+M;c+=1,t[c++],d&&(a=Da(M)*b+x,s=Aa(M)*S+w),f=ka(b,S)*Ia(Oa,Math.abs(T)),r=Da(C)*b+x,o=Aa(C)*S+w;break;case Y.R:a=r=t[c++],s=o=t[c++];f=2*t[c++]+2*t[c++];break;case Y.Z:var I=a-r,y=s-o;f=Math.sqrt(I*I+y*y),r=a,o=s}0<=f&&(u+=l[h++]=f)}return this._pathLen=u},a.prototype.rebuildPath=function(t,e){var n,i,r,o,a,s,l,u,h=this.data,z=this._ux,E=this._uy,B=this._len,c=e<1,p=0,d=0,f=0;if(!c||(this._pathSegLen||this._calculateLength(),a=this._pathSegLen,s=e*this._pathLen))t:for(var g=0;g<B;){var y=h[g++],F=1===g;switch(F&&(n=r=h[g],i=o=h[g+1]),y!==Y.L&&0<f&&(t.lineTo(l,u),f=0),y){case Y.M:n=r=h[g++],i=o=h[g++],t.moveTo(r,o);break;case Y.L:var m=h[g++],v=h[g++],_=Pa(m-r),x=Pa(v-o);if(z<_||E<x){if(c){if(s<p+(N=a[d++])){var w=(s-p)/N;t.lineTo(r*(1-w)+m*w,o*(1-w)+v*w);break t}p+=N}t.lineTo(m,v),r=m,o=v,f=0}else{_=_*_+x*x;f<_&&(l=m,u=v,f=_)}break;case Y.C:var b=h[g++],S=h[g++],M=h[g++],T=h[g++],x=h[g++],_=h[g++];if(c){if(s<p+(N=a[d++])){gn(r,b,M,x,w=(s-p)/N,wa),gn(o,S,T,_,w,ba),t.bezierCurveTo(wa[1],ba[1],wa[2],ba[2],wa[3],ba[3]);break t}p+=N}t.bezierCurveTo(b,S,M,T,x,_),r=x,o=_;break;case Y.Q:b=h[g++],S=h[g++],M=h[g++],T=h[g++];if(c){if(s<p+(N=a[d++])){xn(r,b,M,w=(s-p)/N,wa),xn(o,S,T,w,ba),t.quadraticCurveTo(wa[1],ba[1],wa[2],ba[2]);break t}p+=N}t.quadraticCurveTo(b,S,M,T),r=M,o=T;break;case Y.A:var C=h[g++],I=h[g++],k=h[g++],D=h[g++],A=h[g++],P=h[g++],L=h[g++],V=!h[g++],H=D<k?k:D,O=.001<Pa(k-D),R=A+P,G=!1;if(c&&(s<p+(N=a[d++])&&(R=A+P*(s-p)/N,G=!0),p+=N),O&&t.ellipse?t.ellipse(C,I,k,D,L,A,R,V):t.arc(C,I,H,A,R,V),G)break t;F&&(n=Da(A)*k+C,i=Aa(A)*D+I),r=Da(R)*k+C,o=Aa(R)*D+I;break;case Y.R:n=r=h[g],i=o=h[g+1],m=h[g++],v=h[g++];var N,P=h[g++],O=h[g++];if(c){if(s<p+(N=a[d++])){L=s-p;t.moveTo(m,v),t.lineTo(m+Ia(L,P),v),0<(L-=P)&&t.lineTo(m+P,v+Ia(L,O)),0<(L-=O)&&t.lineTo(m+ka(P-L,0),v+O),0<(L-=P)&&t.lineTo(m,v+ka(O-L,0));break t}p+=N}t.rect(m,v,P,O);break;case Y.Z:if(c){if(s<p+(N=a[d++])){w=(s-p)/N;t.lineTo(r*(1-w)+n*w,o*(1-w)+i*w);break t}p+=N}t.closePath(),r=n,o=i}}},a.prototype.clone=function(){var t=new a,e=this.data;return t.data=e.slice?e.slice():Array.prototype.slice.call(e),t._len=this._len,t},a.CMD=Y,a.initDefaultProps=((Jl=a.prototype)._saveData=!0,Jl._ux=0,Jl._uy=0,Jl._pendingPtDist=0,void(Jl._version=0));var Ea=a;function a(t){this.dpr=1,this._xi=0,this._yi=0,this._x0=0,this._y0=0,this._len=0,t&&(this._saveData=!1),this._saveData&&(this.data=[])}function Ba(t,e,n,i,r,o,a){var s;if(0!==r)return s=0,!(e+(r=r)<a&&i+r<a||a<e-r&&a<i-r||t+r<o&&n+r<o||o<t-r&&o<n-r)&&(t===n?Math.abs(o-t)<=r/2:(o=(s=(e-i)/(t-n))*o-a+(t*i-n*e)/(t-n))*o/(s*s+1)<=r/2*r/2)}var Fa=2*Math.PI;function Va(t){return(t%=Fa)<0&&(t+=Fa),t}var Ha=2*Math.PI;function Ga(t,e,n,i,r,o){return e<o&&i<o||o<e&&o<i||i===e?0:(n=(o=(o-e)/(i-e))*(n-t)+t)===r?1/0:r<n?1!=o&&0!=o?i<e?1:-1:i<e?.5:-.5:0}var Wa=Ea.CMD,Ua=2*Math.PI,Xa=1e-4;var Ya=[-1,-1,-1],qa=[-1,-1];function Za(t,e,n,i,r,o,a,s,l,u){if(e<u&&i<u&&o<u&&s<u||u<e&&u<i&&u<o&&u<s)return 0;var h=dn(e,i,o,s,u,Ya);if(0===h)return 0;for(var c,p=0,d=-1,f=void 0,g=void 0,y=0;y<h;y++){var m=Ya[y],v=0===m||1===m?.5:1;cn(t,n,r,a,m)<l||(d<0&&(d=fn(e,i,o,s,qa),qa[1]<qa[0]&&1<d&&(c=void 0,c=qa[0],qa[0]=qa[1],qa[1]=c),f=cn(e,i,o,s,qa[0]),1<d)&&(g=cn(e,i,o,s,qa[1])),2===d?m<qa[0]?p+=f<e?v:-v:m<qa[1]?p+=g<f?v:-v:p+=s<g?v:-v:m<qa[0]?p+=f<e?v:-v:p+=s<f?v:-v)}return p}function ja(t,e,n,i,r,o,a,s){if(e<s&&i<s&&o<s||s<e&&s<i&&s<o)return 0;c=Ya,h=(l=e)-2*(u=i)+(h=o),u=2*(u-l),l-=s=s,s=0,un(h)?hn(u)&&0<=(p=-l/u)&&p<=1&&(c[s++]=p):un(l=u*u-4*h*l)?0<=(p=-u/(2*h))&&p<=1&&(c[s++]=p):0<l&&(d=(-u-(l=en(l)))/(2*h),0<=(p=(-u+l)/(2*h))&&p<=1&&(c[s++]=p),0<=d)&&d<=1&&(c[s++]=d);var l,u,h,c,p,d,f=s;if(0===f)return 0;var g=_n(e,i,o);if(0<=g&&g<=1){for(var y=0,m=mn(e,i,o,g),v=0;v<f;v++){var _=0===Ya[v]||1===Ya[v]?.5:1;mn(t,n,r,Ya[v])<a||(Ya[v]<g?y+=m<e?_:-_:y+=o<m?_:-_)}return y}return _=0===Ya[0]||1===Ya[0]?.5:1,mn(t,n,r,Ya[0])<a?0:o<e?_:-_}function Ka(t,e,n,i,r){for(var o,a=t.data,s=t.len(),l=0,u=0,h=0,c=0,p=0,d=0;d<s;){var f=a[d++],g=1===d;switch(f===Wa.M&&1<d&&(n||(l+=Ga(u,h,c,p,i,r))),g&&(c=u=a[d],p=h=a[d+1]),f){case Wa.M:u=c=a[d++],h=p=a[d++];break;case Wa.L:if(n){if(Ba(u,h,a[d],a[d+1],e,i,r))return!0}else l+=Ga(u,h,a[d],a[d+1],i,r)||0;u=a[d++],h=a[d++];break;case Wa.C:if(n){if(function(t,e,n,i,r,o,a,s,l,u,h){if(0!==l)return!(e+(l=l)<h&&i+l<h&&o+l<h&&s+l<h||h<e-l&&h<i-l&&h<o-l&&h<s-l||t+l<u&&n+l<u&&r+l<u&&a+l<u||u<t-l&&u<n-l&&u<r-l&&u<a-l)&&yn(t,e,n,i,r,o,a,s,u,h,null)<=l/2}(u,h,a[d++],a[d++],a[d++],a[d++],a[d],a[d+1],e,i,r))return!0}else l+=Za(u,h,a[d++],a[d++],a[d++],a[d++],a[d],a[d+1],i,r)||0;u=a[d++],h=a[d++];break;case Wa.Q:if(n){if(function(t,e,n,i,r,o,a,s,l){if(0!==a)return!(e+(a=a)<l&&i+a<l&&o+a<l||l<e-a&&l<i-a&&l<o-a||t+a<s&&n+a<s&&r+a<s||s<t-a&&s<n-a&&s<r-a)&&wn(t,e,n,i,r,o,s,l,null)<=a/2}(u,h,a[d++],a[d++],a[d],a[d+1],e,i,r))return!0}else l+=ja(u,h,a[d++],a[d++],a[d],a[d+1],i,r)||0;u=a[d++],h=a[d++];break;case Wa.A:var y=a[d++],m=a[d++],v=a[d++],_=a[d++],x=a[d++],w=a[d++],b=(d+=1,!!(1-a[d++])),S=Math.cos(x)*v+y,M=Math.sin(x)*_+m,T=(g?(c=S,p=M):l+=Ga(u,h,S,M,i,r),(i-y)*_/v+y);if(n){if(function(t,e,n,i,r,o,a,s,l){if(0!==a)return a=a,s-=t,l-=e,!(n<(t=Math.sqrt(s*s+l*l))-a||t+a<n)&&(Math.abs(i-r)%Ha<1e-4||((r=o?(e=i,i=Va(r),Va(e)):(i=Va(i),Va(r)))<i&&(r+=Ha),(t=Math.atan2(l,s))<0&&(t+=Ha),i<=t&&t<=r)||i<=t+Ha&&t+Ha<=r)}(y,m,_,x,x+w,b,e,T,r))return!0}else l+=function(t,e,n,i,r,o,a,s){if(n<(s-=e)||s<-n)return 0;var e=Math.sqrt(n*n-s*s);if(Ya[0]=-e,Ya[1]=e,(n=Math.abs(i-r))<1e-4)return 0;if(Ua-1e-4<=n)return r=Ua,h=o?1:-1,a>=Ya[i=0]+t&&a<=Ya[1]+t?h:0;r<i&&(e=i,i=r,r=e),i<0&&(i+=Ua,r+=Ua);for(var l=0,u=0;u<2;u++){var h,c=Ya[u];a<c+t&&(h=o?1:-1,i<=(c=(c=Math.atan2(s,c))<0?Ua+c:c)&&c<=r||i<=c+Ua&&c+Ua<=r)&&(l+=h=c>Math.PI/2&&c<1.5*Math.PI?-h:h)}return l}(y,m,_,x,x+w,b,T,r);u=Math.cos(x+w)*v+y,h=Math.sin(x+w)*_+m;break;case Wa.R:c=u=a[d++],p=h=a[d++];if(S=c+a[d++],M=p+a[d++],n){if(Ba(c,p,S,p,e,i,r)||Ba(S,p,S,M,e,i,r)||Ba(S,M,c,M,e,i,r)||Ba(c,M,c,p,e,i,r))return!0}else l=(l+=Ga(S,p,S,M,i,r))+Ga(c,M,c,p,i,r);break;case Wa.Z:if(n){if(Ba(u,h,c,p,e,i,r))return!0}else l+=Ga(u,h,c,p,i,r);u=c,h=p}}return n||(t=h,o=p,Math.abs(t-o)<Xa)||(l+=Ga(u,h,c,p,i,r)||0),0!==l}var $a,Qa=E({fill:"#000",stroke:null,strokePercent:1,fillOpacity:1,strokeOpacity:1,lineDashOffset:0,lineWidth:1,lineCap:"butt",miterLimit:10,strokeNoScale:!1,strokeFirst:!1},ra),Ja={style:E({fill:!0,stroke:!0,strokePercent:!0,fillOpacity:!0,strokeOpacity:!0,lineDashOffset:!0,lineWidth:!0,miterLimit:!0},oa.style)},ts=tr.concat(["invisible","culling","z","z2","zlevel","parent"]),j=(u(s,$a=i),s.prototype.update=function(){var e=this,t=($a.prototype.update.call(this),this.style);if(t.decal){var n,i=this._decalEl=this._decalEl||new s,r=(i.buildPath===s.prototype.buildPath&&(i.buildPath=function(t){e.buildPath(t,e.shape)}),i.silent=!0,i.style);for(n in t)r[n]!==t[n]&&(r[n]=t[n]);r.fill=t.fill?t.decal:null,r.decal=null,r.shadowColor=null,t.strokeFirst&&(r.stroke=null);for(var o=0;o<ts.length;++o)i[ts[o]]=this[ts[o]];i.__dirty|=Xe}else this._decalEl&&(this._decalEl=null)},s.prototype.getDecalElement=function(){return this._decalEl},s.prototype._init=function(t){var e=ht(t),n=(this.shape=this.getDefaultShape(),this.getDefaultStyle());n&&this.useStyle(n);for(var i=0;i<e.length;i++){var r=e[i],o=t[r];"style"===r?this.style?L(this.style,o):this.useStyle(o):"shape"===r?L(this.shape,o):$a.prototype.attrKV.call(this,r,o)}this.style||this.useStyle({})},s.prototype.getDefaultStyle=function(){return null},s.prototype.getDefaultShape=function(){return{}},s.prototype.canBeInsideText=function(){return this.hasFill()},s.prototype.getInsideTextFill=function(){var t,e=this.style.fill;if("none"!==e){if(V(e))return.5<(t=jn(e,0))?zi:.2<t?"#eee":Ei;if(e)return Ei}return zi},s.prototype.getInsideTextStroke=function(t){var e=this.style.fill;if(V(e)){var n=this.__zr;if(!(!n||!n.isDarkMode())==jn(t,0)<.4)return e}},s.prototype.buildPath=function(t,e,n){},s.prototype.pathUpdated=function(){this.__dirty&=~Ye},s.prototype.getUpdatedPathProxy=function(t){return this.path||this.createPathProxy(),this.path.beginPath(),this.buildPath(this.path,this.shape,t),this.path},s.prototype.createPathProxy=function(){this.path=new Ea(!1)},s.prototype.hasStroke=function(){var t=this.style,e=t.stroke;return!(null==e||"none"===e||!(0<t.lineWidth))},s.prototype.hasFill=function(){var t=this.style.fill;return null!=t&&"none"!==t},s.prototype.getBoundingRect=function(){var t,e,n=this._rect,i=this.style,r=!n;return r&&(t=!1,this.path||(t=!0,this.createPathProxy()),e=this.path,(t||this.__dirty&Ye)&&(e.beginPath(),this.buildPath(e,this.shape,!1),this.pathUpdated()),n=e.getBoundingRect()),this._rect=n,this.hasStroke()&&this.path&&0<this.path.len()?(t=this._rectStroke||(this._rectStroke=n.clone()),(this.__dirty||r)&&(t.copy(n),e=i.strokeNoScale?this.getLineScale():1,r=i.lineWidth,this.hasFill()||(i=this.strokeContainThreshold,r=Math.max(r,null==i?4:i)),1e-10<e)&&(t.width+=r/e,t.height+=r/e,t.x-=r/e/2,t.y-=r/e/2),t):n},s.prototype.contain=function(t,e){var n=this.transformCoordToLocal(t,e),i=this.getBoundingRect(),r=this.style;if(t=n[0],e=n[1],i.contain(t,e)){n=this.path;if(this.hasStroke()){i=r.lineWidth,r=r.strokeNoScale?this.getLineScale():1;if(1e-10<r&&(this.hasFill()||(i=Math.max(i,this.strokeContainThreshold)),Ka(n,i/r,!0,t,e)))return!0}if(this.hasFill())return Ka(n,0,!1,t,e)}return!1},s.prototype.dirtyShape=function(){this.__dirty|=Ye,this._rect&&(this._rect=null),this._decalEl&&this._decalEl.dirtyShape(),this.markRedraw()},s.prototype.dirty=function(){this.dirtyStyle(),this.dirtyShape()},s.prototype.animateShape=function(t){return this.animate("shape",t)},s.prototype.updateDuringAnimation=function(t){"style"===t?this.dirtyStyle():"shape"===t?this.dirtyShape():this.markRedraw()},s.prototype.attrKV=function(t,e){"shape"===t?this.setShape(e):$a.prototype.attrKV.call(this,t,e)},s.prototype.setShape=function(t,e){var n=(n=this.shape)||(this.shape={});return"string"==typeof t?n[t]=e:L(n,t),this.dirtyShape(),this},s.prototype.shapeChanged=function(){return!!(this.__dirty&Ye)},s.prototype.createStyle=function(t){return Rt(Qa,t)},s.prototype._innerSaveToNormal=function(t){$a.prototype._innerSaveToNormal.call(this,t);var e=this._normalState;t.shape&&!e.shape&&(e.shape=L({},this.shape))},s.prototype._applyStateObj=function(t,e,n,i,r,o){$a.prototype._applyStateObj.call(this,t,e,n,i,r,o);var a,s=!(e&&i);if(e&&e.shape?r?i?a=e.shape:(a=L({},n.shape),L(a,e.shape)):(a=L({},(i?this:n).shape),L(a,e.shape)):s&&(a=n.shape),a)if(r){this.shape=L({},this.shape);for(var l={},u=ht(a),h=0;h<u.length;h++){var c=u[h];"object"==typeof a[c]?this.shape[c]=a[c]:l[c]=a[c]}this._transitionState(t,{shape:l},o)}else this.shape=a,this.dirtyShape()},s.prototype._mergeStates=function(t){for(var e,n=$a.prototype._mergeStates.call(this,t),i=0;i<t.length;i++){var r=t[i];r.shape&&this._mergeStyle(e=e||{},r.shape)}return e&&(n.shape=e),n},s.prototype.getAnimationStyleProps=function(){return Ja},s.prototype.isZeroArea=function(){return!1},s.extend=function(n){u(r,i=s),r.prototype.getDefaultStyle=function(){return y(n.style)},r.prototype.getDefaultShape=function(){return y(n.shape)};var i,t,e=r;function r(t){var e=i.call(this,t)||this;return n.init&&n.init.call(e,t),e}for(t in n)"function"==typeof n[t]&&(e.prototype[t]=n[t]);return e},s.initDefaultProps=((Jl=s.prototype).type="path",Jl.strokeContainThreshold=5,Jl.segmentIgnoreThreshold=0,Jl.subPixelOptimize=!1,Jl.autoBatch=!1,void(Jl.__dirty=2|Xe|Ye)),s);function s(t){return $a.call(this,t)||this}var es,ns=E({strokeFirst:!0,font:K,x:0,y:0,textAlign:"left",textBaseline:"top",miterLimit:2},Qa),is=(u(rs,es=i),rs.prototype.hasStroke=function(){var t=this.style,e=t.stroke;return null!=e&&"none"!==e&&0<t.lineWidth},rs.prototype.hasFill=function(){var t=this.style.fill;return null!=t&&"none"!==t},rs.prototype.createStyle=function(t){return Rt(ns,t)},rs.prototype.setBoundingRect=function(t){this._rect=t},rs.prototype.getBoundingRect=function(){var t,e=this.style;return this._rect||(null!=(t=e.text)?t+="":t="",(t=dr(t,e.font,e.textAlign,e.textBaseline)).x+=e.x||0,t.y+=e.y||0,this.hasStroke()&&(e=e.lineWidth,t.x-=e/2,t.y-=e/2,t.width+=e,t.height+=e),this._rect=t),this._rect},rs.initDefaultProps=void(rs.prototype.dirtyRectTolerance=10),rs);function rs(){return null!==es&&es.apply(this,arguments)||this}is.prototype.type="tspan";var os=E({x:0,y:0},ra),as={style:E({x:!0,y:!0,width:!0,height:!0,sx:!0,sy:!0,sWidth:!0,sHeight:!0},oa.style)};u(us,ss=i),us.prototype.createStyle=function(t){return Rt(os,t)},us.prototype._getSize=function(t){var e,n=this.style,i=n[t];return null!=i?i:(i=(i=n.image)&&"string"!=typeof i&&i.width&&i.height?n.image:this.__image)?null==(e=n[n="width"===t?"height":"width"])?i[t]:i[t]/i[n]*e:0},us.prototype.getWidth=function(){return this._getSize("width")},us.prototype.getHeight=function(){return this._getSize("height")},us.prototype.getAnimationStyleProps=function(){return as},us.prototype.getBoundingRect=function(){var t=this.style;return this._rect||(this._rect=new X(t.x||0,t.y||0,this.getWidth(),this.getHeight())),this._rect};var ss,ls=us;function us(){return null!==ss&&ss.apply(this,arguments)||this}ls.prototype.type="image";var hs=Math.round;function cs(t,e,n){var i,r,o;if(e)return i=e.x1,r=e.x2,o=e.y1,e=e.y2,t.x1=i,t.x2=r,t.y1=o,t.y2=e,(n=n&&n.lineWidth)&&(hs(2*i)===hs(2*r)&&(t.x1=t.x2=ds(i,n,!0)),hs(2*o)===hs(2*e))&&(t.y1=t.y2=ds(o,n,!0)),t}function ps(t,e,n){var i,r,o;if(e)return i=e.x,r=e.y,o=e.width,e=e.height,t.x=i,t.y=r,t.width=o,t.height=e,(n=n&&n.lineWidth)&&(t.x=ds(i,n,!0),t.y=ds(r,n,!0),t.width=Math.max(ds(i+o,n,!1)-t.x,0===o?0:1),t.height=Math.max(ds(r+e,n,!1)-t.y,0===e?0:1)),t}function ds(t,e,n){var i;return e?((i=hs(2*t))+hs(e))%2==0?i/2:(i+(n?1:-1))/2:t}var fs,gs=function(){this.x=0,this.y=0,this.width=0,this.height=0},ys={},ms=(u(vs,fs=j),vs.prototype.getDefaultShape=function(){return new gs},vs.prototype.buildPath=function(t,e){var n,i,r,o,a,s,l,u,h,c,p,d,f,g;this.subPixelOptimize?(n=(a=ps(ys,e,this.style)).x,i=a.y,r=a.width,o=a.height,a.r=e.r,e=a):(n=e.x,i=e.y,r=e.width,o=e.height),e.r?(a=t,p=(e=e).x,d=e.y,f=e.width,g=e.height,e=e.r,f<0&&(p+=f,f=-f),g<0&&(d+=g,g=-g),"number"==typeof e?s=l=u=h=e:e instanceof Array?1===e.length?s=l=u=h=e[0]:2===e.length?(s=u=e[0],l=h=e[1]):3===e.length?(s=e[0],l=h=e[1],u=e[2]):(s=e[0],l=e[1],u=e[2],h=e[3]):s=l=u=h=0,f<s+l&&(s*=f/(c=s+l),l*=f/c),f<u+h&&(u*=f/(c=u+h),h*=f/c),g<l+u&&(l*=g/(c=l+u),u*=g/c),g<s+h&&(s*=g/(c=s+h),h*=g/c),a.moveTo(p+s,d),a.lineTo(p+f-l,d),0!==l&&a.arc(p+f-l,d+l,l,-Math.PI/2,0),a.lineTo(p+f,d+g-u),0!==u&&a.arc(p+f-u,d+g-u,u,0,Math.PI/2),a.lineTo(p+h,d+g),0!==h&&a.arc(p+h,d+g-h,h,Math.PI/2,Math.PI),a.lineTo(p,d+s),0!==s&&a.arc(p+s,d+s,s,Math.PI,1.5*Math.PI)):t.rect(n,i,r,o)},vs.prototype.isZeroArea=function(){return!this.shape.width||!this.shape.height},vs);function vs(t){return fs.call(this,t)||this}ms.prototype.type="rect";var _s,xs={fill:"#000"},ws={style:E({fill:!0,stroke:!0,fillOpacity:!0,strokeOpacity:!0,lineWidth:!0,fontSize:!0,lineHeight:!0,width:!0,height:!0,textShadowColor:!0,textShadowBlur:!0,textShadowOffsetX:!0,textShadowOffsetY:!0,backgroundColor:!0,padding:!0,borderColor:!0,borderWidth:!0,borderRadius:!0},oa.style)},bs=(u(Ss,_s=i),Ss.prototype.childrenRef=function(){return this._children},Ss.prototype.update=function(){_s.prototype.update.call(this),this.styleChanged()&&this._updateSubTexts();for(var t=0;t<this._children.length;t++){var e=this._children[t];e.zlevel=this.zlevel,e.z=this.z,e.z2=this.z2,e.culling=this.culling,e.cursor=this.cursor,e.invisible=this.invisible}},Ss.prototype.updateTransform=function(){var t=this.innerTransformable;t?(t.updateTransform(),t.transform&&(this.transform=t.transform)):_s.prototype.updateTransform.call(this)},Ss.prototype.getLocalTransform=function(t){var e=this.innerTransformable;return e?e.getLocalTransform(t):_s.prototype.getLocalTransform.call(this,t)},Ss.prototype.getComputedTransform=function(){return this.__hostTarget&&(this.__hostTarget.getComputedTransform(),this.__hostTarget.updateInnerText(!0)),_s.prototype.getComputedTransform.call(this)},Ss.prototype._updateSubTexts=function(){var t;this._childCursor=0,ks(t=this.style),O(t.rich,ks),this.style.rich?this._updateRichTexts():this._updatePlainTexts(),this._children.length=this._childCursor,this.styleUpdated()},Ss.prototype.addSelfToZr=function(t){_s.prototype.addSelfToZr.call(this,t);for(var e=0;e<this._children.length;e++)this._children[e].__zr=t},Ss.prototype.removeSelfFromZr=function(t){_s.prototype.removeSelfFromZr.call(this,t);for(var e=0;e<this._children.length;e++)this._children[e].__zr=null},Ss.prototype.getBoundingRect=function(){if(this.styleChanged()&&this._updateSubTexts(),!this._rect){for(var t=new X(0,0,0,0),e=this._children,n=[],i=null,r=0;r<e.length;r++){var o=e[r],a=o.getBoundingRect(),o=o.getLocalTransform(n);o?(t.copy(a),t.applyTransform(o),(i=i||t.clone()).union(t)):(i=i||a.clone()).union(a)}this._rect=i||t}return this._rect},Ss.prototype.setDefaultTextStyle=function(t){this._defaultStyle=t||xs},Ss.prototype.setTextContent=function(t){},Ss.prototype._mergeStyle=function(t,e){var n,i;return e&&(n=e.rich,i=t.rich||n&&{},L(t,e),n&&i?(this._mergeRich(i,n),t.rich=i):i&&(t.rich=i)),t},Ss.prototype._mergeRich=function(t,e){for(var n=ht(e),i=0;i<n.length;i++){var r=n[i];t[r]=t[r]||{},L(t[r],e[r])}},Ss.prototype.getAnimationStyleProps=function(){return ws},Ss.prototype._getOrCreateChild=function(t){var e=this._children[this._childCursor];return e&&e instanceof t||(e=new t),(this._children[this._childCursor++]=e).__zr=this.__zr,e.parent=this,e},Ss.prototype._updatePlainTexts=function(){for(var t,e=this.style,n=e.font||K,i=e.padding,r=function(t,e){null!=t&&(t+="");var n,i=e.overflow,r=e.padding,o=e.font,a="truncate"===i,s=yr(o),l=N(e.lineHeight,s),u=!!e.backgroundColor,h="truncate"===e.lineOverflow,c=e.width,i=(n=null==c||"break"!==i&&"breakAll"!==i?t?t.split("\n"):[]:t?ea(t,e.font,c,"breakAll"===i,0).lines:[]).length*l,p=N(e.height,i);if(p<i&&h&&(h=Math.floor(p/l),n=n.slice(0,h)),t&&a&&null!=c)for(var d=qo(c,o,e.ellipsis,{minChar:e.truncateMinChar,placeholder:e.placeholder}),f=0;f<n.length;f++)n[f]=Zo(n[f],d);for(var h=p,g=0,f=0;f<n.length;f++)g=Math.max(cr(n[f],o),g);return null==c&&(c=g),t=g,r&&(h+=r[0]+r[2],t+=r[1]+r[3],c+=r[1]+r[3]),{lines:n,height:p,outerWidth:t=u?c:t,outerHeight:h,lineHeight:l,calculatedLineHeight:s,contentWidth:g,contentHeight:i,width:c}}(Ls(e),e),o=Os(e),a=!!e.backgroundColor,s=r.outerHeight,l=r.outerWidth,u=r.contentWidth,h=r.lines,c=r.lineHeight,p=this._defaultStyle,d=e.x||0,f=e.y||0,g=e.align||p.align||"left",y=e.verticalAlign||p.verticalAlign||"top",m=d,v=gr(f,r.contentHeight,y),_=((o||i)&&(t=fr(d,l,g),f=gr(f,s,y),o)&&this._renderBackground(e,e,t,f,l,s),v+=c/2,i&&(m=Ps(d,g,i),"top"===y?v+=i[0]:"bottom"===y&&(v-=i[2])),0),o=!1,x=(As(("fill"in e?e:(o=!0,p)).fill)),w=(Ds("stroke"in e?e.stroke:a||p.autoStroke&&!o?null:(_=2,p.stroke))),b=0<e.textShadowBlur,S=null!=e.width&&("truncate"===e.overflow||"break"===e.overflow||"breakAll"===e.overflow),M=r.calculatedLineHeight,T=0;T<h.length;T++){var C=this._getOrCreateChild(is),I=C.createStyle();C.useStyle(I),I.text=h[T],I.x=m,I.y=v,g&&(I.textAlign=g),I.textBaseline="middle",I.opacity=e.opacity,I.strokeFirst=!0,b&&(I.shadowBlur=e.textShadowBlur||0,I.shadowColor=e.textShadowColor||"transparent",I.shadowOffsetX=e.textShadowOffsetX||0,I.shadowOffsetY=e.textShadowOffsetY||0),I.stroke=w,I.fill=x,w&&(I.lineWidth=e.lineWidth||_,I.lineDash=e.lineDash,I.lineDashOffset=e.lineDashOffset||0),I.font=n,Is(I,e),v+=c,S&&C.setBoundingRect(new X(fr(I.x,e.width,I.textAlign),gr(I.y,M,I.textBaseline),u,M))}},Ss.prototype._updateRichTexts=function(){for(var t=this.style,e=Qo(Ls(t),t),n=e.width,i=e.outerWidth,r=e.outerHeight,o=t.padding,a=t.x||0,s=t.y||0,l=this._defaultStyle,u=t.align||l.align,l=t.verticalAlign||l.verticalAlign,a=fr(a,i,u),u=gr(s,r,l),h=a,c=u,p=(o&&(h+=o[3],c+=o[0]),h+n),d=(Os(t)&&this._renderBackground(t,t,a,u,i,r),!!t.backgroundColor),f=0;f<e.lines.length;f++){for(var g=e.lines[f],y=g.tokens,m=y.length,v=g.lineHeight,_=g.width,x=0,w=h,b=p,S=m-1,M=void 0;x<m&&(!(M=y[x]).align||"left"===M.align);)this._placeToken(M,t,v,c,w,"left",d),_-=M.width,w+=M.width,x++;for(;0<=S&&"right"===(M=y[S]).align;)this._placeToken(M,t,v,c,b,"right",d),_-=M.width,b-=M.width,S--;for(w+=(n-(w-h)-(p-b)-_)/2;x<=S;)M=y[x],this._placeToken(M,t,v,c,w+M.width/2,"center",d),w+=M.width,x++;c+=v}},Ss.prototype._placeToken=function(t,e,n,i,r,o,a){var s=e.rich[t.styleName]||{},l=(s.text=t.text,t.verticalAlign),u=i+n/2;"top"===l?u=i+t.height/2:"bottom"===l&&(u=i+n-t.height/2);!t.isLineHolder&&Os(s)&&this._renderBackground(s,e,"right"===o?r-t.width:"center"===o?r-t.width/2:r,u-t.height/2,t.width,t.height);var l=!!s.backgroundColor,i=t.textPadding,n=(i&&(r=Ps(r,o,i),u-=t.height/2-i[0]-t.innerHeight/2),this._getOrCreateChild(is)),i=n.createStyle(),h=(n.useStyle(i),this._defaultStyle),c=!1,p=0,d=As(("fill"in s?s:"fill"in e?e:(c=!0,h)).fill),l=Ds("stroke"in s?s.stroke:"stroke"in e?e.stroke:l||a||h.autoStroke&&!c?null:(p=2,h.stroke)),a=0<s.textShadowBlur||0<e.textShadowBlur,c=(i.text=t.text,i.x=r,i.y=u,a&&(i.shadowBlur=s.textShadowBlur||e.textShadowBlur||0,i.shadowColor=s.textShadowColor||e.textShadowColor||"transparent",i.shadowOffsetX=s.textShadowOffsetX||e.textShadowOffsetX||0,i.shadowOffsetY=s.textShadowOffsetY||e.textShadowOffsetY||0),i.textAlign=o,i.textBaseline="middle",i.font=t.font||K,i.opacity=St(s.opacity,e.opacity,1),Is(i,s),l&&(i.lineWidth=St(s.lineWidth,e.lineWidth,p),i.lineDash=N(s.lineDash,e.lineDash),i.lineDashOffset=e.lineDashOffset||0,i.stroke=l),d&&(i.fill=d),t.contentWidth),h=t.contentHeight;n.setBoundingRect(new X(fr(i.x,c,i.textAlign),gr(i.y,h,i.textBaseline),c,h))},Ss.prototype._renderBackground=function(t,e,n,i,r,o){var a,s,l,u,h=t.backgroundColor,c=t.borderWidth,p=t.borderColor,d=h&&h.image,f=h&&!d,g=t.borderRadius,y=this,g=((f||t.lineHeight||c&&p)&&((a=this._getOrCreateChild(ms)).useStyle(a.createStyle()),a.style.fill=null,(l=a.shape).x=n,l.y=i,l.width=r,l.height=o,l.r=g,a.dirtyShape()),f?((u=a.style).fill=h||null,u.fillOpacity=N(t.fillOpacity,1)):d&&((s=this._getOrCreateChild(ls)).onload=function(){y.dirtyStyle()},(l=s.style).image=h.image,l.x=n,l.y=i,l.width=r,l.height=o),c&&p&&((u=a.style).lineWidth=c,u.stroke=p,u.strokeOpacity=N(t.strokeOpacity,1),u.lineDash=t.borderDash,u.lineDashOffset=t.borderDashOffset||0,a.strokeContainThreshold=0,a.hasFill())&&a.hasStroke()&&(u.strokeFirst=!0,u.lineWidth*=2),(a||s).style);g.shadowBlur=t.shadowBlur||0,g.shadowColor=t.shadowColor||"transparent",g.shadowOffsetX=t.shadowOffsetX||0,g.shadowOffsetY=t.shadowOffsetY||0,g.opacity=St(t.opacity,e.opacity,1)},Ss.makeFont=function(t){var e,n="";return(n=null!=(e=t).fontSize||e.fontFamily||e.fontWeight?[t.fontStyle,t.fontWeight,"string"!=typeof(e=t.fontSize)||-1===e.indexOf("px")&&-1===e.indexOf("rem")&&-1===e.indexOf("em")?isNaN(+e)?"12px":e+"px":e,t.fontFamily||"sans-serif"].join(" "):n)&&It(n)||t.textFont||t.font},Ss);function Ss(t){var e=_s.call(this)||this;return e.type="text",e._children=[],e._defaultStyle=xs,e.attr(t),e}var Ms={left:!0,right:1,center:1},Ts={top:1,bottom:1,middle:1},Cs=["fontStyle","fontWeight","fontSize","fontFamily"];function Is(t,e){for(var n=0;n<Cs.length;n++){var i=Cs[n],r=e[i];null!=r&&(t[i]=r)}}function ks(t){var e;t&&(t.font=bs.makeFont(t),e=t.align,t.align=null==(e="middle"===e?"center":e)||Ms[e]?e:"left",e=t.verticalAlign,t.verticalAlign=null==(e="center"===e?"middle":e)||Ts[e]?e:"top",t.padding)&&(t.padding=Tt(t.padding))}function Ds(t,e){return null==t||e<=0||"transparent"===t||"none"===t?null:t.image||t.colorStops?"#000":t}function As(t){return null==t||"none"===t?null:t.image||t.colorStops?"#000":t}function Ps(t,e,n){return"right"===e?t-n[1]:"center"===e?t+n[3]/2-n[1]/2:t+n[3]}function Ls(t){t=t.text;return null!=t&&(t+=""),t}function Os(t){return!!(t.backgroundColor||t.lineHeight||t.borderWidth&&t.borderColor)}var k=_o(),Rs=1,Ns={},zs=_o(),Es=_o(),Bs=0,Fs=1,Vs=2,Hs=["emphasis","blur","select"],Gs=["normal","emphasis","blur","select"],Ws="highlight",Us="downplay",Xs="select",Ys="unselect",qs="toggleSelect";function Zs(t){return null!=t&&"none"!==t}var js=new Dn(100);function Ks(t){var e;return V(t)?((e=js.get(t))||(e=Xn(t,-.1),js.put(t,e)),e):vt(t)?((e=L({},t)).colorStops=B(t.colorStops,function(t){return{offset:t.offset,color:Xn(t.color,-.1)}}),e):t}function $s(t,e,n){t.onHoverStateChange&&(t.hoverState||0)!==n&&t.onHoverStateChange(e),t.hoverState=n}function Qs(t){$s(t,"emphasis",Vs)}function Js(t){t.hoverState===Vs&&$s(t,"normal",Bs)}function tl(t){$s(t,"blur",Fs)}function el(t){t.hoverState===Fs&&$s(t,"normal",Bs)}function nl(t){t.selected=!0}function il(t){t.selected=!1}function rl(t,e,n){e(t,n)}function ol(t,e,n){rl(t,e,n),t.isGroup&&t.traverse(function(t){rl(t,e,n)})}function al(t,e){switch(e){case"emphasis":t.hoverState=Vs;break;case"normal":t.hoverState=Bs;break;case"blur":t.hoverState=Fs;break;case"select":t.selected=!0}}function sl(t,e,n){var i=0<=I(t.currentStates,e),r=t.style.opacity,t=i?null:function(t,e,n,i){for(var r=t.style,o={},a=0;a<e.length;a++){var s=e[a],l=r[s];o[s]=null==l?i&&i[s]:l}for(a=0;a<t.animators.length;a++){var u=t.animators[a];u.__fromStateTransition&&u.__fromStateTransition.indexOf(n)<0&&"style"===u.targetName&&u.saveTo(o,e)}return o}(t,["opacity"],e,{opacity:1}),e=(n=n||{}).style||{};return null==e.opacity&&(n=L({},n),e=L({opacity:i?r:.1*t.opacity},e),n.style=e),n}function ll(t,e){var n,i,r,o,a,s=this.states[t];if(this.style){if("emphasis"===t)return n=this,i=s,e=(e=e)&&0<=I(e,"select"),a=!1,n instanceof j&&(r=zs(n),o=e&&r.selectFill||r.normalFill,e=e&&r.selectStroke||r.normalStroke,Zs(o)||Zs(e))&&("inherit"===(r=(i=i||{}).style||{}).fill?(a=!0,i=L({},i),(r=L({},r)).fill=o):!Zs(r.fill)&&Zs(o)?(a=!0,i=L({},i),(r=L({},r)).fill=Ks(o)):!Zs(r.stroke)&&Zs(e)&&(a||(i=L({},i),r=L({},r)),r.stroke=Ks(e)),i.style=r),i&&null==i.z2&&(a||(i=L({},i)),o=n.z2EmphasisLift,i.z2=n.z2+(null!=o?o:10)),i;if("blur"===t)return sl(this,t,s);if("select"===t)return e=this,(r=s)&&null==r.z2&&(r=L({},r),a=e.z2SelectLift,r.z2=e.z2+(null!=a?a:9)),r}return s}function ul(t){t.stateProxy=ll;var e=t.getTextContent(),t=t.getTextGuideLine();e&&(e.stateProxy=ll),t&&(t.stateProxy=ll)}function hl(t,e){ml(t,e)||t.__highByOuter||ol(t,Qs)}function cl(t,e){ml(t,e)||t.__highByOuter||ol(t,Js)}function pl(t,e){t.__highByOuter|=1<<(e||0),ol(t,Qs)}function dl(t,e){(t.__highByOuter&=~(1<<(e||0)))||ol(t,Js)}function fl(t){ol(t,el)}function gl(t){ol(t,nl)}function yl(t){ol(t,il)}function ml(t,e){return t.__highDownSilentOnTouch&&e.zrByTouch}function vl(i){i.getModel().eachComponent(function(t,e){var n=Es(e);n.isBlured&&("series"===t?i.getViewOfSeriesModel(e):i.getViewOfComponentModel(e)).group.traverse(function(t){el(t)}),n.isBlured=!1})}function _l(t,o,a,s){var l,u,h,n=s.getModel();function c(t,e){for(var n=0;n<e.length;n++){var i=t.getItemGraphicEl(e[n]);i&&fl(i)}}a=a||"coordinateSystem",null!=t&&o&&"none"!==o&&(l=n.getSeriesByIndex(t),(u=l.coordinateSystem)&&u.master&&(u=u.master),h=[],n.eachSeries(function(t){var e=l===t,n=t.coordinateSystem,n=(n=n&&n.master?n.master:n)&&u?n===u:e;if(!("series"===a&&!e||"coordinateSystem"===a&&!n||"series"===o&&e)){if(s.getViewOfSeriesModel(t).group.traverse(function(t){tl(t)}),st(o))c(t.getData(),o);else if(R(o))for(var i=ht(o),r=0;r<i.length;r++)c(t.getData(i[r]),o[i[r]]);h.push(t),Es(t).isBlured=!0}}),n.eachComponent(function(t,e){"series"!==t&&(t=s.getViewOfComponentModel(e))&&t.blurSeries&&t.blurSeries(h,n)}))}function xl(t,e,n){var i;null!=t&&null!=e&&(t=n.getModel().getComponent(t,e))&&(Es(t).isBlured=!0,i=n.getViewOfComponentModel(t))&&i.focusBlurEnabled&&i.group.traverse(function(t){tl(t)})}function wl(t,e,n,i){var r={focusSelf:!1,dispatchers:null};if(null==t||"series"===t||null==e||null==n)return r;t=i.getModel().getComponent(t,e);if(!t)return r;e=i.getViewOfComponentModel(t);if(!e||!e.findHighDownDispatchers)return r;for(var o,a=e.findHighDownDispatchers(n),s=0;s<a.length;s++)if("self"===k(a[s]).focus){o=!0;break}return{focusSelf:o,dispatchers:a}}function bl(i){O(i.getAllData(),function(t){var e=t.data,n=t.type;e.eachItemGraphicEl(function(t,e){(i.isSelected(e,n)?gl:yl)(t)})})}function Sl(t,e,n){kl(t,!0),ol(t,ul);t=k(t),null!=e?(t.focus=e,t.blurScope=n):t.focus&&(t.focus=null)}function Ml(t,e,n,i){i?kl(t,!1):Sl(t,e,n)}var Tl=["emphasis","blur","select"],Cl={itemStyle:"getItemStyle",lineStyle:"getLineStyle",areaStyle:"getAreaStyle"};function Il(t,e,n,i){n=n||"itemStyle";for(var r=0;r<Tl.length;r++){var o=Tl[r],a=e.getModel([o,n]);t.ensureState(o).style=i?i(a):a[Cl[n]]()}}function kl(t,e){var e=!1===e,n=t;t.highDownSilentOnTouch&&(n.__highDownSilentOnTouch=t.highDownSilentOnTouch),e&&!n.__highDownDispatcher||(n.__highByOuter=n.__highByOuter||0,n.__highDownDispatcher=!e)}function Dl(t){return!(!t||!t.__highDownDispatcher)}function Al(t){t=t.type;return t===Xs||t===Ys||t===qs}function Pl(t){t=t.type;return t===Ws||t===Us}var Ll=Ea.CMD,Ol=[[],[],[]],Rl=Math.sqrt,Nl=Math.atan2;var zl=Math.sqrt,El=Math.sin,Bl=Math.cos,Fl=Math.PI;function Vl(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])}function Hl(t,e){return(t[0]*e[0]+t[1]*e[1])/(Vl(t)*Vl(e))}function Gl(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(Hl(t,e))}function Wl(t,e,n,i,r,o,a,s,l,u,h){var l=l*(Fl/180),c=Bl(l)*(t-n)/2+El(l)*(e-i)/2,p=-1*El(l)*(t-n)/2+Bl(l)*(e-i)/2,d=c*c/(a*a)+p*p/(s*s),d=(1<d&&(a*=zl(d),s*=zl(d)),(r===o?-1:1)*zl((a*a*(s*s)-a*a*(p*p)-s*s*(c*c))/(a*a*(p*p)+s*s*(c*c)))||0),r=d*a*p/s,d=d*-s*c/a,t=(t+n)/2+Bl(l)*r-El(l)*d,n=(e+i)/2+El(l)*r+Bl(l)*d,e=Gl([1,0],[(c-r)/a,(p-d)/s]),i=[(c-r)/a,(p-d)/s],c=[(-1*c-r)/a,(-1*p-d)/s],r=Gl(i,c);Hl(i,c)<=-1&&(r=Fl),(r=1<=Hl(i,c)?0:r)<0&&(p=Math.round(r/Fl*1e6)/1e6,r=2*Fl+p%2*Fl),h.addData(u,t,n,a,s,e,r,l,o)}var Ul=/([mlvhzcqtsa])([^mlvhzcqtsa]*)/gi,Xl=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;u(Zl,Yl=j),Zl.prototype.applyTransform=function(t){};var Yl,ql=Zl;function Zl(){return null!==Yl&&Yl.apply(this,arguments)||this}function jl(t){return null!=t.setData}function Kl(t,e){var S=function(t){var e=new Ea;if(t){var n,i=0,r=0,o=i,a=r,s=Ea.CMD,l=t.match(Ul);if(l){for(var u=0;u<l.length;u++){for(var h=l[u],c=h.charAt(0),p=void 0,d=h.match(Xl)||[],f=d.length,g=0;g<f;g++)d[g]=parseFloat(d[g]);for(var y=0;y<f;){var m=void 0,v=void 0,_=void 0,x=void 0,w=void 0,b=void 0,S=void 0,M=i,T=r,C=void 0,I=void 0;switch(c){case"l":i+=d[y++],r+=d[y++],p=s.L,e.addData(p,i,r);break;case"L":i=d[y++],r=d[y++],p=s.L,e.addData(p,i,r);break;case"m":i+=d[y++],r+=d[y++],p=s.M,e.addData(p,i,r),o=i,a=r,c="l";break;case"M":i=d[y++],r=d[y++],p=s.M,e.addData(p,i,r),o=i,a=r,c="L";break;case"h":i+=d[y++],p=s.L,e.addData(p,i,r);break;case"H":i=d[y++],p=s.L,e.addData(p,i,r);break;case"v":r+=d[y++],p=s.L,e.addData(p,i,r);break;case"V":r=d[y++],p=s.L,e.addData(p,i,r);break;case"C":p=s.C,e.addData(p,d[y++],d[y++],d[y++],d[y++],d[y++],d[y++]),i=d[y-2],r=d[y-1];break;case"c":p=s.C,e.addData(p,d[y++]+i,d[y++]+r,d[y++]+i,d[y++]+r,d[y++]+i,d[y++]+r),i+=d[y-2],r+=d[y-1];break;case"S":m=i,v=r,C=e.len(),I=e.data,n===s.C&&(m+=i-I[C-4],v+=r-I[C-3]),p=s.C,M=d[y++],T=d[y++],i=d[y++],r=d[y++],e.addData(p,m,v,M,T,i,r);break;case"s":m=i,v=r,C=e.len(),I=e.data,n===s.C&&(m+=i-I[C-4],v+=r-I[C-3]),p=s.C,M=i+d[y++],T=r+d[y++],i+=d[y++],r+=d[y++],e.addData(p,m,v,M,T,i,r);break;case"Q":M=d[y++],T=d[y++],i=d[y++],r=d[y++],p=s.Q,e.addData(p,M,T,i,r);break;case"q":M=d[y++]+i,T=d[y++]+r,i+=d[y++],r+=d[y++],p=s.Q,e.addData(p,M,T,i,r);break;case"T":m=i,v=r,C=e.len(),I=e.data,n===s.Q&&(m+=i-I[C-4],v+=r-I[C-3]),i=d[y++],r=d[y++],p=s.Q,e.addData(p,m,v,i,r);break;case"t":m=i,v=r,C=e.len(),I=e.data,n===s.Q&&(m+=i-I[C-4],v+=r-I[C-3]),i+=d[y++],r+=d[y++],p=s.Q,e.addData(p,m,v,i,r);break;case"A":_=d[y++],x=d[y++],w=d[y++],b=d[y++],S=d[y++],Wl(M=i,T=r,i=d[y++],r=d[y++],b,S,_,x,w,p=s.A,e);break;case"a":_=d[y++],x=d[y++],w=d[y++],b=d[y++],S=d[y++],Wl(M=i,T=r,i+=d[y++],r+=d[y++],b,S,_,x,w,p=s.A,e)}}"z"!==c&&"Z"!==c||(p=s.Z,e.addData(p),i=o,r=a),n=p}e.toStatic()}}return e}(t),t=L({},e);return t.buildPath=function(t){var e;jl(t)?(t.setData(S.data),(e=t.getContext())&&t.rebuildPath(e,1)):S.rebuildPath(e=t,1)},t.applyTransform=function(t){var e=S,n=t;if(n){for(var i,r,o,a,s=e.data,l=e.len(),u=Ll.M,h=Ll.C,c=Ll.L,p=Ll.R,d=Ll.A,f=Ll.Q,g=0,y=0;g<l;){switch(i=s[g++],y=g,r=0,i){case u:case c:r=1;break;case h:r=3;break;case f:r=2;break;case d:var m=n[4],v=n[5],_=Rl(n[0]*n[0]+n[1]*n[1]),x=Rl(n[2]*n[2]+n[3]*n[3]),w=Nl(-n[1]/x,n[0]/_);s[g]*=_,s[g++]+=m,s[g]*=x,s[g++]+=v,s[g++]*=_,s[g++]*=x,s[g++]+=w,s[g++]+=w,y=g+=2;break;case p:a[0]=s[g++],a[1]=s[g++],Jt(a,a,n),s[y++]=a[0],s[y++]=a[1],a[0]+=s[g++],a[1]+=s[g++],Jt(a,a,n),s[y++]=a[0],s[y++]=a[1]}for(o=0;o<r;o++){var b=Ol[o];b[0]=s[g++],b[1]=s[g++],Jt(b,b,n),s[y++]=b[0],s[y++]=b[1]}}e.increaseVersion()}this.dirtyShape()},t}var $l,Ql=function(){this.cx=0,this.cy=0,this.r=0},Jl=(u(tu,$l=j),tu.prototype.getDefaultShape=function(){return new Ql},tu.prototype.buildPath=function(t,e){t.moveTo(e.cx+e.r,e.cy),t.arc(e.cx,e.cy,e.r,0,2*Math.PI)},tu);function tu(t){return $l.call(this,t)||this}Jl.prototype.type="circle";var eu,nu=function(){this.cx=0,this.cy=0,this.rx=0,this.ry=0},iu=(u(ru,eu=j),ru.prototype.getDefaultShape=function(){return new nu},ru.prototype.buildPath=function(t,e){var n=e.cx,i=e.cy,r=e.rx,e=e.ry,o=.5522848*r,a=.5522848*e;t.moveTo(n-r,i),t.bezierCurveTo(n-r,i-a,n-o,i-e,n,i-e),t.bezierCurveTo(n+o,i-e,n+r,i-a,n+r,i),t.bezierCurveTo(n+r,i+a,n+o,i+e,n,i+e),t.bezierCurveTo(n-o,i+e,n-r,i+a,n-r,i),t.closePath()},ru);function ru(t){return eu.call(this,t)||this}iu.prototype.type="ellipse";var ou=Math.PI,au=2*ou,su=Math.sin,lu=Math.cos,uu=Math.acos,hu=Math.atan2,cu=Math.abs,pu=Math.sqrt,du=Math.max,fu=Math.min,gu=1e-4;function yu(t,e,n,i,r,o,a){var s=t-n,l=e-i,a=(a?o:-o)/pu(s*s+l*l),l=a*l,a=-a*s,s=t+l,t=e+a,e=n+l,n=i+a,i=(s+e)/2,u=(t+n)/2,h=e-s,c=n-t,p=h*h+c*c,o=r-o,s=s*n-e*t,n=(c<0?-1:1)*pu(du(0,o*o*p-s*s)),e=(s*c-h*n)/p,t=(-s*h-c*n)/p,d=(s*c+h*n)/p,s=(-s*h+c*n)/p,h=e-i,c=t-u,n=d-i,p=s-u;return n*n+p*p<h*h+c*c&&(e=d,t=s),{cx:e,cy:t,x0:-l,y0:-a,x1:e*(r/o-1),y1:t*(r/o-1)}}function mu(t,e){var n,i,r,o,a,s,l,u,h,c,p,d,f,g,y,m,v,_,x,w,b,S,M,T,C,I,k,D,A,P,L=du(e.r,0),O=du(e.r0||0,0),R=0<L;(R||0<O)&&(R||(L=O,O=0),L<O&&(R=L,L=O,O=R),R=e.startAngle,n=e.endAngle,isNaN(R)||isNaN(n)||(i=e.cx,r=e.cy,o=!!e.clockwise,m=cu(n-R),gu<(a=au<m&&m%au)&&(m=a),gu<L?au-gu<m?(t.moveTo(i+L*lu(R),r+L*su(R)),t.arc(i,r,L,R,n,!o),gu<O&&(t.moveTo(i+O*lu(n),r+O*su(n)),t.arc(i,r,O,n,R,o))):(S=b=w=x=_=v=c=h=I=C=T=M=u=l=s=a=void 0,p=L*lu(R),d=L*su(R),f=O*lu(n),g=O*su(n),(y=gu<m)&&((e=e.cornerRadius)&&(a=(e=function(t){if(F(t)){var e=t.length;if(!e)return t;e=1===e?[t[0],t[0],0,0]:2===e?[t[0],t[0],t[1],t[1]]:3===e?t.concat(t[2]):t}else e=[t,t,t,t];return e}(e))[0],s=e[1],l=e[2],u=e[3]),e=cu(L-O)/2,M=fu(e,l),T=fu(e,u),C=fu(e,a),I=fu(e,s),v=h=du(M,T),_=c=du(C,I),gu<h||gu<c)&&(x=L*lu(n),w=L*su(n),b=O*lu(R),S=O*su(R),m<ou)&&(e=function(t,e,n,i,r,o,a,s){var l=(s=s-o)*(n=n-t)-(a=a-r)*(i=i-e);if(!(l*l<gu))return[t+(l=(a*(e-o)-s*(t-r))/l)*n,e+l*i]}(p,d,b,S,x,w,f,g))&&(M=p-e[0],T=d-e[1],C=x-e[0],I=w-e[1],m=1/su(uu((M*C+T*I)/(pu(M*M+T*T)*pu(C*C+I*I)))/2),M=pu(e[0]*e[0]+e[1]*e[1]),v=fu(h,(L-M)/(1+m)),_=fu(c,(O-M)/(m-1))),y?gu<v?(k=fu(l,v),D=fu(u,v),A=yu(b,S,p,d,L,k,o),P=yu(x,w,f,g,L,D,o),t.moveTo(i+A.cx+A.x0,r+A.cy+A.y0),v<h&&k===D?t.arc(i+A.cx,r+A.cy,v,hu(A.y0,A.x0),hu(P.y0,P.x0),!o):(0<k&&t.arc(i+A.cx,r+A.cy,k,hu(A.y0,A.x0),hu(A.y1,A.x1),!o),t.arc(i,r,L,hu(A.cy+A.y1,A.cx+A.x1),hu(P.cy+P.y1,P.cx+P.x1),!o),0<D&&t.arc(i+P.cx,r+P.cy,D,hu(P.y1,P.x1),hu(P.y0,P.x0),!o))):(t.moveTo(i+p,r+d),t.arc(i,r,L,R,n,!o)):t.moveTo(i+p,r+d),gu<O&&y?gu<_?(k=fu(a,_),A=yu(f,g,x,w,O,-(D=fu(s,_)),o),P=yu(p,d,b,S,O,-k,o),t.lineTo(i+A.cx+A.x0,r+A.cy+A.y0),_<c&&k===D?t.arc(i+A.cx,r+A.cy,_,hu(A.y0,A.x0),hu(P.y0,P.x0),!o):(0<D&&t.arc(i+A.cx,r+A.cy,D,hu(A.y0,A.x0),hu(A.y1,A.x1),!o),t.arc(i,r,O,hu(A.cy+A.y1,A.cx+A.x1),hu(P.cy+P.y1,P.cx+P.x1),o),0<k&&t.arc(i+P.cx,r+P.cy,k,hu(P.y1,P.x1),hu(P.y0,P.x0),!o))):(t.lineTo(i+f,r+g),t.arc(i,r,O,n,R,o)):t.lineTo(i+f,r+g)):t.moveTo(i,r),t.closePath()))}var vu,_u=function(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0,this.cornerRadius=0},xu=(u(wu,vu=j),wu.prototype.getDefaultShape=function(){return new _u},wu.prototype.buildPath=function(t,e){mu(t,e)},wu.prototype.isZeroArea=function(){return this.shape.startAngle===this.shape.endAngle||this.shape.r===this.shape.r0},wu);function wu(t){return vu.call(this,t)||this}xu.prototype.type="sector";var bu,Su=function(){this.cx=0,this.cy=0,this.r=0,this.r0=0},Mu=(u(Tu,bu=j),Tu.prototype.getDefaultShape=function(){return new Su},Tu.prototype.buildPath=function(t,e){var n=e.cx,i=e.cy,r=2*Math.PI;t.moveTo(n+e.r,i),t.arc(n,i,e.r,0,r,!1),t.moveTo(n+e.r0,i),t.arc(n,i,e.r0,0,r,!0)},Tu);function Tu(t){return bu.call(this,t)||this}function Cu(t,e,n){var i=e.smooth,r=e.points;if(r&&2<=r.length){if(i)for(var o=function(t,e,n,i){var r,o,a=[],s=[],l=[],u=[];if(i){for(var h=[1/0,1/0],c=[-1/0,-1/0],p=0,d=t.length;p<d;p++)te(h,h,t[p]),ee(c,c,t[p]);te(h,h,i[0]),ee(c,c,i[1])}for(p=0,d=t.length;p<d;p++){var f=t[p];if(n)r=t[p?p-1:d-1],o=t[(p+1)%d];else{if(0===p||p===d-1){a.push(Ht(t[p]));continue}r=t[p-1],o=t[p+1]}Wt(s,o,r),Yt(s,s,e);var g=Zt(f,r),y=Zt(f,o),m=g+y,m=(0!==m&&(g/=m,y/=m),Yt(l,s,-g),Yt(u,s,y),Gt([],f,l)),g=Gt([],f,u);i&&(ee(m,m,h),te(m,m,c),ee(g,g,h),te(g,g,c)),a.push(m),a.push(g)}return n&&a.push(a.shift()),a}(r,i,n,e.smoothConstraint),a=(t.moveTo(r[0][0],r[0][1]),r.length),s=0;s<(n?a:a-1);s++){var l=o[2*s],u=o[2*s+1],h=r[(s+1)%a];t.bezierCurveTo(l[0],l[1],u[0],u[1],h[0],h[1])}else{t.moveTo(r[0][0],r[0][1]);for(var s=1,c=r.length;s<c;s++)t.lineTo(r[s][0],r[s][1])}n&&t.closePath()}}Mu.prototype.type="ring";var Iu,ku=function(){this.points=null,this.smooth=0,this.smoothConstraint=null},Du=(u(Au,Iu=j),Au.prototype.getDefaultShape=function(){return new ku},Au.prototype.buildPath=function(t,e){Cu(t,e,!0)},Au);function Au(t){return Iu.call(this,t)||this}Du.prototype.type="polygon";var Pu,Lu=function(){this.points=null,this.percent=1,this.smooth=0,this.smoothConstraint=null},Ou=(u(Ru,Pu=j),Ru.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},Ru.prototype.getDefaultShape=function(){return new Lu},Ru.prototype.buildPath=function(t,e){Cu(t,e,!1)},Ru);function Ru(t){return Pu.call(this,t)||this}Ou.prototype.type="polyline";var Nu,zu={},Eu=function(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.percent=1},Bu=(u(Fu,Nu=j),Fu.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},Fu.prototype.getDefaultShape=function(){return new Eu},Fu.prototype.buildPath=function(t,e){o=(this.subPixelOptimize?(n=(o=cs(zu,e,this.style)).x1,i=o.y1,r=o.x2,o):(n=e.x1,i=e.y1,r=e.x2,e)).y2;var n,i,r,o,e=e.percent;0!==e&&(t.moveTo(n,i),e<1&&(r=n*(1-e)+r*e,o=i*(1-e)+o*e),t.lineTo(r,o))},Fu.prototype.pointAt=function(t){var e=this.shape;return[e.x1*(1-t)+e.x2*t,e.y1*(1-t)+e.y2*t]},Fu);function Fu(t){return Nu.call(this,t)||this}Bu.prototype.type="line";var Vu=[],Hu=function(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.cpx1=0,this.cpy1=0,this.percent=1};function Gu(t,e,n){var i=t.cpx2,r=t.cpy2;return null!=i||null!=r?[(n?pn:cn)(t.x1,t.cpx1,t.cpx2,t.x2,e),(n?pn:cn)(t.y1,t.cpy1,t.cpy2,t.y2,e)]:[(n?vn:mn)(t.x1,t.cpx1,t.x2,e),(n?vn:mn)(t.y1,t.cpy1,t.y2,e)]}u(Xu,Wu=j),Xu.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},Xu.prototype.getDefaultShape=function(){return new Hu},Xu.prototype.buildPath=function(t,e){var n=e.x1,i=e.y1,r=e.x2,o=e.y2,a=e.cpx1,s=e.cpy1,l=e.cpx2,u=e.cpy2,e=e.percent;0!==e&&(t.moveTo(n,i),null==l||null==u?(e<1&&(xn(n,a,r,e,Vu),a=Vu[1],r=Vu[2],xn(i,s,o,e,Vu),s=Vu[1],o=Vu[2]),t.quadraticCurveTo(a,s,r,o)):(e<1&&(gn(n,a,l,r,e,Vu),a=Vu[1],l=Vu[2],r=Vu[3],gn(i,s,u,o,e,Vu),s=Vu[1],u=Vu[2],o=Vu[3]),t.bezierCurveTo(a,s,l,u,r,o)))},Xu.prototype.pointAt=function(t){return Gu(this.shape,t,!1)},Xu.prototype.tangentAt=function(t){t=Gu(this.shape,t,!0);return qt(t,t)};var Wu,Uu=Xu;function Xu(t){return Wu.call(this,t)||this}Uu.prototype.type="bezier-curve";var Yu,qu=function(){this.cx=0,this.cy=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0},Zu=(u(ju,Yu=j),ju.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},ju.prototype.getDefaultShape=function(){return new qu},ju.prototype.buildPath=function(t,e){var n=e.cx,i=e.cy,r=Math.max(e.r,0),o=e.startAngle,a=e.endAngle,e=e.clockwise,s=Math.cos(o),l=Math.sin(o);t.moveTo(s*r+n,l*r+i),t.arc(n,i,r,o,a,!e)},ju);function ju(t){return Yu.call(this,t)||this}Zu.prototype.type="arc";u(Qu,Ku=j),Qu.prototype._updatePathDirty=function(){for(var t=this.shape.paths,e=this.shapeChanged(),n=0;n<t.length;n++)e=e||t[n].shapeChanged();e&&this.dirtyShape()},Qu.prototype.beforeBrush=function(){this._updatePathDirty();for(var t=this.shape.paths||[],e=this.getGlobalScale(),n=0;n<t.length;n++)t[n].path||t[n].createPathProxy(),t[n].path.setScale(e[0],e[1],t[n].segmentIgnoreThreshold)},Qu.prototype.buildPath=function(t,e){for(var n=e.paths||[],i=0;i<n.length;i++)n[i].buildPath(t,n[i].shape,!0)},Qu.prototype.afterBrush=function(){for(var t=this.shape.paths||[],e=0;e<t.length;e++)t[e].pathUpdated()},Qu.prototype.getBoundingRect=function(){return this._updatePathDirty.call(this),j.prototype.getBoundingRect.call(this)};var Ku,$u=Qu;function Qu(){var t=null!==Ku&&Ku.apply(this,arguments)||this;return t.type="compound",t}th.prototype.addColorStop=function(t,e){this.colorStops.push({offset:t,color:e})};var Ju=th;function th(t){this.colorStops=t||[]}u(ih,eh=Ju);var eh,nh=ih;function ih(t,e,n,i,r,o){r=eh.call(this,r)||this;return r.x=null==t?0:t,r.y=null==e?0:e,r.x2=null==n?1:n,r.y2=null==i?0:i,r.type="linear",r.global=o||!1,r}u(oh,rh=Ju);var rh,Ju=oh;function oh(t,e,n,i,r){i=rh.call(this,i)||this;return i.x=null==t?.5:t,i.y=null==e?.5:e,i.r=null==n?.5:n,i.type="radial",i.global=r||!1,i}var ah=[0,0],sh=[0,0],lh=new M,uh=new M,hh=(ch.prototype.fromBoundingRect=function(t,e){var n=this._corners,i=this._axes,r=t.x,o=t.y,a=r+t.width,t=o+t.height;if(n[0].set(r,o),n[1].set(a,o),n[2].set(a,t),n[3].set(r,t),e)for(var s=0;s<4;s++)n[s].transform(e);M.sub(i[0],n[1],n[0]),M.sub(i[1],n[3],n[0]),i[0].normalize(),i[1].normalize();for(s=0;s<2;s++)this._origin[s]=i[s].dot(n[0])},ch.prototype.intersect=function(t,e){var n=!0,i=!e;return lh.set(1/0,1/0),uh.set(0,0),!this._intersectCheckOneSide(this,t,lh,uh,i,1)&&(n=!1,i)||!this._intersectCheckOneSide(t,this,lh,uh,i,-1)&&(n=!1,i)||i||M.copy(e,n?lh:uh),n},ch.prototype._intersectCheckOneSide=function(t,e,n,i,r,o){for(var a=!0,s=0;s<2;s++){var l=this._axes[s];if(this._getProjMinMaxOnAxis(s,t._corners,ah),this._getProjMinMaxOnAxis(s,e._corners,sh),ah[1]<sh[0]||sh[1]<ah[0]){if(a=!1,r)return a;var u=Math.abs(sh[0]-ah[1]),h=Math.abs(ah[0]-sh[1]);Math.min(u,h)>i.len()&&(u<h?M.scale(i,l,-u*o):M.scale(i,l,h*o))}else n&&(u=Math.abs(sh[0]-ah[1]),h=Math.abs(ah[0]-sh[1]),Math.min(u,h)<n.len())&&(u<h?M.scale(n,l,u*o):M.scale(n,l,-h*o))}return a},ch.prototype._getProjMinMaxOnAxis=function(t,e,n){for(var i=this._axes[t],r=this._origin,o=e[0].dot(i)+r[t],a=o,s=o,l=1;l<e.length;l++)var u=e[l].dot(i)+r[t],a=Math.min(u,a),s=Math.max(u,s);n[0]=a,n[1]=s},ch);function ch(t,e){this._corners=[],this._axes=[],this._origin=[0,0];for(var n=0;n<4;n++)this._corners[n]=new M;for(n=0;n<2;n++)this._axes[n]=new M;t&&this.fromBoundingRect(t,e)}var ph,dh=[],i=(u(fh,ph=i),fh.prototype.traverse=function(t,e){t.call(e,this)},fh.prototype.useStyle=function(){this.style={}},fh.prototype.getCursor=function(){return this._cursor},fh.prototype.innerAfterBrush=function(){this._cursor=this._displayables.length},fh.prototype.clearDisplaybles=function(){this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.markRedraw(),this.notClear=!1},fh.prototype.clearTemporalDisplayables=function(){this._temporaryDisplayables=[]},fh.prototype.addDisplayable=function(t,e){(e?this._temporaryDisplayables:this._displayables).push(t),this.markRedraw()},fh.prototype.addDisplayables=function(t,e){e=e||!1;for(var n=0;n<t.length;n++)this.addDisplayable(t[n],e)},fh.prototype.getDisplayables=function(){return this._displayables},fh.prototype.getTemporalDisplayables=function(){return this._temporaryDisplayables},fh.prototype.eachPendingDisplayable=function(t){for(var e=this._cursor;e<this._displayables.length;e++)t&&t(this._displayables[e]);for(e=0;e<this._temporaryDisplayables.length;e++)t&&t(this._temporaryDisplayables[e])},fh.prototype.update=function(){this.updateTransform();for(var t=this._cursor;t<this._displayables.length;t++)(e=this._displayables[t]).parent=this,e.update(),e.parent=null;for(var e,t=0;t<this._temporaryDisplayables.length;t++)(e=this._temporaryDisplayables[t]).parent=this,e.update(),e.parent=null},fh.prototype.getBoundingRect=function(){if(!this._rect){for(var t=new X(1/0,1/0,-1/0,-1/0),e=0;e<this._displayables.length;e++){var n=this._displayables[e],i=n.getBoundingRect().clone();n.needLocalTransform()&&i.applyTransform(n.getLocalTransform(dh)),t.union(i)}this._rect=t}return this._rect},fh.prototype.contain=function(t,e){var n=this.transformCoordToLocal(t,e);if(this.getBoundingRect().contain(n[0],n[1]))for(var i=0;i<this._displayables.length;i++)if(this._displayables[i].contain(t,e))return!0;return!1},fh);function fh(){var t=null!==ph&&ph.apply(this,arguments)||this;return t.notClear=!0,t.incremental=!0,t._displayables=[],t._temporaryDisplayables=[],t._cursor=0,t}var gh=_o();function yh(t,e,n,i,r,o,a){var s,l,u,h,c,p,d=!1,f=(S(r)?(a=o,o=r,r=null):R(r)&&(o=r.cb,a=r.during,d=r.isFrom,l=r.removeOpt,r=r.dataIndex),"leave"===t),g=(f||e.stopAnimation("leave"),p=t,s=r,l=f?l||{}:null,i=(g=i)&&i.getAnimationDelayParams?i.getAnimationDelayParams(e,r):null,g&&g.ecModel&&(u=(u=g.ecModel.getUpdatePayload())&&u.animation),p="update"===p,g&&g.isAnimationEnabled()?(c=h=r=void 0,c=l?(r=N(l.duration,200),h=N(l.easing,"cubicOut"),0):(r=g.getShallow(p?"animationDurationUpdate":"animationDuration"),h=g.getShallow(p?"animationEasingUpdate":"animationEasing"),g.getShallow(p?"animationDelayUpdate":"animationDelay")),S(c=u&&(null!=u.duration&&(r=u.duration),null!=u.easing&&(h=u.easing),null!=u.delay)?u.delay:c)&&(c=c(s,i)),{duration:(r=S(r)?r(s):r)||0,delay:c,easing:h}):null);g&&0<g.duration?(p={duration:g.duration,delay:g.delay||0,easing:g.easing,done:o,force:!!o||!!a,setToFinal:!f,scope:t,during:a},d?e.animateFrom(n,p):e.animateTo(n,p)):(e.stopAnimation(),d||e.attr(n),a&&a(1),o&&o())}function mh(t,e,n,i,r,o){yh("update",t,e,n,i,r,o)}function vh(t,e,n,i,r,o){yh("enter",t,e,n,i,r,o)}function _h(t){if(!t.__zr)return!0;for(var e=0;e<t.animators.length;e++)if("leave"===t.animators[e].scope)return!0;return!1}function xh(t,e,n,i,r,o){_h(t)||yh("leave",t,e,n,i,r,o)}function wh(t,e,n,i){t.removeTextContent(),t.removeTextGuideLine(),xh(t,{style:{opacity:0}},e,n,i)}var bh=Math.max,Sh=Math.min,Mh={};function Th(t){return j.extend(t)}var Ch=function(t,e){var n,i=Kl(t,e);function r(t){t=n.call(this,t)||this;return t.applyTransform=i.applyTransform,t.buildPath=i.buildPath,t}return u(r,n=ql),r};function Ih(t,e){return Ch(t,e)}function kh(t,e){Mh[t]=e}function Dh(t){if(Mh.hasOwnProperty(t))return Mh[t]}function Ah(t,e,n,i){t=new ql(Kl(t,e));return n&&("center"===i&&(n=Lh(n,t.getBoundingRect())),Rh(t,n)),t}function Ph(t,e,n){var i=new ls({style:{image:t,x:e.x,y:e.y,width:e.width,height:e.height},onload:function(t){"center"===n&&(t={width:t.width,height:t.height},i.setStyle(Lh(e,t)))}});return i}function Lh(t,e){var e=e.width/e.height,n=t.height*e,e=n<=t.width?t.height:(n=t.width)/e;return{x:t.x+t.width/2-n/2,y:t.y+t.height/2-e/2,width:n,height:e}}function Oh(t,e){for(var n=[],i=t.length,r=0;r<i;r++){var o=t[r];n.push(o.getUpdatedPathProxy(!0))}return(e=new j(e)).createPathProxy(),e.buildPath=function(t){var e;jl(t)&&(t.appendPath(n),e=t.getContext())&&t.rebuildPath(e,1)},e}function Rh(t,e){t.applyTransform&&(e=t.getBoundingRect().calculateTransform(e),t.applyTransform(e))}var Nh=ds;function zh(t,e){for(var n=Fi([]);t&&t!==e;)Hi(n,t.getLocalTransform(),n),t=t.parent;return n}function Eh(t,e,n){return e&&!st(e)&&(e=Qi.getLocalTransform(e)),Jt([],t,e=n?Xi([],e):e)}function Bh(t){return!t.isGroup}function Fh(t,e,i){var r,n;function o(t){var e={x:t.x,y:t.y,rotation:t.rotation};return null!=t.shape&&(e.shape=L({},t.shape)),e}t&&e&&(n={},t.traverse(function(t){Bh(t)&&t.anid&&(n[t.anid]=t)}),r=n,e.traverse(function(t){var e,n;Bh(t)&&t.anid&&(e=r[t.anid])&&(n=o(t),t.attr(o(e)),mh(t,n,i,k(t).dataIndex))}))}function Vh(t,n){return B(t,function(t){var e=t[0],e=bh(e,n.x),t=(e=Sh(e,n.x+n.width),t[1]),t=bh(t,n.y);return[e,Sh(t,n.y+n.height)]})}function Hh(t,e){var n=bh(t.x,e.x),i=Sh(t.x+t.width,e.x+e.width),r=bh(t.y,e.y),t=Sh(t.y+t.height,e.y+e.height);if(n<=i&&r<=t)return{x:n,y:r,width:i-n,height:t-r}}function Gh(t,e,n){var e=L({rectHover:!0},e),i=e.style={strokeNoScale:!0};if(n=n||{x:-1,y:-1,width:2,height:2},t)return 0===t.indexOf("image://")?(i.image=t.slice(8),E(i,n),new ls(e)):Ah(t.replace("path://",""),e,n,"center")}function Wh(t,e,n,i,r,o,a,s){var l,n=n-t,i=i-e,a=a-r,s=s-o,u=a*i-n*s;return!((l=u)<=1e-6&&-1e-6<=l||(r=((l=t-r)*i-n*(t=e-o))/u)<0||1<r||(i=(l*s-a*t)/u)<0||1<i)}function Uh(t){var e=t.itemTooltipOption,n=t.componentModel,i=t.itemName,e=V(e)?{formatter:e}:e,r=n.mainType,n=n.componentIndex,o={componentType:r,name:i,$vars:["name"]},a=(o[r+"Index"]=n,t.formatterParamsExtra),t=(a&&O(ht(a),function(t){zt(o,t)||(o[t]=a[t],o.$vars.push(t))}),k(t.el));t.componentMainType=r,t.componentIndex=n,t.tooltipConfig={name:i,option:E({content:i,formatterParams:o},e)}}function Xh(t,e){var n;(n=t.isGroup?e(t):n)||t.traverse(e)}function Yh(t,e){if(t)if(F(t))for(var n=0;n<t.length;n++)Xh(t[n],e);else Xh(t,e)}kh("circle",Jl),kh("ellipse",iu),kh("sector",xu),kh("ring",Mu),kh("polygon",Du),kh("polyline",Ou),kh("rect",ms),kh("line",Bu),kh("bezierCurve",Uu),kh("arc",Zu);var qh=Object.freeze({__proto__:null,Arc:Zu,BezierCurve:Uu,BoundingRect:X,Circle:Jl,CompoundPath:$u,Ellipse:iu,Group:Pr,Image:ls,IncrementalDisplayable:i,Line:Bu,LinearGradient:nh,OrientedBoundingRect:hh,Path:j,Point:M,Polygon:Du,Polyline:Ou,RadialGradient:Ju,Rect:ms,Ring:Mu,Sector:xu,Text:bs,applyTransform:Eh,clipPointsByRect:Vh,clipRectByRect:Hh,createIcon:Gh,extendPath:Ih,extendShape:Th,getShapeClass:Dh,getTransform:zh,groupTransition:Fh,initProps:vh,isElementRemoved:_h,lineLineIntersect:Wh,linePolygonIntersect:function(t,e,n,i,r){for(var o=0,a=r[r.length-1];o<r.length;o++){var s=r[o];if(Wh(t,e,n,i,s[0],s[1],a[0],a[1]))return!0;a=s}},makeImage:Ph,makePath:Ah,mergePath:Oh,registerShape:kh,removeElement:xh,removeElementWithFadeOut:function(t,e,n){function i(){t.parent&&t.parent.remove(t)}t.isGroup?t.traverse(function(t){t.isGroup||wh(t,e,n,i)}):wh(t,e,n,i)},resizePath:Rh,setTooltipConfig:Uh,subPixelOptimize:Nh,subPixelOptimizeLine:function(t){return cs(t.shape,t.shape,t.style),t},subPixelOptimizeRect:function(t){return ps(t.shape,t.shape,t.style),t},transformDirection:function(t,e,n){var i=0===e[4]||0===e[5]||0===e[0]?1:Math.abs(2*e[4]/e[0]),r=0===e[4]||0===e[5]||0===e[2]?1:Math.abs(2*e[4]/e[2]),i=Eh(["left"===t?-i:"right"===t?i:0,"top"===t?-r:"bottom"===t?r:0],e,n);return Math.abs(i[0])>Math.abs(i[1])?0<i[0]?"right":"left":0<i[1]?"bottom":"top"},traverseElements:Yh,updateProps:mh}),Zh={};function jh(t,e){for(var n=0;n<Hs.length;n++){var i=Hs[n],r=e[i],i=t.ensureState(i);i.style=i.style||{},i.style.text=r}var o=t.currentStates.slice();t.clearStates(!0),t.setStyle({text:e.normal}),t.useStates(o,!0)}function Kh(t,e,n){for(var i,r=t.labelFetcher,o=t.labelDataIndex,a=t.labelDimIndex,s=e.normal,l={normal:i=null==(i=r?r.getFormattedLabel(o,"normal",null,a,s&&s.get("formatter"),null!=n?{interpolatedValue:n}:null):i)?S(t.defaultText)?t.defaultText(o,t,n):t.defaultText:i},u=0;u<Hs.length;u++){var h=Hs[u],c=e[h];l[h]=N(r?r.getFormattedLabel(o,h,null,a,c&&c.get("formatter")):null,i)}return l}function $h(t,e,n,i){n=n||Zh;for(var r=t instanceof bs,o=!1,a=0;a<Gs.length;a++)if((p=e[Gs[a]])&&p.getShallow("show")){o=!0;break}var s=r?t:t.getTextContent();if(o){r||(s||(s=new bs,t.setTextContent(s)),t.stateProxy&&(s.stateProxy=t.stateProxy));var l=Kh(n,e),u=e.normal,h=!!u.getShallow("show"),c=Jh(u,i&&i.normal,n,!1,!r);c.text=l.normal,r||t.setTextConfig(tc(u,n,!1));for(a=0;a<Hs.length;a++){var p,d,f,g=Hs[a];(p=e[g])&&(d=s.ensureState(g),(f=!!N(p.getShallow("show"),h))!=h&&(d.ignore=!f),d.style=Jh(p,i&&i[g],n,!0,!r),d.style.text=l[g],r||(t.ensureState(g).textConfig=tc(p,n,!0)))}s.silent=!!u.getShallow("silent"),null!=s.style.x&&(c.x=s.style.x),null!=s.style.y&&(c.y=s.style.y),s.ignore=!h,s.useStyle(c),s.dirty(),n.enableTextSetter&&(oc(s).setLabelText=function(t){t=Kh(n,e,t);jh(s,t)})}else s&&(s.ignore=!0);t.dirty()}function Qh(t,e){for(var n={normal:t.getModel(e=e||"label")},i=0;i<Hs.length;i++){var r=Hs[i];n[r]=t.getModel([r,e])}return n}function Jh(t,e,n,i,r){var o,a={},s=a,l=t,u=n,h=i,c=r;u=u||Zh;var p,t=l.ecModel,d=t&&t.option.textStyle,f=function(t){var e;for(;t&&t!==t.ecModel;){var n=(t.option||Zh).rich;if(n){e=e||{};for(var i=ht(n),r=0;r<i.length;r++){var o=i[r];e[o]=1}}t=t.parentModel}return e}(l);if(f)for(var g in p={},f)f.hasOwnProperty(g)&&(o=l.getModel(["rich",g]),rc(p[g]={},o,d,u,h,c,!1,!0));return p&&(s.rich=p),(t=l.get("overflow"))&&(s.overflow=t),null!=(t=l.get("minMargin"))&&(s.margin=t),rc(s,l,d,u,h,c,!0,!1),e&&L(a,e),a}function tc(t,e,n){e=e||{};var i={},r=t.getShallow("rotate"),o=N(t.getShallow("distance"),n?null:5),a=t.getShallow("offset"),n=t.getShallow("position")||(n?null:"inside");return null!=(n="outside"===n?e.defaultOutsidePosition||"top":n)&&(i.position=n),null!=a&&(i.offset=a),null!=r&&(r*=Math.PI/180,i.rotation=r),null!=o&&(i.distance=o),i.outsideFill="inherit"===t.get("color")?e.inheritColor||null:"auto",i}var ec=["fontStyle","fontWeight","fontSize","fontFamily","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY"],nc=["align","lineHeight","width","height","tag","verticalAlign"],ic=["padding","borderWidth","borderRadius","borderDashOffset","backgroundColor","borderColor","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"];function rc(t,e,n,i,r,o,a,s){n=!r&&n||Zh;var l=i&&i.inheritColor,u=e.getShallow("color"),h=e.getShallow("textBorderColor"),c=N(e.getShallow("opacity"),n.opacity),u=("inherit"!==u&&"auto"!==u||(u=l||null),"inherit"!==h&&"auto"!==h||(h=l||null),o||(u=u||n.color,h=h||n.textBorderColor),null!=u&&(t.fill=u),null!=h&&(t.stroke=h),N(e.getShallow("textBorderWidth"),n.textBorderWidth)),h=(null!=u&&(t.lineWidth=u),N(e.getShallow("textBorderType"),n.textBorderType)),u=(null!=h&&(t.lineDash=h),N(e.getShallow("textBorderDashOffset"),n.textBorderDashOffset));null!=u&&(t.lineDashOffset=u),null!=(c=r||null!=c||s?c:i&&i.defaultOpacity)&&(t.opacity=c),r||o||null==t.fill&&i.inheritColor&&(t.fill=i.inheritColor);for(var p=0;p<ec.length;p++){var d=ec[p];null!=(f=N(e.getShallow(d),n[d]))&&(t[d]=f)}for(var p=0;p<nc.length;p++){d=nc[p];null!=(f=e.getShallow(d))&&(t[d]=f)}if(null==t.verticalAlign&&null!=(h=e.getShallow("baseline"))&&(t.verticalAlign=h),!a||!i.disableBox){for(p=0;p<ic.length;p++){var f,d=ic[p];null!=(f=e.getShallow(d))&&(t[d]=f)}u=e.getShallow("borderType");null!=u&&(t.borderDash=u),"auto"!==t.backgroundColor&&"inherit"!==t.backgroundColor||!l||(t.backgroundColor=l),"auto"!==t.borderColor&&"inherit"!==t.borderColor||!l||(t.borderColor=l)}}var oc=_o();function ac(n,i,r,t,o){var a,s,l,u=oc(n);u.valueAnimation&&u.prevValue!==u.value&&(a=u.defaultInterpolatedText,s=N(u.interpolatedValue,u.prevValue),l=u.value,n.percent=0,(null==u.prevValue?vh:mh)(n,{percent:1},t,i,null,function(t){var e=Co(r,u.precision,s,l,t),t=(u.interpolatedValue=1===t?null:e,Kh({labelDataIndex:i,labelFetcher:o,defaultText:a?a(e):e+""},u.statesModels,e));jh(n,t)}))}var sc=["textStyle","color"],lc=["fontStyle","fontWeight","fontSize","fontFamily","padding","lineHeight","rich","width","height","overflow"],uc=new bs,Nh=(hc.prototype.getTextColor=function(t){var e=this.ecModel;return this.getShallow("color")||(!t&&e?e.get(sc):null)},hc.prototype.getFont=function(){return t={fontStyle:this.getShallow("fontStyle"),fontWeight:this.getShallow("fontWeight"),fontSize:this.getShallow("fontSize"),fontFamily:this.getShallow("fontFamily")},e=(e=this.ecModel)&&e.getModel("textStyle"),It([t.fontStyle||e&&e.getShallow("fontStyle")||"",t.fontWeight||e&&e.getShallow("fontWeight")||"",(t.fontSize||e&&e.getShallow("fontSize")||12)+"px",t.fontFamily||e&&e.getShallow("fontFamily")||"sans-serif"].join(" "));var t,e},hc.prototype.getTextRect=function(t){for(var e={text:t,verticalAlign:this.getShallow("verticalAlign")||this.getShallow("baseline")},n=0;n<lc.length;n++)e[lc[n]]=this.getShallow(lc[n]);return uc.useStyle(e),uc.update(),uc.getBoundingRect()},hc);function hc(){}var cc=[["lineWidth","width"],["stroke","color"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","type"],["lineDashOffset","dashOffset"],["lineCap","cap"],["lineJoin","join"],["miterLimit"]],pc=Eo(cc),dc=(fc.prototype.getLineStyle=function(t){return pc(this,t)},fc);function fc(){}var gc=[["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","borderType"],["lineDashOffset","borderDashOffset"],["lineCap","borderCap"],["lineJoin","borderJoin"],["miterLimit","borderMiterLimit"]],yc=Eo(gc),mc=(vc.prototype.getItemStyle=function(t,e){return yc(this,t,e)},vc);function vc(){}wc.prototype.init=function(t,e,n){},wc.prototype.mergeOption=function(t,e){d(this.option,t,!0)},wc.prototype.get=function(t,e){return null==t?this.option:this._doGet(this.parsePath(t),!e&&this.parentModel)},wc.prototype.getShallow=function(t,e){var n=this.option,n=null==n?n:n[t];return null!=n||e||(e=this.parentModel)&&(n=e.getShallow(t)),n},wc.prototype.getModel=function(t,e){var n=null!=t,t=n?this.parsePath(t):null;return new wc(n?this._doGet(t):this.option,e=e||this.parentModel&&this.parentModel.getModel(this.resolveParentPath(t)),this.ecModel)},wc.prototype.isEmpty=function(){return null==this.option},wc.prototype.restoreData=function(){},wc.prototype.clone=function(){return new this.constructor(y(this.option))},wc.prototype.parsePath=function(t){return"string"==typeof t?t.split("."):t},wc.prototype.resolveParentPath=function(t){return t},wc.prototype.isAnimationEnabled=function(){if(!b.node&&this.option)return null!=this.option.animation?!!this.option.animation:this.parentModel?this.parentModel.isAnimationEnabled():void 0},wc.prototype._doGet=function(t,e){var n=this.option;if(t){for(var i=0;i<t.length&&(!t[i]||null!=(n=n&&"object"==typeof n?n[t[i]]:null));i++);null==n&&e&&(n=e._doGet(this.resolveParentPath(t),e.parentModel))}return n};var _c,xc=wc;function wc(t,e,n){this.parentModel=e,this.ecModel=n,this.option=t}Po(xc),zc=xc,_c=["__\0is_clz",Oo++].join("_"),zc.prototype[_c]=!0,zc.isInstance=function(t){return!(!t||!t[_c])},at(xc,dc),at(xc,mc),at(xc,Fo),at(xc,Nh);var bc=Math.round(10*Math.random());function Sc(t){return[t||"",bc++].join("_")}var Mc="ZH",Tc="EN",Cc=Tc,Ic={},kc={},Dc=b.domSupported&&-1<(document.documentElement.lang||navigator.language||navigator.browserLanguage).toUpperCase().indexOf(Mc)?Mc:Cc;function Ac(t,e){t=t.toUpperCase(),kc[t]=new xc(e),Ic[t]=e}Ac(Tc,{time:{month:["January","February","March","April","May","June","July","August","September","October","November","December"],monthAbbr:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],dayOfWeek:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],dayOfWeekAbbr:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"]},legend:{selector:{all:"All",inverse:"Inv"}},toolbox:{brush:{title:{rect:"Box Select",polygon:"Lasso Select",lineX:"Horizontally Select",lineY:"Vertically Select",keep:"Keep Selections",clear:"Clear Selections"}},dataView:{title:"Data View",lang:["Data View","Close","Refresh"]},dataZoom:{title:{zoom:"Zoom",back:"Zoom Reset"}},magicType:{title:{line:"Switch to Line Chart",bar:"Switch to Bar Chart",stack:"Stack",tiled:"Tile"}},restore:{title:"Restore"},saveAsImage:{title:"Save as Image",lang:["Right Click to Save Image"]}},series:{typeNames:{pie:"Pie chart",bar:"Bar chart",line:"Line chart",scatter:"Scatter plot",effectScatter:"Ripple scatter plot",radar:"Radar chart",tree:"Tree",treemap:"Treemap",boxplot:"Boxplot",candlestick:"Candlestick",k:"K line chart",heatmap:"Heat map",map:"Map",parallel:"Parallel coordinate map",lines:"Line graph",graph:"Relationship graph",sankey:"Sankey diagram",funnel:"Funnel chart",gauge:"Gauge",pictorialBar:"Pictorial bar",themeRiver:"Theme River Map",sunburst:"Sunburst"}},aria:{general:{withTitle:'This is a chart about "{title}"',withoutTitle:"This is a chart"},series:{single:{prefix:"",withName:" with type {seriesType} named {seriesName}.",withoutName:" with type {seriesType}."},multiple:{prefix:". It consists of {seriesCount} series count.",withName:" The {seriesId} series is a {seriesType} representing {seriesName}.",withoutName:" The {seriesId} series is a {seriesType}.",separator:{middle:"",end:""}}},data:{allData:"The data is as follows: ",partialData:"The first {displayCnt} items are: ",withName:"the data for {name} is {value}",withoutName:"{value}",separator:{middle:", ",end:". "}}}}),Ac(Mc,{time:{month:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"],monthAbbr:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],dayOfWeek:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"],dayOfWeekAbbr:["日","一","二","三","四","五","六"]},legend:{selector:{all:"全选",inverse:"反选"}},toolbox:{brush:{title:{rect:"矩形选择",polygon:"圈选",lineX:"横向选择",lineY:"纵向选择",keep:"保持选择",clear:"清除选择"}},dataView:{title:"数据视图",lang:["数据视图","关闭","刷新"]},dataZoom:{title:{zoom:"区域缩放",back:"区域缩放还原"}},magicType:{title:{line:"切换为折线图",bar:"切换为柱状图",stack:"切换为堆叠",tiled:"切换为平铺"}},restore:{title:"还原"},saveAsImage:{title:"保存为图片",lang:["右键另存为图片"]}},series:{typeNames:{pie:"饼图",bar:"柱状图",line:"折线图",scatter:"散点图",effectScatter:"涟漪散点图",radar:"雷达图",tree:"树图",treemap:"矩形树图",boxplot:"箱型图",candlestick:"K线图",k:"K线图",heatmap:"热力图",map:"地图",parallel:"平行坐标图",lines:"线图",graph:"关系图",sankey:"桑基图",funnel:"漏斗图",gauge:"仪表盘图",pictorialBar:"象形柱图",themeRiver:"主题河流图",sunburst:"旭日图"}},aria:{general:{withTitle:"这是一个关于“{title}”的图表。",withoutTitle:"这是一个图表，"},series:{single:{prefix:"",withName:"图表类型是{seriesType}，表示{seriesName}。",withoutName:"图表类型是{seriesType}。"},multiple:{prefix:"它由{seriesCount}个图表系列组成。",withName:"第{seriesId}个系列是一个表示{seriesName}的{seriesType}，",withoutName:"第{seriesId}个系列是一个{seriesType}，",separator:{middle:"；",end:"。"}}},data:{allData:"其数据是——",partialData:"其中，前{displayCnt}项是——",withName:"{name}的数据是{value}",withoutName:"{value}",separator:{middle:"，",end:""}}}});var Pc=1e3,Lc=60*Pc,Oc=60*Lc,Rc=24*Oc,Oo=365*Rc,Nc={year:"{yyyy}",month:"{MMM}",day:"{d}",hour:"{HH}:{mm}",minute:"{HH}:{mm}",second:"{HH}:{mm}:{ss}",millisecond:"{HH}:{mm}:{ss} {SSS}",none:"{yyyy}-{MM}-{dd} {HH}:{mm}:{ss} {SSS}"},zc="{yyyy}-{MM}-{dd}",Ec={year:"{yyyy}",month:"{yyyy}-{MM}",day:zc,hour:zc+" "+Nc.hour,minute:zc+" "+Nc.minute,second:zc+" "+Nc.second,millisecond:Nc.none},Bc=["year","month","day","hour","minute","second","millisecond"],Fc=["year","half-year","quarter","month","week","half-week","day","half-day","quarter-day","hour","minute","second","millisecond"];function Vc(t,e){return"0000".substr(0,e-(t+="").length)+t}function Hc(t){switch(t){case"half-year":case"quarter":return"month";case"week":case"half-week":return"day";case"half-day":case"quarter-day":return"hour";default:return t}}function Gc(t,e,n,i){var t=Kr(t),r=t[Xc(n)](),o=t[Yc(n)]()+1,a=Math.floor((o-1)/4)+1,s=t[qc(n)](),l=t["get"+(n?"UTC":"")+"Day"](),u=t[Zc(n)](),h=(u-1)%12+1,c=t[jc(n)](),p=t[Kc(n)](),t=t[$c(n)](),n=(i instanceof xc?i:kc[i||Dc]||kc[Cc]).getModel("time"),i=n.get("month"),d=n.get("monthAbbr"),f=n.get("dayOfWeek"),n=n.get("dayOfWeekAbbr");return(e||"").replace(/{yyyy}/g,r+"").replace(/{yy}/g,r%100+"").replace(/{Q}/g,a+"").replace(/{MMMM}/g,i[o-1]).replace(/{MMM}/g,d[o-1]).replace(/{MM}/g,Vc(o,2)).replace(/{M}/g,o+"").replace(/{dd}/g,Vc(s,2)).replace(/{d}/g,s+"").replace(/{eeee}/g,f[l]).replace(/{ee}/g,n[l]).replace(/{e}/g,l+"").replace(/{HH}/g,Vc(u,2)).replace(/{H}/g,u+"").replace(/{hh}/g,Vc(h+"",2)).replace(/{h}/g,h+"").replace(/{mm}/g,Vc(c,2)).replace(/{m}/g,c+"").replace(/{ss}/g,Vc(p,2)).replace(/{s}/g,p+"").replace(/{SSS}/g,Vc(t,3)).replace(/{S}/g,t+"")}function Wc(t,e){var t=Kr(t),n=t[Yc(e)]()+1,i=t[qc(e)](),r=t[Zc(e)](),o=t[jc(e)](),a=t[Kc(e)](),t=0===t[$c(e)](),e=t&&0===a,a=e&&0===o,o=a&&0===r,r=o&&1===i;return r&&1===n?"year":r?"month":o?"day":a?"hour":e?"minute":t?"second":"millisecond"}function Uc(t,e,n){var i=ft(t)?Kr(t):t;switch(e=e||Wc(t,n)){case"year":return i[Xc(n)]();case"half-year":return 6<=i[Yc(n)]()?1:0;case"quarter":return Math.floor((i[Yc(n)]()+1)/4);case"month":return i[Yc(n)]();case"day":return i[qc(n)]();case"half-day":return i[Zc(n)]()/24;case"hour":return i[Zc(n)]();case"minute":return i[jc(n)]();case"second":return i[Kc(n)]();case"millisecond":return i[$c(n)]()}}function Xc(t){return t?"getUTCFullYear":"getFullYear"}function Yc(t){return t?"getUTCMonth":"getMonth"}function qc(t){return t?"getUTCDate":"getDate"}function Zc(t){return t?"getUTCHours":"getHours"}function jc(t){return t?"getUTCMinutes":"getMinutes"}function Kc(t){return t?"getUTCSeconds":"getSeconds"}function $c(t){return t?"getUTCMilliseconds":"getMilliseconds"}function Qc(t){return t?"setUTCMonth":"setMonth"}function Jc(t){return t?"setUTCDate":"setDate"}function tp(t){return t?"setUTCHours":"setHours"}function ep(t){return t?"setUTCMinutes":"setMinutes"}function np(t){return t?"setUTCSeconds":"setSeconds"}function ip(t){return t?"setUTCMilliseconds":"setMilliseconds"}function rp(t){var e;return eo(t)?(e=(t+"").split("."))[0].replace(/(\d{1,3})(?=(?:\d{3})+(?!\d))/g,"$1,")+(1<e.length?"."+e[1]:""):V(t)?t:"-"}function op(t,e){return t=(t||"").toLowerCase().replace(/-(.)/g,function(t,e){return e.toUpperCase()}),t=e?t&&t.charAt(0).toUpperCase()+t.slice(1):t}var ap=Tt,sp=/([&<>"'])/g,lp={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};function up(t){return null==t?"":(t+"").replace(sp,function(t,e){return lp[e]})}function hp(t,e,n){function i(t){return t&&It(t)?t:"-"}function r(t){return null!=t&&!isNaN(t)&&isFinite(t)}var o="time"===e,a=t instanceof Date;if(o||a){var o=o?Kr(t):t;if(!isNaN(+o))return Gc(o,"{yyyy}-{MM}-{dd} {hh}:{mm}:{ss}",n);if(a)return"-"}return"ordinal"===e?dt(t)?i(t):ft(t)&&r(t)?t+"":"-":r(o=to(t))?rp(o):dt(t)?i(t):"boolean"==typeof t?t+"":"-"}function cp(t,e){return"{"+t+(null==e?"":e)+"}"}var pp=["a","b","c","d","e","f","g"];function dp(t,e,n){var i=(e=F(e)?e:[e]).length;if(!i)return"";for(var r=e[0].$vars||[],o=0;o<r.length;o++){var a=pp[o];t=t.replace(cp(a),cp(a,0))}for(var s=0;s<i;s++)for(var l=0;l<r.length;l++){var u=e[s][r[l]];t=t.replace(cp(pp[l],s),n?up(u):u)}return t}function fp(t,e){var t=V(t)?{color:t,extraCssText:e}:t||{},n=t.color,i=t.type,r=(e=t.extraCssText,t.renderMode||"html");return n?"html"===r?"subItem"===i?'<span style="display:inline-block;vertical-align:middle;margin-right:8px;margin-left:3px;border-radius:4px;width:4px;height:4px;background-color:'+up(n)+";"+(e||"")+'"></span>':'<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:'+up(n)+";"+(e||"")+'"></span>':{renderMode:r,content:"{"+(t.markerId||"markerX")+"|}  ",style:"subItem"===i?{width:4,height:4,borderRadius:2,backgroundColor:n}:{width:10,height:10,borderRadius:5,backgroundColor:n}}:""}function gp(t,e){return e=e||"transparent",V(t)?t:R(t)&&t.colorStops&&(t.colorStops[0]||{}).color||e}var yp=O,mp=["left","right","top","bottom","width","height"],vp=[["width","left","right"],["height","top","bottom"]];function _p(a,s,l,u,h){var c=0,p=0,d=(null==u&&(u=1/0),null==h&&(h=1/0),0);s.eachChild(function(t,e){var n,i,r,o=t.getBoundingRect(),e=s.childAt(e+1),e=e&&e.getBoundingRect();d="horizontal"===a?(i=o.width+(e?-e.x+o.x:0),u<(n=c+i)||t.newline?(c=0,n=i,p+=d+l,o.height):Math.max(d,o.height)):(i=o.height+(e?-e.y+o.y:0),h<(r=p+i)||t.newline?(c+=d+l,p=0,r=i,o.width):Math.max(d,o.width)),t.newline||(t.x=c,t.y=p,t.markRedraw(),"horizontal"===a?c=n+l:p=r+l)})}var xp=_p;function wp(t,e,n){n=ap(n||0);var i=e.width,r=e.height,o=Gr(t.left,i),a=Gr(t.top,r),e=Gr(t.right,i),s=Gr(t.bottom,r),l=Gr(t.width,i),u=Gr(t.height,r),h=n[2]+n[0],c=n[1]+n[3],p=t.aspect;switch(isNaN(l)&&(l=i-e-c-o),isNaN(u)&&(u=r-s-h-a),null!=p&&(isNaN(l)&&isNaN(u)&&(i/r<p?l=.8*i:u=.8*r),isNaN(l)&&(l=p*u),isNaN(u))&&(u=l/p),isNaN(o)&&(o=i-e-l-c),isNaN(a)&&(a=r-s-u-h),t.left||t.right){case"center":o=i/2-l/2-n[3];break;case"right":o=i-l-c}switch(t.top||t.bottom){case"middle":case"center":a=r/2-u/2-n[0];break;case"bottom":a=r-u-h}o=o||0,a=a||0,isNaN(l)&&(l=i-c-o-(e||0)),isNaN(u)&&(u=r-h-a-(s||0));p=new X(o+n[3],a+n[0],l,u);return p.margin=n,p}function bp(t){t=t.layoutMode||t.constructor.layoutMode;return R(t)?t:t?{type:t}:null}function Sp(l,u,t){var h=t&&t.ignoreSize,t=(F(h)||(h=[h,h]),n(vp[0],0)),e=n(vp[1],1);function n(t,e){var n={},i=0,r={},o=0;if(yp(t,function(t){r[t]=l[t]}),yp(t,function(t){c(u,t)&&(n[t]=r[t]=u[t]),p(n,t)&&i++,p(r,t)&&o++}),h[e])p(u,t[1])?r[t[2]]=null:p(u,t[2])&&(r[t[1]]=null);else if(2!==o&&i){if(!(2<=i))for(var a=0;a<t.length;a++){var s=t[a];if(!c(n,s)&&c(l,s)){n[s]=l[s];break}}return n}return r}function c(t,e){return t.hasOwnProperty(e)}function p(t,e){return null!=t[e]&&"auto"!==t[e]}function i(t,e,n){yp(t,function(t){e[t]=n[t]})}i(vp[0],l,t),i(vp[1],l,e)}function Mp(t){return e={},(n=t)&&e&&yp(mp,function(t){n.hasOwnProperty(t)&&(e[t]=n[t])}),e;var e,n}pt(_p,"vertical"),pt(_p,"horizontal");var Tp,Cp,Ip,kp,Dp=_o(),g=(u(Ap,Tp=xc),Ap.prototype.init=function(t,e,n){this.mergeDefaultAndTheme(t,n)},Ap.prototype.mergeDefaultAndTheme=function(t,e){var n=bp(this),i=n?Mp(t):{};d(t,e.getTheme().get(this.mainType)),d(t,this.getDefaultOption()),n&&Sp(t,i,n)},Ap.prototype.mergeOption=function(t,e){d(this.option,t,!0);var n=bp(this);n&&Sp(this.option,t,n)},Ap.prototype.optionUpdated=function(t,e){},Ap.prototype.getDefaultOption=function(){var t=this.constructor;if(!(e=t)||!e[Do])return t.defaultOption;var e=Dp(this);if(!e.defaultOption){for(var n=[],i=t;i;){var r=i.prototype.defaultOption;r&&n.push(r),i=i.superClass}for(var o={},a=n.length-1;0<=a;a--)o=d(o,n[a],!0);e.defaultOption=o}return e.defaultOption},Ap.prototype.getReferringComponents=function(t,e){var n=t+"Id";return Mo(this.ecModel,t,{index:this.get(t+"Index",!0),id:this.get(n,!0)},e)},Ap.prototype.getBoxLayoutParams=function(){return{left:this.get("left"),top:this.get("top"),right:this.get("right"),bottom:this.get("bottom"),width:this.get("width"),height:this.get("height")}},Ap.prototype.getZLevelKey=function(){return""},Ap.prototype.setZLevel=function(t){this.option.zlevel=t},Ap.protoInitialize=((dc=Ap.prototype).type="component",dc.id="",dc.name="",dc.mainType="",dc.subType="",void(dc.componentIndex=0)),Ap);function Ap(t,e,n){t=Tp.call(this,t,e,n)||this;return t.uid=Sc("ec_cpt_model"),t}function Pp(t,e){return t[e]||(t[e]={predecessor:[],successor:[]}),t[e]}Lo(g,xc),zo(g),Ip={},(Cp=g).registerSubTypeDefaulter=function(t,e){t=Ao(t);Ip[t.main]=e},Cp.determineSubType=function(t,e){var n,i=e.type;return i||(n=Ao(t).main,Cp.hasSubTypes(t)&&Ip[n]&&(i=Ip[n](e))),i},kp=function(t){var e=[];O(g.getClassesByMainType(t),function(t){e=e.concat(t.dependencies||t.prototype.dependencies||[])}),e=B(e,function(t){return Ao(t).main}),"dataset"!==t&&I(e,"dataset")<=0&&e.unshift("dataset");return e},g.topologicalTravel=function(t,e,n,i){if(t.length){a={},s=[],O(o=e,function(n){var e,i,r=Pp(a,n),t=r.originalDeps=kp(n),t=(e=o,i=[],O(t,function(t){0<=I(e,t)&&i.push(t)}),i);r.entryCount=t.length,0===r.entryCount&&s.push(n),O(t,function(t){I(r.predecessor,t)<0&&r.predecessor.push(t);var e=Pp(a,t);I(e.successor,t)<0&&e.successor.push(n)})});var o,a,s,e={graph:a,noEntryList:s},r=e.graph,l=e.noEntryList,u={};for(O(t,function(t){u[t]=!0});l.length;){var h=l.pop(),c=r[h],p=!!u[h];p&&(n.call(i,h,c.originalDeps.slice()),delete u[h]),O(c.successor,p?f:d)}O(u,function(){throw new Error("")})}function d(t){r[t].entryCount--,0===r[t].entryCount&&l.push(t)}function f(t){u[t]=!0,d(t)}};var mc="",Fo=("undefined"!=typeof navigator&&(mc=navigator.platform||""),"rgba(0, 0, 0, 0.2)"),Lp={darkMode:"auto",colorBy:"series",color:["#5470c6","#91cc75","#fac858","#ee6666","#73c0de","#3ba272","#fc8452","#9a60b4","#ea7ccc"],gradientColor:["#f6efa6","#d88273","#bf444c"],aria:{decal:{decals:[{color:Fo,dashArrayX:[1,0],dashArrayY:[2,5],symbolSize:1,rotation:Math.PI/6},{color:Fo,symbol:"circle",dashArrayX:[[8,8],[0,8,8,0]],dashArrayY:[6,0],symbolSize:.8},{color:Fo,dashArrayX:[1,0],dashArrayY:[4,3],rotation:-Math.PI/4},{color:Fo,dashArrayX:[[6,6],[0,6,6,0]],dashArrayY:[6,0]},{color:Fo,dashArrayX:[[1,0],[1,6]],dashArrayY:[1,0,6,0],rotation:Math.PI/4},{color:Fo,symbol:"triangle",dashArrayX:[[9,9],[0,9,9,0]],dashArrayY:[7,2],symbolSize:.75}]}},textStyle:{fontFamily:mc.match(/^Win/)?"Microsoft YaHei":"sans-serif",fontSize:12,fontStyle:"normal",fontWeight:"normal"},blendMode:null,stateAnimation:{duration:300,easing:"cubicOut"},animation:"auto",animationDuration:1e3,animationDurationUpdate:500,animationEasing:"cubicInOut",animationEasingUpdate:"cubicInOut",animationThreshold:2e3,progressiveThreshold:3e3,progressive:400,hoverLayerThreshold:3e3,useUTC:!1},Op=z(["tooltip","label","itemName","itemId","itemGroupId","seriesName"]),Rp="original",Np="arrayRows",zp="objectRows",Ep="keyedColumns",Bp="typedArray",Fp="unknown",Vp="column",Hp="row",Gp={Must:1,Might:2,Not:3},Wp=_o();function Up(n,t,e){var r,o,a,i,s,l={},u=Xp(t);return u&&n&&(r=[],o=[],t=t.ecModel,t=Wp(t).datasetMap,u=u.uid+"_"+e.seriesLayoutBy,O(n=n.slice(),function(t,e){t=R(t)?t:n[e]={name:t};"ordinal"===t.type&&null==a&&(a=e,i=c(t)),l[t.name]=[]}),s=t.get(u)||t.set(u,{categoryWayDim:i,valueWayDim:0}),O(n,function(t,e){var n,i=t.name,t=c(t);null==a?(n=s.valueWayDim,h(l[i],n,t),h(o,n,t),s.valueWayDim+=t):a===e?(h(l[i],0,t),h(r,0,t)):(n=s.categoryWayDim,h(l[i],n,t),h(o,n,t),s.categoryWayDim+=t)}),r.length&&(l.itemName=r),o.length)&&(l.seriesName=o),l;function h(t,e,n){for(var i=0;i<n;i++)t.push(e+i)}function c(t){t=t.dimsDef;return t?t.length:1}}function Xp(t){if(!t.get("data",!0))return Mo(t.ecModel,"dataset",{index:t.get("datasetIndex",!0),id:t.get("datasetId",!0)},So).models[0]}function Yp(t,e){var n,i,r,o=t.data,a=t.sourceFormat,s=t.seriesLayoutBy,l=t.dimensionsDefine,u=t.startIndex,h=e;if(!yt(o)){if(l&&(R(l=l[h])?(i=l.name,r=l.type):V(l)&&(i=l)),null!=r)return"ordinal"===r?Gp.Must:Gp.Not;if(a===Np){var c=o;if(s===Hp){for(var p=c[h],d=0;d<(p||[]).length&&d<5;d++)if(null!=(n=_(p[u+d])))return n}else for(d=0;d<c.length&&d<5;d++){var f=c[u+d];if(f&&null!=(n=_(f[h])))return n}}else if(a===zp){var g=o;if(!i)return Gp.Not;for(d=0;d<g.length&&d<5;d++)if((m=g[d])&&null!=(n=_(m[i])))return n}else if(a===Ep){l=o;if(!i)return Gp.Not;if(!(p=l[i])||yt(p))return Gp.Not;for(d=0;d<p.length&&d<5;d++)if(null!=(n=_(p[d])))return n}else if(a===Rp)for(var y=o,d=0;d<y.length&&d<5;d++){var m,v=uo(m=y[d]);if(!F(v))return Gp.Not;if(null!=(n=_(v[h])))return n}}return Gp.Not;function _(t){var e=V(t);return null!=t&&isFinite(t)&&""!==t?e?Gp.Might:Gp.Not:e&&"-"!==t?Gp.Must:void 0}}var qp=z();var Zp,jp,Kp,$p=_o(),Qp=(_o(),Jp.prototype.getColorFromPalette=function(t,e,n){var i=ao(this.get("color",!0)),r=this.get("colorLayer",!0),o=this,a=$p;return a=a(e=e||o),o=a.paletteIdx||0,(e=a.paletteNameMap=a.paletteNameMap||{}).hasOwnProperty(t)?e[t]:(r=(r=n==null||!r?i:td(r,n))||i)&&r.length?(n=r[o],t&&(e[t]=n),a.paletteIdx=(o+1)%r.length,n):void 0},Jp.prototype.clearColorPalette=function(){var t,e;(e=$p)(t=this).paletteIdx=0,e(t).paletteNameMap={}},Jp);function Jp(){}function td(t,e){for(var n=t.length,i=0;i<n;i++)if(t[i].length>e)return t[i];return t[n-1]}var ed,nd="\0_ec_inner",id=(u(l,ed=xc),l.prototype.init=function(t,e,n,i,r,o){i=i||{},this.option=null,this._theme=new xc(i),this._locale=new xc(r),this._optionManager=o},l.prototype.setOption=function(t,e,n){e=ad(e);this._optionManager.setOption(t,n,e),this._resetOption(null,e)},l.prototype.resetOption=function(t,e){return this._resetOption(t,ad(e))},l.prototype._resetOption=function(t,e){var n,i=!1,r=this._optionManager;return t&&"recreate"!==t||(n=r.mountOption("recreate"===t),this.option&&"recreate"!==t?(this.restoreData(),this._mergeOption(n,e)):Kp(this,n),i=!0),"timeline"!==t&&"media"!==t||this.restoreData(),t&&"recreate"!==t&&"timeline"!==t||(n=r.getTimelineOption(this))&&(i=!0,this._mergeOption(n,e)),t&&"recreate"!==t&&"media"!==t||(n=r.getMediaOption(this)).length&&O(n,function(t){i=!0,this._mergeOption(t,e)},this),i},l.prototype.mergeOption=function(t){this._mergeOption(t,null)},l.prototype._mergeOption=function(i,t){var r=this.option,h=this._componentsMap,c=this._componentsCount,n=[],o=z(),p=t&&t.replaceMergeMainTypeMap;Wp(this).datasetMap=z(),O(i,function(t,e){null!=t&&(g.hasClass(e)?e&&(n.push(e),o.set(e,!0)):r[e]=null==r[e]?y(t):d(r[e],t,!0))}),p&&p.each(function(t,e){g.hasClass(e)&&!o.get(e)&&(n.push(e),o.set(e,!0))}),g.topologicalTravel(n,g.getAllClassMainTypes(),function(o){var a,t=function(t,e,n){return(e=(e=qp.get(e))&&e(t))?n.concat(e):n}(this,o,ao(i[o])),e=h.get(o),n=e?p&&p.get(o)?"replaceMerge":"normalMerge":"replaceAll",e=ho(e,t,n),s=(mo(e,o,g),r[o]=null,h.set(o,null),c.set(o,0),[]),l=[],u=0;O(e,function(t,e){var n=t.existing,i=t.newOption;if(i){var r=g.getClass(o,t.keyInfo.subType,!("series"===o));if(!r)return;if("tooltip"===o){if(a)return;a=!0}n&&n.constructor===r?(n.name=t.keyInfo.name,n.mergeOption(i,this),n.optionUpdated(i,!1)):(e=L({componentIndex:e},t.keyInfo),L(n=new r(i,this,this,e),e),t.brandNew&&(n.__requireNewView=!0),n.init(i,this,this),n.optionUpdated(null,!0))}else n&&(n.mergeOption({},this),n.optionUpdated({},!1));n?(s.push(n.option),l.push(n),u++):(s.push(void 0),l.push(void 0))},this),r[o]=s,h.set(o,l),c.set(o,u),"series"===o&&Zp(this)},this),this._seriesIndices||Zp(this)},l.prototype.getOption=function(){var a=y(this.option);return O(a,function(t,e){if(g.hasClass(e)){for(var n=ao(t),i=n.length,r=!1,o=i-1;0<=o;o--)n[o]&&!yo(n[o])?r=!0:(n[o]=null,r||i--);n.length=i,a[e]=n}}),delete a[nd],a},l.prototype.getTheme=function(){return this._theme},l.prototype.getLocaleModel=function(){return this._locale},l.prototype.setUpdatePayload=function(t){this._payload=t},l.prototype.getUpdatePayload=function(){return this._payload},l.prototype.getComponent=function(t,e){var n=this._componentsMap.get(t);if(n){t=n[e||0];if(t)return t;if(null==e)for(var i=0;i<n.length;i++)if(n[i])return n[i]}},l.prototype.queryComponents=function(t){var e,n,i,r,o,a=t.mainType;return a&&(e=t.index,n=t.id,i=t.name,r=this._componentsMap.get(a))&&r.length?(null!=e?(o=[],O(ao(e),function(t){r[t]&&o.push(r[t])})):o=null!=n?rd("id",n,r):null!=i?rd("name",i,r):ut(r,function(t){return!!t}),od(o,t)):[]},l.prototype.findComponents=function(t){var e,n=t.query,i=t.mainType,r=(r=i+"Index",o=i+"Id",e=i+"Name",!(n=n)||null==n[r]&&null==n[o]&&null==n[e]?null:{mainType:i,index:n[r],id:n[o],name:n[e]}),o=r?this.queryComponents(r):ut(this._componentsMap.get(i),function(t){return!!t});return n=od(o,t),t.filter?ut(n,t.filter):n},l.prototype.eachComponent=function(t,e,n){var i=this._componentsMap;if(S(t)){var r=e,o=t;i.each(function(t,e){for(var n=0;t&&n<t.length;n++){var i=t[n];i&&o.call(r,e,i,i.componentIndex)}})}else for(var a=V(t)?i.get(t):R(t)?this.findComponents(t):null,s=0;a&&s<a.length;s++){var l=a[s];l&&e.call(n,l,l.componentIndex)}},l.prototype.getSeriesByName=function(t){var e=fo(t,null);return ut(this._componentsMap.get("series"),function(t){return!!t&&null!=e&&t.name===e})},l.prototype.getSeriesByIndex=function(t){return this._componentsMap.get("series")[t]},l.prototype.getSeriesByType=function(e){return ut(this._componentsMap.get("series"),function(t){return!!t&&t.subType===e})},l.prototype.getSeries=function(){return ut(this._componentsMap.get("series"),function(t){return!!t})},l.prototype.getSeriesCount=function(){return this._componentsCount.get("series")},l.prototype.eachSeries=function(n,i){jp(this),O(this._seriesIndices,function(t){var e=this._componentsMap.get("series")[t];n.call(i,e,t)},this)},l.prototype.eachRawSeries=function(e,n){O(this._componentsMap.get("series"),function(t){t&&e.call(n,t,t.componentIndex)})},l.prototype.eachSeriesByType=function(n,i,r){jp(this),O(this._seriesIndices,function(t){var e=this._componentsMap.get("series")[t];e.subType===n&&i.call(r,e,t)},this)},l.prototype.eachRawSeriesByType=function(t,e,n){return O(this.getSeriesByType(t),e,n)},l.prototype.isSeriesFiltered=function(t){return jp(this),null==this._seriesIndicesMap.get(t.componentIndex)},l.prototype.getCurrentSeriesIndices=function(){return(this._seriesIndices||[]).slice()},l.prototype.filterSeries=function(n,i){jp(this);var r=[];O(this._seriesIndices,function(t){var e=this._componentsMap.get("series")[t];n.call(i,e,t)&&r.push(t)},this),this._seriesIndices=r,this._seriesIndicesMap=z(r)},l.prototype.restoreData=function(n){Zp(this);var t=this._componentsMap,i=[];t.each(function(t,e){g.hasClass(e)&&i.push(e)}),g.topologicalTravel(i,g.getAllClassMainTypes(),function(e){O(t.get(e),function(t){!t||"series"===e&&function(t,e){{var n,i;if(e)return n=e.seriesIndex,i=e.seriesId,e=e.seriesName,null!=n&&t.componentIndex!==n||null!=i&&t.id!==i||null!=e&&t.name!==e}}(t,n)||t.restoreData()})})},l.internalField=(Zp=function(t){var e=t._seriesIndices=[];O(t._componentsMap.get("series"),function(t){t&&e.push(t.componentIndex)}),t._seriesIndicesMap=z(e)},jp=function(t){},void(Kp=function(t,e){t.option={},t.option[nd]=1,t._componentsMap=z({series:[]}),t._componentsCount=z();var n,i,r=e.aria;R(r)&&null==r.enabled&&(r.enabled=!0),n=e,r=t._theme.option,i=n.color&&!n.colorLayer,O(r,function(t,e){"colorLayer"===e&&i||g.hasClass(e)||("object"==typeof t?n[e]=n[e]?d(n[e],t,!1):y(t):null==n[e]&&(n[e]=t))}),d(e,Lp,!1),t._mergeOption(e,null)})),l);function l(){return null!==ed&&ed.apply(this,arguments)||this}function rd(e,t,n){var i,r;return F(t)?(i=z(),O(t,function(t){null!=t&&null!=fo(t,null)&&i.set(t,!0)}),ut(n,function(t){return t&&i.get(t[e])})):(r=fo(t,null),ut(n,function(t){return t&&null!=r&&t[e]===r}))}function od(t,e){return e.hasOwnProperty("subType")?ut(t,function(t){return t&&t.subType===e.subType}):t}function ad(t){var e=z();return t&&O(ao(t.replaceMerge),function(t){e.set(t,!0)}),{replaceMergeMainTypeMap:e}}at(id,Qp);function sd(e){O(ld,function(t){this[t]=ct(e[t],e)},this)}var ld=["getDom","getZr","getWidth","getHeight","getDevicePixelRatio","dispatchAction","isSSR","isDisposed","on","off","getDataURL","getConnectedDataURL","getOption","getId","updateLabelLayout"],ud={},hd=(cd.prototype.create=function(n,i){var r=[];O(ud,function(t,e){t=t.create(n,i);r=r.concat(t||[])}),this._coordinateSystems=r},cd.prototype.update=function(e,n){O(this._coordinateSystems,function(t){t.update&&t.update(e,n)})},cd.prototype.getCoordinateSystems=function(){return this._coordinateSystems.slice()},cd.register=function(t,e){ud[t]=e},cd.get=function(t){return ud[t]},cd);function cd(){this._coordinateSystems=[]}var pd=/^(min|max)?(.+)$/,dd=(fd.prototype.setOption=function(t,e,n){t&&(O(ao(t.series),function(t){t&&t.data&&yt(t.data)&&Dt(t.data)}),O(ao(t.dataset),function(t){t&&t.source&&yt(t.source)&&Dt(t.source)})),t=y(t);var i=this._optionBackup,t=function(t,n,i){var e,r,o=[],a=t.baseOption,s=t.timeline,l=t.options,u=t.media,h=!!t.media,c=!!(l||s||a&&a.timeline);a?(r=a).timeline||(r.timeline=s):((c||h)&&(t.options=t.media=null),r=t);h&&F(u)&&O(u,function(t){t&&t.option&&(t.query?o.push(t):e=e||t)});function p(e){O(n,function(t){t(e,i)})}return p(r),O(l,p),O(o,function(t){return p(t.option)}),{baseOption:r,timelineOptions:l||[],mediaDefault:e,mediaList:o}}(t,e,!i);this._newBaseOption=t.baseOption,i?(t.timelineOptions.length&&(i.timelineOptions=t.timelineOptions),t.mediaList.length&&(i.mediaList=t.mediaList),t.mediaDefault&&(i.mediaDefault=t.mediaDefault)):this._optionBackup=t},fd.prototype.mountOption=function(t){var e=this._optionBackup;return this._timelineOptions=e.timelineOptions,this._mediaList=e.mediaList,this._mediaDefault=e.mediaDefault,this._currentMediaIndices=[],y(t?e.baseOption:this._newBaseOption)},fd.prototype.getTimelineOption=function(t){var e,n=this._timelineOptions;return e=n.length&&(t=t.getComponent("timeline"))?y(n[t.getCurrentIndex()]):e},fd.prototype.getMediaOption=function(t){var e=this._api.getWidth(),n=this._api.getHeight(),i=this._mediaList,r=this._mediaDefault,o=[],a=[];if(i.length||r){for(var s,l,u=0,h=i.length;u<h;u++)!function(t,e,n){var i={width:e,height:n,aspectratio:e/n},r=!0;return O(t,function(t,e){var n,e=e.match(pd);e&&e[1]&&e[2]&&(n=e[1],e=e[2].toLowerCase(),e=i[e],t=t,("min"===(n=n)?t<=e:"max"===n?e<=t:e===t)||(r=!1))}),r}(i[u].query,e,n)||o.push(u);(o=!o.length&&r?[-1]:o).length&&(s=o,l=this._currentMediaIndices,s.join(",")!==l.join(","))&&(a=B(o,function(t){return y((-1===t?r:i[t]).option)})),this._currentMediaIndices=o}return a},fd);function fd(t){this._timelineOptions=[],this._mediaList=[],this._currentMediaIndices=[],this._api=t}var gd=O,yd=R,md=["areaStyle","lineStyle","nodeStyle","linkStyle","chordStyle","label","labelLine"];function vd(t){var e=t&&t.itemStyle;if(e)for(var n=0,i=md.length;n<i;n++){var r=md[n],o=e.normal,a=e.emphasis;o&&o[r]&&(t[r]=t[r]||{},t[r].normal?d(t[r].normal,o[r]):t[r].normal=o[r],o[r]=null),a&&a[r]&&(t[r]=t[r]||{},t[r].emphasis?d(t[r].emphasis,a[r]):t[r].emphasis=a[r],a[r]=null)}}function _d(t,e,n){var i,r;t&&t[e]&&(t[e].normal||t[e].emphasis)&&(i=t[e].normal,r=t[e].emphasis,i&&(n?(t[e].normal=t[e].emphasis=null,E(t[e],i)):t[e]=i),r)&&(t.emphasis=t.emphasis||{},(t.emphasis[e]=r).focus&&(t.emphasis.focus=r.focus),r.blurScope)&&(t.emphasis.blurScope=r.blurScope)}function xd(t){_d(t,"itemStyle"),_d(t,"lineStyle"),_d(t,"areaStyle"),_d(t,"label"),_d(t,"labelLine"),_d(t,"upperLabel"),_d(t,"edgeLabel")}function wd(t,e){var n=yd(t)&&t[e],i=yd(n)&&n.textStyle;if(i)for(var r=0,o=lo.length;r<o;r++){var a=lo[r];i.hasOwnProperty(a)&&(n[a]=i[a])}}function bd(t){t&&(xd(t),wd(t,"label"),t.emphasis)&&wd(t.emphasis,"label")}function Sd(t){return F(t)?t:t?[t]:[]}function Md(t){return(F(t)?t[0]:t)||{}}function Td(e,t){gd(Sd(e.series),function(t){if(yd(t))if(yd(t)){vd(t),xd(t),wd(t,"label"),wd(t,"upperLabel"),wd(t,"edgeLabel"),t.emphasis&&(wd(t.emphasis,"label"),wd(t.emphasis,"upperLabel"),wd(t.emphasis,"edgeLabel"));var e=t.markPoint,n=(e&&(vd(e),bd(e)),t.markLine),i=(n&&(vd(n),bd(n)),t.markArea),r=(i&&bd(i),t.data);if("graph"===t.type){var r=r||t.nodes,o=t.links||t.edges;if(o&&!yt(o))for(var a=0;a<o.length;a++)bd(o[a]);O(t.categories,function(t){xd(t)})}if(r&&!yt(r))for(a=0;a<r.length;a++)bd(r[a]);if((e=t.markPoint)&&e.data)for(var s=e.data,a=0;a<s.length;a++)bd(s[a]);if((n=t.markLine)&&n.data)for(var l=n.data,a=0;a<l.length;a++)F(l[a])?(bd(l[a][0]),bd(l[a][1])):bd(l[a]);"gauge"===t.type?(wd(t,"axisLabel"),wd(t,"title"),wd(t,"detail")):"treemap"===t.type?(_d(t.breadcrumb,"itemStyle"),O(t.levels,function(t){xd(t)})):"tree"===t.type&&xd(t.leaves)}});var n=["xAxis","yAxis","radiusAxis","angleAxis","singleAxis","parallelAxis","radar"];t&&n.push("valueAxis","categoryAxis","logAxis","timeAxis"),gd(n,function(t){gd(Sd(e[t]),function(t){t&&(wd(t,"axisLabel"),wd(t.axisPointer,"label"))})}),gd(Sd(e.parallel),function(t){t=t&&t.parallelAxisDefault;wd(t,"axisLabel"),wd(t&&t.axisPointer,"label")}),gd(Sd(e.calendar),function(t){_d(t,"itemStyle"),wd(t,"dayLabel"),wd(t,"monthLabel"),wd(t,"yearLabel")}),gd(Sd(e.radar),function(t){wd(t,"name"),t.name&&null==t.axisName&&(t.axisName=t.name,delete t.name),null!=t.nameGap&&null==t.axisNameGap&&(t.axisNameGap=t.nameGap,delete t.nameGap)}),gd(Sd(e.geo),function(t){yd(t)&&(bd(t),gd(Sd(t.regions),function(t){bd(t)}))}),gd(Sd(e.timeline),function(t){bd(t),_d(t,"label"),_d(t,"itemStyle"),_d(t,"controlStyle",!0);t=t.data;F(t)&&O(t,function(t){R(t)&&(_d(t,"label"),_d(t,"itemStyle"))})}),gd(Sd(e.toolbox),function(t){_d(t,"iconStyle"),gd(t.feature,function(t){_d(t,"iconStyle")})}),wd(Md(e.axisPointer),"label"),wd(Md(e.tooltip).axisPointer,"label")}function Cd(e){e&&O(Id,function(t){t[0]in e&&!(t[1]in e)&&(e[t[1]]=e[t[0]])})}var Id=[["x","left"],["y","top"],["x2","right"],["y2","bottom"]],kd=["grid","geo","parallel","legend","toolbox","title","visualMap","dataZoom","timeline"],Dd=[["borderRadius","barBorderRadius"],["borderColor","barBorderColor"],["borderWidth","barBorderWidth"]];function Ad(t){var e=t&&t.itemStyle;if(e)for(var n=0;n<Dd.length;n++){var i=Dd[n][1],r=Dd[n][0];null!=e[i]&&(e[r]=e[i])}}function Pd(t){t&&"edge"===t.alignTo&&null!=t.margin&&null==t.edgeDistance&&(t.edgeDistance=t.margin)}function Ld(t){t&&t.downplay&&!t.blur&&(t.blur=t.downplay)}function Od(e,t){Td(e,t),e.series=ao(e.series),O(e.series,function(t){if(R(t)){var e,n=t.type;if("line"===n)null!=t.clipOverflow&&(t.clip=t.clipOverflow);else if("pie"===n||"gauge"===n){if(null!=t.clockWise&&(t.clockwise=t.clockWise),Pd(t.label),(e=t.data)&&!yt(e))for(var i=0;i<e.length;i++)Pd(e[i]);null!=t.hoverOffset&&(t.emphasis=t.emphasis||{},t.emphasis.scaleSize=null)&&(t.emphasis.scaleSize=t.hoverOffset)}else if("gauge"===n){var r=function(t,e){for(var n=e.split(","),i=t,r=0;r<n.length&&null!=(i=i&&i[n[r]]);r++);return i}(t,"pointer.color");if(null!=r){var o=t;var a="itemStyle.color";var s=void 0;for(var l,u=a.split(","),h=o,c=0;c<u.length-1;c++)null==h[l=u[c]]&&(h[l]={}),h=h[l];!s&&null!=h[u[c]]||(h[u[c]]=r)}}else if("bar"===n){if(Ad(t),Ad(t.backgroundStyle),Ad(t.emphasis),(e=t.data)&&!yt(e))for(i=0;i<e.length;i++)"object"==typeof e[i]&&(Ad(e[i]),Ad(e[i]&&e[i].emphasis))}else"sunburst"===n?((a=t.highlightPolicy)&&(t.emphasis=t.emphasis||{},t.emphasis.focus||(t.emphasis.focus=a)),Ld(t),function t(e,n){if(e)for(var i=0;i<e.length;i++)n(e[i]),e[i]&&t(e[i].children,n)}(t.data,Ld)):"graph"===n||"sankey"===n?(o=t)&&null!=o.focusNodeAdjacency&&(o.emphasis=o.emphasis||{},null==o.emphasis.focus)&&(o.emphasis.focus="adjacency"):"map"===n&&(t.mapType&&!t.map&&(t.map=t.mapType),t.mapLocation)&&E(t,t.mapLocation);null!=t.hoverAnimation&&(t.emphasis=t.emphasis||{},t.emphasis)&&null==t.emphasis.scale&&(t.emphasis.scale=t.hoverAnimation),Cd(t)}}),e.dataRange&&(e.visualMap=e.dataRange),O(kd,function(t){t=e[t];t&&O(t=F(t)?t:[t],function(t){Cd(t)})})}function Rd(v){O(v,function(p,d){var f=[],g=[NaN,NaN],t=[p.stackResultDimension,p.stackedOverDimension],y=p.data,m=p.isStackedByIndex;y.modify(t,function(t,e,n){var i,r,o=y.get(p.stackedDimension,n);if(isNaN(o))return g;m?r=y.getRawIndex(n):i=y.get(p.stackedByDimension,n);for(var a,s,l,u=NaN,h=d-1;0<=h;h--){var c=v[h];if(0<=(r=m?r:c.data.rawIndexOf(c.stackedByDimension,i))){c=c.data.getByRawIndex(c.stackResultDimension,r);if(0<=o&&0<c||o<=0&&c<0){a=o,s=c,l=void 0,l=Math.max(Ur(a),Ur(s)),a+=s,o=Vr<l?a:Wr(a,l),u=c;break}}}return f[0]=o,f[1]=u,f})})}var Nd,zd,Ed=function(t){this.data=t.data||(t.sourceFormat===Ep?{}:[]),this.sourceFormat=t.sourceFormat||Fp,this.seriesLayoutBy=t.seriesLayoutBy||Vp,this.startIndex=t.startIndex||0,this.dimensionsDetectedCount=t.dimensionsDetectedCount,this.metaRawOption=t.metaRawOption;var e=this.dimensionsDefine=t.dimensionsDefine;if(e)for(var n=0;n<e.length;n++){var i=e[n];null==i.type&&Yp(this,n)===Gp.Must&&(i.type="ordinal")}};function Bd(t){return t instanceof Ed}function Fd(t,e,n){n=n||Hd(t);var i=e.seriesLayoutBy,r=function(t,e,n,i,r){var o,a;if(!t)return{dimensionsDefine:Gd(r),startIndex:a,dimensionsDetectedCount:o};{var s;e===Np?(s=t,"auto"===i||null==i?Wd(function(t){null!=t&&"-"!==t&&(V(t)?null==a&&(a=1):a=0)},n,s,10):a=ft(i)?i:i?1:0,r||1!==a||(r=[],Wd(function(t,e){r[e]=null!=t?t+"":""},n,s,1/0)),o=r?r.length:n===Hp?s.length:s[0]?s[0].length:null):e===zp?r=r||function(t){var e,n=0;for(;n<t.length&&!(e=t[n++]););{var i;if(e)return i=[],O(e,function(t,e){i.push(e)}),i}}(t):e===Ep?r||(r=[],O(t,function(t,e){r.push(e)})):e===Rp&&(i=uo(t[0]),o=F(i)&&i.length||1)}return{startIndex:a,dimensionsDefine:Gd(r),dimensionsDetectedCount:o}}(t,n,i,e.sourceHeader,e.dimensions);return new Ed({data:t,sourceFormat:n,seriesLayoutBy:i,dimensionsDefine:r.dimensionsDefine,startIndex:r.startIndex,dimensionsDetectedCount:r.dimensionsDetectedCount,metaRawOption:y(e)})}function Vd(t){return new Ed({data:t,sourceFormat:yt(t)?Bp:Rp})}function Hd(t){var e=Fp;if(yt(t))e=Bp;else if(F(t)){0===t.length&&(e=Np);for(var n=0,i=t.length;n<i;n++){var r=t[n];if(null!=r){if(F(r)){e=Np;break}if(R(r)){e=zp;break}}}}else if(R(t))for(var o in t)if(zt(t,o)&&st(t[o])){e=Ep;break}return e}function Gd(t){var i;if(t)return i=z(),B(t,function(t,e){var n,t={name:(t=R(t)?t:{name:t}).name,displayName:t.displayName,type:t.type};return null!=t.name&&(t.name+="",null==t.displayName&&(t.displayName=t.name),(n=i.get(t.name))?t.name+="-"+n.count++:i.set(t.name,{count:1})),t})}function Wd(t,e,n,i){if(e===Hp)for(var r=0;r<n.length&&r<i;r++)t(n[r]?n[r][0]:null,r);else for(var o=n[0]||[],r=0;r<o.length&&r<i;r++)t(o[r],r)}function Ud(t){t=t.sourceFormat;return t===zp||t===Ep}jd.prototype.getSource=function(){return this._source},jd.prototype.count=function(){return 0},jd.prototype.getItem=function(t,e){},jd.prototype.appendData=function(t){},jd.prototype.clean=function(){},jd.protoInitialize=((Nh=jd.prototype).pure=!1,void(Nh.persistent=!0)),jd.internalField=(zd=function(t,e,n){var i,r=n.sourceFormat,o=n.seriesLayoutBy,a=n.startIndex,n=n.dimensionsDefine;L(t,Nd[sf(r,o)]),r===Bp?(t.getItem=Xd,t.count=qd,t.fillStorage=Yd):(i=Jd(r,o),t.getItem=ct(i,null,e,a,n),i=nf(r,o),t.count=ct(i,null,e,a,n))},Xd=function(t,e){t-=this._offset,e=e||[];for(var n=this._data,i=this._dimSize,r=i*t,o=0;o<i;o++)e[o]=n[r+o];return e},Yd=function(t,e,n,i){for(var r=this._data,o=this._dimSize,a=0;a<o;a++){for(var s=i[a],l=null==s[0]?1/0:s[0],u=null==s[1]?-1/0:s[1],h=e-t,c=n[a],p=0;p<h;p++){var d=r[p*o+a];(c[t+p]=d)<l&&(l=d),u<d&&(u=d)}s[0]=l,s[1]=u}},qd=function(){return this._data?this._data.length/this._dimSize:0},(Nh={})[Np+"_"+Vp]={pure:!0,appendData:Kd},Nh[Np+"_"+Hp]={pure:!0,appendData:function(){throw new Error('Do not support appendData when set seriesLayoutBy: "row".')}},Nh[zp]={pure:!0,appendData:Kd},Nh[Ep]={pure:!0,appendData:function(t){var r=this._data;O(t,function(t,e){for(var n=r[e]||(r[e]=[]),i=0;i<(t||[]).length;i++)n.push(t[i])})}},Nh[Rp]={appendData:Kd},Nh[Bp]={persistent:!1,pure:!0,appendData:function(t){this._data=t},clean:function(){this._offset+=this.count(),this._data=null}},void(Nd=Nh));var Xd,Yd,qd,Zd=jd;function jd(t,e){var t=Bd(t)?t:Vd(t),n=(this._source=t,this._data=t.data);t.sourceFormat===Bp&&(this._offset=0,this._dimSize=e,this._data=n),zd(this,n,t)}function Kd(t){for(var e=0;e<t.length;e++)this._data.push(t[e])}function $d(t,e,n,i){return t[i]}(zc={})[Np+"_"+Vp]=function(t,e,n,i){return t[i+e]},zc[Np+"_"+Hp]=function(t,e,n,i,r){i+=e;for(var o=r||[],a=t,s=0;s<a.length;s++){var l=a[s];o[s]=l?l[i]:null}return o},zc[zp]=$d,zc[Ep]=function(t,e,n,i,r){for(var o=r||[],a=0;a<n.length;a++){var s=t[n[a].name];o[a]=s?s[i]:null}return o},zc[Rp]=$d;var Qd=zc;function Jd(t,e){return Qd[sf(t,e)]}function tf(t,e,n){return t.length}(dc={})[Np+"_"+Vp]=function(t,e,n){return Math.max(0,t.length-e)},dc[Np+"_"+Hp]=function(t,e,n){t=t[0];return t?Math.max(0,t.length-e):0},dc[zp]=tf,dc[Ep]=function(t,e,n){t=t[n[0].name];return t?t.length:0},dc[Rp]=tf;var ef=dc;function nf(t,e){return ef[sf(t,e)]}function rf(t,e,n){return t[e]}(Fo={})[Np]=rf,Fo[zp]=function(t,e,n){return t[n]},Fo[Ep]=rf,Fo[Rp]=function(t,e,n){t=uo(t);return t instanceof Array?t[e]:t},Fo[Bp]=rf;var of=Fo;function af(t){return of[t]}function sf(t,e){return t===Np?t+"_"+e:t}function lf(t,e,n){if(t){var i,r,e=t.getRawDataItem(e);if(null!=e)return i=(r=t.getStore()).getSource().sourceFormat,null!=n?(t=t.getDimensionIndex(n),n=r.getDimensionProperty(t),af(i)(e,t,n)):(r=e,i===Rp?uo(e):r)}}var uf=/\{@(.+?)\}/g,mc=(hf.prototype.getDataParams=function(t,e){var n=this.getData(e),i=this.getRawValue(t,e),r=n.getRawIndex(t),o=n.getName(t),a=n.getRawDataItem(t),s=n.getItemVisual(t,"style"),t=s&&s[n.getItemVisual(t,"drawType")||"fill"],s=s&&s.stroke,l=this.mainType,u="series"===l,n=n.userOutput&&n.userOutput.get();return{componentType:l,componentSubType:this.subType,componentIndex:this.componentIndex,seriesType:u?this.subType:null,seriesIndex:this.seriesIndex,seriesId:u?this.id:null,seriesName:u?this.name:null,name:o,dataIndex:r,data:a,dataType:e,value:i,color:t,borderColor:s,dimensionNames:n?n.fullDimensions:null,encode:n?n.encode:null,$vars:["seriesName","name","value"]}},hf.prototype.getFormattedLabel=function(i,t,e,n,r,o){t=t||"normal";var a=this.getData(e),e=this.getDataParams(i,e);return o&&(e.value=o.interpolatedValue),null!=n&&F(e.value)&&(e.value=e.value[n]),S(r=r||a.getItemModel(i).get("normal"===t?["label","formatter"]:[t,"label","formatter"]))?(e.status=t,e.dimensionIndex=n,r(e)):V(r)?dp(r,e).replace(uf,function(t,e){var n=e.length,n=("["===e.charAt(0)&&"]"===e.charAt(n-1)&&(e=+e.slice(1,n-1)),lf(a,i,e));return null!=(n=o&&F(o.interpolatedValue)&&0<=(e=a.getDimensionIndex(e))?o.interpolatedValue[e]:n)?n+"":""}):void 0},hf.prototype.getRawValue=function(t,e){return lf(this.getData(e),t)},hf.prototype.formatTooltip=function(t,e,n){},hf);function hf(){}function cf(t){var e,n;return R(t)?t.type&&(n=t):e=t,{text:e,frag:n}}function pf(t){return new df(t)}ff.prototype.perform=function(t){var e,n,i=this._upstream,r=t&&t.skip,o=(this._dirty&&i&&((o=this.context).data=o.outputData=i.context.outputData),this.__pipeline&&(this.__pipeline.currentTask=this),this._plan&&!r&&(e=this._plan(this.context)),u(this._modBy)),a=this._modDataCount||0,s=u(t&&t.modBy),l=t&&t.modDataCount||0;function u(t){return t=1<=t?t:1}o===s&&a===l||(e="reset"),!this._dirty&&"reset"!==e||(this._dirty=!1,n=this._doReset(r)),this._modBy=s,this._modDataCount=l;o=t&&t.step;if(this._dueEnd=i?i._outputDueEnd:this._count?this._count(this.context):1/0,this._progress){var h=this._dueIndex,c=Math.min(null!=o?this._dueIndex+o:1/0,this._dueEnd);if(!r&&(n||h<c)){var p=this._progress;if(F(p))for(var d=0;d<p.length;d++)this._doProgress(p[d],h,c,s,l);else this._doProgress(p,h,c,s,l)}this._dueIndex=c;a=null!=this._settedOutputEnd?this._settedOutputEnd:c;this._outputDueEnd=a}else this._dueIndex=this._outputDueEnd=null!=this._settedOutputEnd?this._settedOutputEnd:this._dueEnd;return this.unfinished()},ff.prototype.dirty=function(){this._dirty=!0,this._onDirty&&this._onDirty(this.context)},ff.prototype._doProgress=function(t,e,n,i,r){wf.reset(e,n,i,r),this._callingProgress=t,this._callingProgress({start:e,end:n,count:n-e,next:wf.next},this.context)},ff.prototype._doReset=function(t){this._dueIndex=this._outputDueEnd=this._dueEnd=0,this._settedOutputEnd=null,!t&&this._reset&&((e=this._reset(this.context))&&e.progress&&(n=e.forceFirstProgress,e=e.progress),F(e))&&!e.length&&(e=null),this._progress=e,this._modBy=this._modDataCount=null;var e,n,t=this._downstream;return t&&t.dirty(),n},ff.prototype.unfinished=function(){return this._progress&&this._dueIndex<this._dueEnd},ff.prototype.pipe=function(t){this._downstream===t&&!this._dirty||((this._downstream=t)._upstream=this,t.dirty())},ff.prototype.dispose=function(){this._disposed||(this._upstream&&(this._upstream._downstream=null),this._downstream&&(this._downstream._upstream=null),this._dirty=!1,this._disposed=!0)},ff.prototype.getUpstream=function(){return this._upstream},ff.prototype.getDownstream=function(){return this._downstream},ff.prototype.setOutputEnd=function(t){this._outputDueEnd=this._settedOutputEnd=t};var df=ff;function ff(t){this._reset=(t=t||{}).reset,this._plan=t.plan,this._count=t.count,this._onDirty=t.onDirty,this._dirty=!0}var gf,yf,mf,vf,_f,xf,wf=xf={reset:function(t,e,n,i){yf=t,gf=e,mf=n,vf=i,_f=Math.ceil(vf/mf),xf.next=1<mf&&0<vf?Sf:bf}};function bf(){return yf<gf?yf++:null}function Sf(){var t=yf%_f*mf+Math.ceil(yf/_f),t=gf<=yf?null:t<vf?t:yf;return yf++,t}function Mf(t,e){e=e&&e.type;return"ordinal"===e?t:null==(t="time"!==e||ft(t)||null==t||"-"===t?t:+Kr(t))||""===t?NaN:+t}var Tf=z({number:function(t){return parseFloat(t)},time:function(t){return+Kr(t)},trim:function(t){return V(t)?It(t):t}});function Cf(t){return Tf.get(t)}var If={lt:function(t,e){return t<e},lte:function(t,e){return t<=e},gt:function(t,e){return e<t},gte:function(t,e){return e<=t}},kf=(Df.prototype.evaluate=function(t){return ft(t)?this._opFn(t,this._rvalFloat):this._opFn(to(t),this._rvalFloat)},Df);function Df(t,e){ft(e)||f(""),this._opFn=If[t],this._rvalFloat=to(e)}Pf.prototype.evaluate=function(t,e){var n=ft(t)?t:to(t),i=ft(e)?e:to(e),r=isNaN(n),o=isNaN(i);return r&&(n=this._incomparable),o&&(i=this._incomparable),r&&o&&(r=V(t),o=V(e),r&&(n=o?t:0),o)&&(i=r?e:0),n<i?this._resultLT:i<n?-this._resultLT:0};var Af=Pf;function Pf(t,e){t="desc"===t;this._resultLT=t?1:-1,this._incomparable="min"===(e=null==e?t?"min":"max":e)?-1/0:1/0}Of.prototype.evaluate=function(t){var e,n=t===this._rval;return n||(e=typeof t)===this._rvalTypeof||"number"!=e&&"number"!==this._rvalTypeof||(n=to(t)===this._rvalFloat),this._isEQ?n:!n};var Lf=Of;function Of(t,e){this._rval=e,this._isEQ=t,this._rvalTypeof=typeof e,this._rvalFloat=to(e)}Nf.prototype.getRawData=function(){throw new Error("not supported")},Nf.prototype.getRawDataItem=function(t){throw new Error("not supported")},Nf.prototype.cloneRawData=function(){},Nf.prototype.getDimensionInfo=function(t){},Nf.prototype.cloneAllDimensionInfo=function(){},Nf.prototype.count=function(){},Nf.prototype.retrieveValue=function(t,e){},Nf.prototype.retrieveValueFromItem=function(t,e){},Nf.prototype.convertValue=Mf;var Rf=Nf;function Nf(){}function zf(t){return Gf(t.sourceFormat)||f(""),t.data}function Ef(t){var e=t.sourceFormat,n=t.data;if(Gf(e)||f(""),e===Np){for(var i=[],r=0,o=n.length;r<o;r++)i.push(n[r].slice());return i}if(e===zp){for(i=[],r=0,o=n.length;r<o;r++)i.push(L({},n[r]));return i}}function Bf(t,e,n){if(null!=n)return ft(n)||!isNaN(n)&&!zt(e,n)?t[n]:zt(e,n)?e[n]:void 0}function Ff(t){return y(t)}var Vf=z();function Hf(t,e){var n=ao(t),t=n.length;t||f("");for(var i=0,r=t;i<r;i++)e=function(t,i){i.length||f("");R(t)||f("");var e=t.type,d=Vf.get(e);d||f("");e=B(i,function(t){var e=t,t=d,n=new Rf,i=e.data,r=n.sourceFormat=e.sourceFormat,o=e.startIndex,a=(e.seriesLayoutBy!==Vp&&f(""),[]),s={};if(h=e.dimensionsDefine)O(h,function(t,e){var n=t.name,e={index:e,name:n,displayName:t.displayName};a.push(e),null!=n&&(zt(s,n)&&f(""),s[n]=e)});else for(var l=0;l<e.dimensionsDetectedCount;l++)a.push({index:l});var u=Jd(r,Vp),h=(t.__isBuiltIn&&(n.getRawDataItem=function(t){return u(i,o,a,t)},n.getRawData=ct(zf,null,e)),n.cloneRawData=ct(Ef,null,e),nf(r,Vp)),c=(n.count=ct(h,null,i,o,a),af(r)),p=(n.retrieveValue=function(t,e){t=u(i,o,a,t);return p(t,e)},n.retrieveValueFromItem=function(t,e){var n;return null!=t&&(n=a[e])?c(t,e,n.name):void 0});return n.getDimensionInfo=ct(Bf,null,a,s),n.cloneAllDimensionInfo=ct(Ff,null,a),n});return B(ao(d.transform({upstream:e[0],upstreamList:e,config:y(t.config)})),function(t,e){R(t)||f(""),t.data||f("");Gf(Hd(t.data))||f("");var n=i[0],e=n&&0===e&&!t.dimensions?((e=n.startIndex)&&(t.data=n.data.slice(0,e).concat(t.data)),{seriesLayoutBy:Vp,sourceHeader:e,dimensions:n.metaRawOption.dimensions}):{seriesLayoutBy:Vp,sourceHeader:0,dimensions:t.dimensions};return Fd(t.data,e,null)})}(n[i],e),i!==r-1&&(e.length=Math.max(e.length,1));return e}function Gf(t){return t===Np||t===zp}var Wf,Nh="undefined",Uf=typeof Uint32Array==Nh?Array:Uint32Array,Xf=typeof Uint16Array==Nh?Array:Uint16Array,Yf=typeof Int32Array==Nh?Array:Int32Array,zc=typeof Float64Array==Nh?Array:Float64Array,qf={float:zc,int:Yf,ordinal:Array,number:Array,time:zc};function Zf(t){return 65535<t?Uf:Xf}function jf(){return[1/0,-1/0]}function Kf(t,e,n,i,r){n=qf[n||"float"];if(r){var o=t[e],a=o&&o.length;if(a!==i){for(var s=new n(i),l=0;l<a;l++)s[l]=o[l];t[e]=s}}else t[e]=new n(i)}h.prototype.initData=function(t,e,n){this._provider=t,this._chunks=[],this._indices=null,this.getRawIndex=this._getRawIdxIdentity;var i=t.getSource(),r=this.defaultDimValueGetter=Wf[i.sourceFormat];this._dimValueGetter=n||r,this._rawExtent=[],Ud(i),this._dimensions=B(e,function(t){return{type:t.type,property:t.property}}),this._initDataFromProvider(0,t.count())},h.prototype.getProvider=function(){return this._provider},h.prototype.getSource=function(){return this._provider.getSource()},h.prototype.ensureCalculationDimension=function(t,e){var n=this._calcDimNameToIdx,i=this._dimensions,r=n.get(t);if(null!=r){if(i[r].type===e)return r}else r=i.length;return i[r]={type:e},n.set(t,r),this._chunks[r]=new qf[e||"float"](this._rawCount),this._rawExtent[r]=jf(),r},h.prototype.collectOrdinalMeta=function(t,e){for(var n=this._chunks[t],i=this._dimensions[t],r=this._rawExtent,o=i.ordinalOffset||0,a=n.length,s=(0===o&&(r[t]=jf()),r[t]),l=o;l<a;l++){var u=n[l]=e.parseAndCollect(n[l]);s[0]=Math.min(u,s[0]),s[1]=Math.max(u,s[1])}i.ordinalMeta=e,i.ordinalOffset=a,i.type="ordinal"},h.prototype.getOrdinalMeta=function(t){return this._dimensions[t].ordinalMeta},h.prototype.getDimensionProperty=function(t){t=this._dimensions[t];return t&&t.property},h.prototype.appendData=function(t){var e=this._provider,n=this.count(),t=(e.appendData(t),e.count());return e.persistent||(t+=n),n<t&&this._initDataFromProvider(n,t,!0),[n,t]},h.prototype.appendValues=function(t,e){for(var n=this._chunks,i=this._dimensions,r=i.length,o=this._rawExtent,a=this.count(),s=a+Math.max(t.length,e||0),l=0;l<r;l++)Kf(n,l,(d=i[l]).type,s,!0);for(var u=[],h=a;h<s;h++)for(var c=h-a,p=0;p<r;p++){var d=i[p],f=Wf.arrayRows.call(this,t[c]||u,d.property,c,p),g=(n[p][h]=f,o[p]);f<g[0]&&(g[0]=f),f>g[1]&&(g[1]=f)}return{start:a,end:this._rawCount=this._count=s}},h.prototype._initDataFromProvider=function(t,e,n){for(var i=this._provider,r=this._chunks,o=this._dimensions,a=o.length,s=this._rawExtent,l=B(o,function(t){return t.property}),u=0;u<a;u++){var h=o[u];s[u]||(s[u]=jf()),Kf(r,u,h.type,e,n)}if(i.fillStorage)i.fillStorage(t,e,r,s);else for(var c=[],p=t;p<e;p++)for(var c=i.getItem(p,c),d=0;d<a;d++){var f=r[d],g=this._dimValueGetter(c,l[d],p,d),f=(f[p]=g,s[d]);g<f[0]&&(f[0]=g),g>f[1]&&(f[1]=g)}!i.persistent&&i.clean&&i.clean(),this._rawCount=this._count=e,this._extent=[]},h.prototype.count=function(){return this._count},h.prototype.get=function(t,e){return 0<=e&&e<this._count&&(t=this._chunks[t])?t[this.getRawIndex(e)]:NaN},h.prototype.getValues=function(t,e){var n=[],i=[];if(null==e){e=t,t=[];for(var r=0;r<this._dimensions.length;r++)i.push(r)}else i=t;for(var r=0,o=i.length;r<o;r++)n.push(this.get(i[r],e));return n},h.prototype.getByRawIndex=function(t,e){return 0<=e&&e<this._rawCount&&(t=this._chunks[t])?t[e]:NaN},h.prototype.getSum=function(t){var e=0;if(this._chunks[t])for(var n=0,i=this.count();n<i;n++){var r=this.get(t,n);isNaN(r)||(e+=r)}return e},h.prototype.getMedian=function(t){var e=[],t=(this.each([t],function(t){isNaN(t)||e.push(t)}),e.sort(function(t,e){return t-e})),n=this.count();return 0===n?0:n%2==1?t[(n-1)/2]:(t[n/2]+t[n/2-1])/2},h.prototype.indexOfRawIndex=function(t){if(!(t>=this._rawCount||t<0)){if(!this._indices)return t;var e=this._indices,n=e[t];if(null!=n&&n<this._count&&n===t)return t;for(var i=0,r=this._count-1;i<=r;){var o=(i+r)/2|0;if(e[o]<t)i=1+o;else{if(!(e[o]>t))return o;r=o-1}}}return-1},h.prototype.indicesOfNearest=function(t,e,n){var i=this._chunks[t],r=[];if(i){null==n&&(n=1/0);for(var o=1/0,a=-1,s=0,l=0,u=this.count();l<u;l++){var h=e-i[this.getRawIndex(l)],c=Math.abs(h);c<=n&&((c<o||c===o&&0<=h&&a<0)&&(o=c,a=h,s=0),h===a)&&(r[s++]=l)}r.length=s}return r},h.prototype.getIndices=function(){var t=this._indices;if(t){var e=t.constructor,n=this._count;if(e===Array)for(var i=new e(n),r=0;r<n;r++)i[r]=t[r];else i=new e(t.buffer,0,n)}else{i=new(e=Zf(this._rawCount))(this.count());for(r=0;r<i.length;r++)i[r]=r}return i},h.prototype.filter=function(t,e){if(!this._count)return this;for(var n=this.clone(),i=n.count(),r=new(Zf(n._rawCount))(i),o=[],a=t.length,s=0,l=t[0],u=n._chunks,h=0;h<i;h++){var c=void 0,p=n.getRawIndex(h);if(0===a)c=e(h);else if(1===a)c=e(u[l][p],h);else{for(var d=0;d<a;d++)o[d]=u[t[d]][p];o[d]=h,c=e.apply(null,o)}c&&(r[s++]=p)}return s<i&&(n._indices=r),n._count=s,n._extent=[],n._updateGetRawIdx(),n},h.prototype.selectRange=function(t){var e=this.clone(),n=e._count;if(!n)return this;var i=ht(t),r=i.length;if(!r)return this;var o=e.count(),a=new(Zf(e._rawCount))(o),s=0,l=i[0],u=t[l][0],h=t[l][1],c=e._chunks,l=!1;if(!e._indices){var p=0;if(1===r){for(var d=c[i[0]],f=0;f<n;f++)(u<=(v=d[f])&&v<=h||isNaN(v))&&(a[s++]=p),p++;l=!0}else if(2===r){for(var d=c[i[0]],g=c[i[1]],y=t[i[1]][0],m=t[i[1]][1],f=0;f<n;f++){var v=d[f],_=g[f];(u<=v&&v<=h||isNaN(v))&&(y<=_&&_<=m||isNaN(_))&&(a[s++]=p),p++}l=!0}}if(!l)if(1===r)for(f=0;f<o;f++){var x=e.getRawIndex(f);(u<=(v=c[i[0]][x])&&v<=h||isNaN(v))&&(a[s++]=x)}else for(f=0;f<o;f++){for(var w=!0,x=e.getRawIndex(f),b=0;b<r;b++){var S=i[b];((v=c[S][x])<t[S][0]||v>t[S][1])&&(w=!1)}w&&(a[s++]=e.getRawIndex(f))}return s<o&&(e._indices=a),e._count=s,e._extent=[],e._updateGetRawIdx(),e},h.prototype.map=function(t,e){var n=this.clone(t);return this._updateDims(n,t,e),n},h.prototype.modify=function(t,e){this._updateDims(this,t,e)},h.prototype._updateDims=function(t,e,n){for(var i=t._chunks,r=[],o=e.length,a=t.count(),s=[],l=t._rawExtent,u=0;u<e.length;u++)l[e[u]]=jf();for(var h=0;h<a;h++){for(var c=t.getRawIndex(h),p=0;p<o;p++)s[p]=i[e[p]][c];s[o]=h;var d=n&&n.apply(null,s);if(null!=d){"object"!=typeof d&&(r[0]=d,d=r);for(u=0;u<d.length;u++){var f=e[u],g=d[u],y=l[f],f=i[f];f&&(f[c]=g),g<y[0]&&(y[0]=g),g>y[1]&&(y[1]=g)}}}},h.prototype.lttbDownSample=function(t,e){var n,i=this.clone([t],!0),r=i._chunks[t],o=this.count(),a=0,s=Math.floor(1/e),l=this.getRawIndex(0),u=new(Zf(this._rawCount))(Math.ceil(o/s)+2);u[a++]=l;for(var h=1;h<o-1;h+=s){for(var c=Math.min(h+s,o-1),p=Math.min(h+2*s,o),d=(p+c)/2,f=0,g=c;g<p;g++){var y=r[b=this.getRawIndex(g)];isNaN(y)||(f+=y)}f/=p-c;for(var c=h,m=Math.min(h+s,o),v=h-1,_=r[l],x=-1,w=c,g=c;g<m;g++){var b,y=r[b=this.getRawIndex(g)];isNaN(y)||x<(n=Math.abs((v-d)*(y-_)-(v-g)*(f-_)))&&(x=n,w=b)}l=u[a++]=w}return u[a++]=this.getRawIndex(o-1),i._count=a,i._indices=u,i.getRawIndex=this._getRawIdx,i},h.prototype.downSample=function(t,e,n,i){for(var r=this.clone([t],!0),o=r._chunks,a=[],s=Math.floor(1/e),l=o[t],u=this.count(),h=r._rawExtent[t]=jf(),c=new(Zf(this._rawCount))(Math.ceil(u/s)),p=0,d=0;d<u;d+=s){u-d<s&&(a.length=s=u-d);for(var f=0;f<s;f++){var g=this.getRawIndex(d+f);a[f]=l[g]}var y=n(a),m=this.getRawIndex(Math.min(d+i(a,y)||0,u-1));(l[m]=y)<h[0]&&(h[0]=y),y>h[1]&&(h[1]=y),c[p++]=m}return r._count=p,r._indices=c,r._updateGetRawIdx(),r},h.prototype.each=function(t,e){if(this._count)for(var n=t.length,i=this._chunks,r=0,o=this.count();r<o;r++){var a=this.getRawIndex(r);switch(n){case 0:e(r);break;case 1:e(i[t[0]][a],r);break;case 2:e(i[t[0]][a],i[t[1]][a],r);break;default:for(var s=0,l=[];s<n;s++)l[s]=i[t[s]][a];l[s]=r,e.apply(null,l)}}},h.prototype.getDataExtent=function(t){var e=this._chunks[t],n=jf();if(!e)return n;var i=this.count();if(!this._indices)return this._rawExtent[t].slice();if(r=this._extent[t])return r.slice();for(var r,o=(r=n)[0],a=r[1],s=0;s<i;s++){var l=e[this.getRawIndex(s)];l<o&&(o=l),a<l&&(a=l)}return this._extent[t]=r=[o,a]},h.prototype.getRawDataItem=function(t){var e=this.getRawIndex(t);if(this._provider.persistent)return this._provider.getItem(e);for(var n=[],i=this._chunks,r=0;r<i.length;r++)n.push(i[r][e]);return n},h.prototype.clone=function(t,e){var n,i,r=new h,o=this._chunks,a=t&&lt(t,function(t,e){return t[e]=!0,t},{});if(a)for(var s=0;s<o.length;s++)r._chunks[s]=a[s]?(n=o[s],i=void 0,(i=n.constructor)===Array?n.slice():new i(n)):o[s];else r._chunks=o;return this._copyCommonProps(r),e||(r._indices=this._cloneIndices()),r._updateGetRawIdx(),r},h.prototype._copyCommonProps=function(t){t._count=this._count,t._rawCount=this._rawCount,t._provider=this._provider,t._dimensions=this._dimensions,t._extent=y(this._extent),t._rawExtent=y(this._rawExtent)},h.prototype._cloneIndices=function(){if(this._indices){var t=this._indices.constructor,e=void 0;if(t===Array)for(var n=this._indices.length,e=new t(n),i=0;i<n;i++)e[i]=this._indices[i];else e=new t(this._indices);return e}return null},h.prototype._getRawIdxIdentity=function(t){return t},h.prototype._getRawIdx=function(t){return t<this._count&&0<=t?this._indices[t]:-1},h.prototype._updateGetRawIdx=function(){this.getRawIndex=this._indices?this._getRawIdx:this._getRawIdxIdentity},h.internalField=void(Wf={arrayRows:Qf,objectRows:function(t,e,n,i){return Mf(t[e],this._dimensions[i])},keyedColumns:Qf,original:function(t,e,n,i){t=t&&(null==t.value?t:t.value);return Mf(t instanceof Array?t[i]:t,this._dimensions[i])},typedArray:function(t,e,n,i){return t[i]}});var $f=h;function h(){this._chunks=[],this._rawExtent=[],this._extent=[],this._count=0,this._rawCount=0,this._calcDimNameToIdx=z()}function Qf(t,e,n,i){return Mf(t[i],this._dimensions[i])}tg.prototype.dirty=function(){this._setLocalSource([],[]),this._storeList=[],this._dirty=!0},tg.prototype._setLocalSource=function(t,e){this._sourceList=t,this._upstreamSignList=e,this._versionSignBase++,9e10<this._versionSignBase&&(this._versionSignBase=0)},tg.prototype._getVersionSign=function(){return this._sourceHost.uid+"_"+this._versionSignBase},tg.prototype.prepareSource=function(){this._isDirty()&&(this._createSource(),this._dirty=!1)},tg.prototype._createSource=function(){this._setLocalSource([],[]);var t,e,n,i,r,o,a,s=this._sourceHost,l=this._getUpstreamSourceManagers(),u=!!l.length;ng(s)?(i=s,r=t=o=void 0,e=u?((e=l[0]).prepareSource(),o=(r=e.getSource()).data,t=r.sourceFormat,[e._getVersionSign()]):(t=yt(o=i.get("data",!0))?Bp:Rp,[]),i=this._getSourceMetaRawOption()||{},r=r&&r.metaRawOption||{},a=N(i.seriesLayoutBy,r.seriesLayoutBy)||null,n=N(i.sourceHeader,r.sourceHeader),i=N(i.dimensions,r.dimensions),r=a!==r.seriesLayoutBy||!!n!=!!r.sourceHeader||i?[Fd(o,{seriesLayoutBy:a,sourceHeader:n,dimensions:i},t)]:[]):(o=s,e=u?(r=(a=this._applyTransform(l)).sourceList,a.upstreamSignList):(r=[Fd(o.get("source",!0),this._getSourceMetaRawOption(),null)],[])),this._setLocalSource(r,e)},tg.prototype._applyTransform=function(t){var e,n=this._sourceHost,i=n.get("transform",!0),r=n.get("fromTransformResult",!0),o=(null!=r&&1!==t.length&&ig(""),[]),a=[];return O(t,function(t){t.prepareSource();var e=t.getSource(r||0);null==r||e||ig(""),o.push(e),a.push(t._getVersionSign())}),i?e=Hf(i,o,n.componentIndex):null!=r&&(e=[new Ed({data:(t=o[0]).data,sourceFormat:t.sourceFormat,seriesLayoutBy:t.seriesLayoutBy,dimensionsDefine:y(t.dimensionsDefine),startIndex:t.startIndex,dimensionsDetectedCount:t.dimensionsDetectedCount})]),{sourceList:e,upstreamSignList:a}},tg.prototype._isDirty=function(){if(this._dirty)return!0;for(var t=this._getUpstreamSourceManagers(),e=0;e<t.length;e++){var n=t[e];if(n._isDirty()||this._upstreamSignList[e]!==n._getVersionSign())return!0}},tg.prototype.getSource=function(t){var e=this._sourceList[t=t||0];return e||(e=this._getUpstreamSourceManagers())[0]&&e[0].getSource(t)},tg.prototype.getSharedDataStore=function(t){var e=t.makeStoreSchema();return this._innerGetDataStore(e.dimensions,t.source,e.hash)},tg.prototype._innerGetDataStore=function(t,e,n){var i,r=this._storeList,o=r[0],r=(o=o||(r[0]={}))[n];return r||(i=this._getUpstreamSourceManagers()[0],ng(this._sourceHost)&&i?r=i._innerGetDataStore(t,e,n):(r=new $f).initData(new Zd(e,t.length),t),o[n]=r),r},tg.prototype._getUpstreamSourceManagers=function(){var t,e=this._sourceHost;return ng(e)?(t=Xp(e))?[t.getSourceManager()]:[]:B((t=e).get("transform",!0)||t.get("fromTransformResult",!0)?Mo(t.ecModel,"dataset",{index:t.get("fromDatasetIndex",!0),id:t.get("fromDatasetId",!0)},So).models:[],function(t){return t.getSourceManager()})},tg.prototype._getSourceMetaRawOption=function(){var t,e,n,i=this._sourceHost;return ng(i)?(t=i.get("seriesLayoutBy",!0),e=i.get("sourceHeader",!0),n=i.get("dimensions",!0)):this._getUpstreamSourceManagers().length||(t=(i=i).get("seriesLayoutBy",!0),e=i.get("sourceHeader",!0),n=i.get("dimensions",!0)),{seriesLayoutBy:t,sourceHeader:e,dimensions:n}};var Jf=tg;function tg(t){this._sourceList=[],this._storeList=[],this._upstreamSignList=[],this._versionSignBase=0,this._dirty=!0,this._sourceHost=t}function eg(t){t.option.transform&&Dt(t.option.transform)}function ng(t){return"series"===t.mainType}function ig(t){throw new Error(t)}var rg="line-height:1";function og(t,e){var n=t.color||"#6e7079",i=t.fontSize||12,r=t.fontWeight||"400",o=t.color||"#464646",a=t.fontSize||14,t=t.fontWeight||"900";return"html"===e?{nameStyle:"font-size:"+up(i+"")+"px;color:"+up(n)+";font-weight:"+up(r+""),valueStyle:"font-size:"+up(a+"")+"px;color:"+up(o)+";font-weight:"+up(t+"")}:{nameStyle:{fontSize:i,fill:n,fontWeight:r},valueStyle:{fontSize:a,fill:o,fontWeight:t}}}var ag=[0,10,20,30],sg=["","\n","\n\n","\n\n\n"];function lg(t,e){return e.type=t,e}function ug(t){return"section"===t.type}function hg(t){return ug(t)?cg:pg}function cg(i,r,t,o){var n,e=r.noHeader,a=(l=function n(t){var i,e,r;return ug(t)?(i=0,e=t.blocks.length,r=1<e||0<e&&!t.noHeader,O(t.blocks,function(t){var e=n(t);i<=e&&(i=e+ +(r&&(!e||ug(t)&&!t.noHeader)))}),i):0}(r),{html:ag[l],richText:sg[l]}),s=[],l=r.blocks||[],u=(Ct(!l||F(l)),l=l||[],i.orderMode),h=(r.sortBlocks&&u&&(l=l.slice(),zt(h={valueAsc:"asc",valueDesc:"desc"},u)?(n=new Af(h[u],null),l.sort(function(t,e){return n.evaluate(t.sortParam,e.sortParam)})):"seriesDesc"===u&&l.reverse()),O(l,function(t,e){var n=r.valueFormatter,n=hg(t)(n?L(L({},i),{valueFormatter:n}):i,t,0<e?a.html:0,o);null!=n&&s.push(n)}),"richText"===i.renderMode?s.join(a.richText):fg(s.join(""),e?t:a.html));return e?h:(u=hp(r.header,"ordinal",i.useUTC),l=og(o,i.renderMode).nameStyle,"richText"===i.renderMode?gg(i,u,l)+a.richText+h:fg('<div style="'+l+";"+rg+';">'+up(u)+"</div>"+h,t))}function pg(t,e,n,i){var r,o,a,s,l=t.renderMode,u=e.noName,h=e.noValue,c=!e.markerType,p=e.name,d=t.useUTC,f=e.valueFormatter||t.valueFormatter||function(t){return B(t=F(t)?t:[t],function(t,e){return hp(t,F(o)?o[e]:o,d)})};if(!u||!h)return r=c?"":t.markupStyleCreator.makeTooltipMarker(e.markerType,e.markerColor||"#333",l),p=u?"":hp(p,"ordinal",d),o=e.valueType,f=h?[]:f(e.value),e=!c||!u,a=!c&&u,i=og(i,l),s=i.nameStyle,i=i.valueStyle,"richText"===l?(c?"":r)+(u?"":gg(t,p,s))+(h?"":function(t,e,n,i,r){r=[r],i=i?10:20;return n&&r.push({padding:[0,0,0,i],align:"right"}),t.markupStyleCreator.wrapRichTextStyle(F(e)?e.join("  "):e,r)}(t,f,e,a,i)):fg((c?"":r)+(u?"":'<span style="'+s+";"+(!c?"margin-left:2px":"")+'">'+up(p)+"</span>")+(h?"":function(t,e,n,i){n=n?"10px":"20px",e=e?"float:right;margin-left:"+n:"";return t=F(t)?t:[t],'<span style="'+e+";"+i+'">'+B(t,up).join("&nbsp;&nbsp;")+"</span>"}(f,e,a,i)),n)}function dg(t,e,n,i,r,o){if(t)return hg(t)({useUTC:r,renderMode:n,orderMode:i,markupStyleCreator:e,valueFormatter:t.valueFormatter},t,0,o)}function fg(t,e){return'<div style="'+("margin: "+e+"px 0 0")+";"+rg+';">'+t+'<div style="clear:both"></div></div>'}function gg(t,e,n){return t.markupStyleCreator.wrapRichTextStyle(e,n)}function yg(t,e){t=t.get("padding");return null!=t?t:"richText"===e?[8,10]:10}vg.prototype._generateStyleName=function(){return"__EC_aUTo_"+this._nextStyleNameId++},vg.prototype.makeTooltipMarker=function(t,e,n){var i="richText"===n?this._generateStyleName():null,e=fp({color:e,type:t,renderMode:n,markerId:i});return V(e)?e:(this.richTextStyles[i]=e.style,e.content)},vg.prototype.wrapRichTextStyle=function(t,e){var n={},e=(F(e)?O(e,function(t){return L(n,t)}):L(n,e),this._generateStyleName());return this.richTextStyles[e]=n,"{"+e+"|"+t+"}"};var mg=vg;function vg(){this.richTextStyles={},this._nextStyleNameId=no()}function _g(t){var e,n,i,r,o,a,s,l,u,h,c,p=t.series,d=t.dataIndex,t=t.multipleSeries,f=p.getData(),g=f.mapDimensionsAll("defaultedTooltip"),y=g.length,m=p.getRawValue(d),v=F(m),_=(_=d,gp((w=p).getData().getItemVisual(_,"style")[w.visualDrawType]));function x(t,e){e=s.getDimensionInfo(e);e&&!1!==e.otherDims.tooltip&&(l?c.push(lg("nameValue",{markerType:"subItem",markerColor:a,name:e.displayName,value:t,valueType:e.type})):(u.push(t),h.push(e.type)))}1<y||v&&!y?(w=m,r=d,o=g,a=_,s=p.getData(),l=lt(w,function(t,e,n){n=s.getDimensionInfo(n);return t||n&&!1!==n.tooltip&&null!=n.displayName},!1),u=[],h=[],c=[],o.length?O(o,function(t){x(lf(s,r,t),t)}):O(w,x),e=(o={inlineValues:u,inlineValueTypes:h,blocks:c}).inlineValueTypes,n=o.blocks,i=(o=o.inlineValues)[0]):y?(w=f.getDimensionInfo(g[0]),i=o=lf(f,d,g[0]),e=w.type):i=o=v?m[0]:m;var y=go(p),g=y&&p.name||"",w=f.getName(d),v=t?g:w;return lg("section",{header:g,noHeader:t||!y,sortParam:i,blocks:[lg("nameValue",{markerType:"item",markerColor:_,name:v,noName:!It(v),value:o,valueType:e})].concat(n||[])})}var xg=_o();function wg(t,e){return t.getName(e)||t.getId(e)}u(c,bg=g),c.prototype.init=function(t,e,n){this.seriesIndex=this.componentIndex,this.dataTask=pf({count:Tg,reset:Cg}),this.dataTask.context={model:this},this.mergeDefaultAndTheme(t,n);(xg(this).sourceManager=new Jf(this)).prepareSource();t=this.getInitialData(t,n);kg(t,this),this.dataTask.context.data=t,xg(this).dataBeforeProcessed=t,Mg(this),this._initSelectedMapFromData(t)},c.prototype.mergeDefaultAndTheme=function(t,e){var n=bp(this),i=n?Mp(t):{},r=this.subType;g.hasClass(r),d(t,e.getTheme().get(this.subType)),d(t,this.getDefaultOption()),so(t,"label",["show"]),this.fillDataTextStyle(t.data),n&&Sp(t,i,n)},c.prototype.mergeOption=function(t,e){t=d(this.option,t,!0),this.fillDataTextStyle(t.data);var n=bp(this),n=(n&&Sp(this.option,t,n),xg(this).sourceManager),n=(n.dirty(),n.prepareSource(),this.getInitialData(t,e));kg(n,this),this.dataTask.dirty(),this.dataTask.context.data=n,xg(this).dataBeforeProcessed=n,Mg(this),this._initSelectedMapFromData(n)},c.prototype.fillDataTextStyle=function(t){if(t&&!yt(t))for(var e=["show"],n=0;n<t.length;n++)t[n]&&t[n].label&&so(t[n],"label",e)},c.prototype.getInitialData=function(t,e){},c.prototype.appendData=function(t){this.getRawData().appendData(t.data)},c.prototype.getData=function(t){var e=Ag(this);return e?(e=e.context.data,null==t?e:e.getLinkedData(t)):xg(this).data},c.prototype.getAllData=function(){var t=this.getData();return t&&t.getLinkedDataAll?t.getLinkedDataAll():[{data:t}]},c.prototype.setData=function(t){var e,n=Ag(this);n&&((e=n.context).outputData=t,n!==this.dataTask)&&(e.data=t),xg(this).data=t},c.prototype.getEncode=function(){var t=this.get("encode",!0);if(t)return z(t)},c.prototype.getSourceManager=function(){return xg(this).sourceManager},c.prototype.getSource=function(){return this.getSourceManager().getSource()},c.prototype.getRawData=function(){return xg(this).dataBeforeProcessed},c.prototype.getColorBy=function(){return this.get("colorBy")||"series"},c.prototype.isColorBySeries=function(){return"series"===this.getColorBy()},c.prototype.getBaseAxis=function(){var t=this.coordinateSystem;return t&&t.getBaseAxis&&t.getBaseAxis()},c.prototype.formatTooltip=function(t,e,n){return _g({series:this,dataIndex:t,multipleSeries:e})},c.prototype.isAnimationEnabled=function(){var t=this.ecModel;return!!(!b.node||t&&t.ssr)&&!!(t=(t=this.getShallow("animation"))&&this.getData().count()>this.getShallow("animationThreshold")?!1:t)},c.prototype.restoreData=function(){this.dataTask.dirty()},c.prototype.getColorFromPalette=function(t,e,n){var i=this.ecModel;return Qp.prototype.getColorFromPalette.call(this,t,e,n)||i.getColorFromPalette(t,e,n)},c.prototype.coordDimToDataDim=function(t){return this.getRawData().mapDimensionsAll(t)},c.prototype.getProgressive=function(){return this.get("progressive")},c.prototype.getProgressiveThreshold=function(){return this.get("progressiveThreshold")},c.prototype.select=function(t,e){this._innerSelect(this.getData(e),t)},c.prototype.unselect=function(t,e){var n=this.option.selectedMap;if(n){var i=this.option.selectedMode,r=this.getData(e);if("series"===i||"all"===n)this.option.selectedMap={},this._selectedDataIndicesMap={};else for(var o=0;o<t.length;o++){var a=wg(r,t[o]);n[a]=!1,this._selectedDataIndicesMap[a]=-1}}},c.prototype.toggleSelect=function(t,e){for(var n=[],i=0;i<t.length;i++)n[0]=t[i],this.isSelected(t[i],e)?this.unselect(n,e):this.select(n,e)},c.prototype.getSelectedDataIndices=function(){if("all"===this.option.selectedMap)return[].slice.call(this.getData().getIndices());for(var t=this._selectedDataIndicesMap,e=ht(t),n=[],i=0;i<e.length;i++){var r=t[e[i]];0<=r&&n.push(r)}return n},c.prototype.isSelected=function(t,e){var n=this.option.selectedMap;return!!n&&(e=this.getData(e),"all"===n||n[wg(e,t)])&&!e.getItemModel(t).get(["select","disabled"])},c.prototype.isUniversalTransitionEnabled=function(){var t;return!!this.__universalTransitionEnabled||!!(t=this.option.universalTransition)&&(!0===t||t&&t.enabled)},c.prototype._innerSelect=function(t,e){var n=this.option,i=n.selectedMode,r=e.length;if(i&&r)if("series"===i)n.selectedMap="all";else if("multiple"===i){R(n.selectedMap)||(n.selectedMap={});for(var o=n.selectedMap,a=0;a<r;a++){var s,l=e[a];o[s=wg(t,l)]=!0,this._selectedDataIndicesMap[s]=t.getRawIndex(l)}}else"single"!==i&&!0!==i||(s=wg(t,i=e[r-1]),n.selectedMap=((n={})[s]=!0,n),this._selectedDataIndicesMap=((n={})[s]=t.getRawIndex(i),n))},c.prototype._initSelectedMapFromData=function(n){var i;this.option.selectedMap||(i=[],n.hasItemOption&&n.each(function(t){var e=n.getRawDataItem(t);e&&e.selected&&i.push(t)}),0<i.length&&this._innerSelect(n,i))},c.registerClass=function(t){return g.registerClass(t)},c.protoInitialize=((dc=c.prototype).type="series.__base__",dc.seriesIndex=0,dc.ignoreStyleOnData=!1,dc.hasSymbolVisual=!1,dc.defaultSymbol="circle",dc.visualStyleAccessPath="itemStyle",void(dc.visualDrawType="fill"));var bg,Sg=c;function c(){var t=null!==bg&&bg.apply(this,arguments)||this;return t._selectedDataIndicesMap={},t}function Mg(t){var e,n,i=t.name;go(t)||(t.name=(t=(e=(t=t).getRawData()).mapDimensionsAll("seriesName"),n=[],O(t,function(t){t=e.getDimensionInfo(t);t.displayName&&n.push(t.displayName)}),n.join(" ")||i))}function Tg(t){return t.model.getRawData().count()}function Cg(t){t=t.model;return t.setData(t.getRawData().cloneShallow()),Ig}function Ig(t,e){e.outputData&&t.end>e.outputData.count()&&e.model.getRawData().cloneShallow(e.outputData)}function kg(e,n){O(Ot(e.CHANGABLE_METHODS,e.DOWNSAMPLE_METHODS),function(t){e.wrapMethod(t,pt(Dg,n))})}function Dg(t,e){t=Ag(t);return t&&t.setOutputEnd((e||this).count()),e}function Ag(t){var e,n=(t.ecModel||{}).scheduler,n=n&&n.getPipeline(t.uid);if(n)return(n=n.currentTask)&&(e=n.agentStubMap)?e.get(t.uid):n}at(Sg,mc),at(Sg,Qp),Lo(Sg,g);Lg.prototype.init=function(t,e){},Lg.prototype.render=function(t,e,n,i){},Lg.prototype.dispose=function(t,e){},Lg.prototype.updateView=function(t,e,n,i){},Lg.prototype.updateLayout=function(t,e,n,i){},Lg.prototype.updateVisual=function(t,e,n,i){},Lg.prototype.blurSeries=function(t,e){},Lg.prototype.eachRendered=function(t){var e=this.group;e&&e.traverse(t)};var Pg=Lg;function Lg(){this.group=new Pr,this.uid=Sc("viewComponent")}function Og(){var o=_o();return function(t){var e=o(t),t=t.pipelineContext,n=!!e.large,i=!!e.progressiveRender,r=e.large=!(!t||!t.large),e=e.progressiveRender=!(!t||!t.progressiveRender);return!(n==r&&i==e)&&"reset"}}Po(Pg),zo(Pg);var Rg=_o(),Ng=Og(),zg=(Eg.prototype.init=function(t,e){},Eg.prototype.render=function(t,e,n,i){},Eg.prototype.highlight=function(t,e,n,i){t=t.getData(i&&i.dataType);t&&Fg(t,i,"emphasis")},Eg.prototype.downplay=function(t,e,n,i){t=t.getData(i&&i.dataType);t&&Fg(t,i,"normal")},Eg.prototype.remove=function(t,e){this.group.removeAll()},Eg.prototype.dispose=function(t,e){},Eg.prototype.updateView=function(t,e,n,i){this.render(t,e,n,i)},Eg.prototype.updateLayout=function(t,e,n,i){this.render(t,e,n,i)},Eg.prototype.updateVisual=function(t,e,n,i){this.render(t,e,n,i)},Eg.prototype.eachRendered=function(t){Yh(this.group,t)},Eg.markUpdateMethod=function(t,e){Rg(t).updateMethod=e},Eg.protoInitialize=void(Eg.prototype.type="chart"),Eg);function Eg(){this.group=new Pr,this.uid=Sc("viewChart"),this.renderTask=pf({plan:Vg,reset:Hg}),this.renderTask.context={view:this}}function Bg(t,e,n){t&&Dl(t)&&("emphasis"===e?pl:dl)(t,n)}function Fg(e,t,n){var i,r=vo(e,t),o=t&&null!=t.highlightKey?(t=t.highlightKey,i=null==(i=Ns[t])&&Rs<=32?Ns[t]=Rs++:i):null;null!=r?O(ao(r),function(t){Bg(e.getItemGraphicEl(t),n,o)}):e.eachItemGraphicEl(function(t){Bg(t,n,o)})}function Vg(t){return Ng(t.model)}function Hg(t){var e=t.model,n=t.ecModel,i=t.api,r=t.payload,o=e.pipelineContext.progressiveRender,t=t.view,a=r&&Rg(r).updateMethod,o=o?"incrementalPrepareRender":a&&t[a]?a:"render";return"render"!==o&&t[o](e,n,i,r),Gg[o]}Po(zg),zo(zg);var Gg={incrementalPrepareRender:{progress:function(t,e){e.view.incrementalRender(t,e.model,e.ecModel,e.api,e.payload)}},render:{forceFirstProgress:!0,progress:function(t,e){e.view.render(e.model,e.ecModel,e.api,e.payload)}}},Wg="\0__throttleOriginMethod",Ug="\0__throttleRate",Xg="\0__throttleType";function Yg(t,r,o){var a,s,l,u,h,c=0,p=0,d=null;function f(){p=(new Date).getTime(),d=null,t.apply(l,u||[])}r=r||0;function e(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];a=(new Date).getTime(),l=this,u=t;var n=h||r,i=h||o;h=null,s=a-(i?c:p)-n,clearTimeout(d),i?d=setTimeout(f,n):0<=s?f():d=setTimeout(f,-s),c=a}return e.clear=function(){d&&(clearTimeout(d),d=null)},e.debounceNextCall=function(t){h=t},e}function qg(t,e,n,i){var r=t[e];if(r){var o=r[Wg]||r,a=r[Xg];if(r[Ug]!==n||a!==i){if(null==n||!i)return t[e]=o;(r=t[e]=Yg(o,n,"debounce"===i))[Wg]=o,r[Xg]=i,r[Ug]=n}}}function Zg(t,e){var n=t[e];n&&n[Wg]&&(n.clear&&n.clear(),t[e]=n[Wg])}var jg=_o(),Kg={itemStyle:Eo(gc,!0),lineStyle:Eo(cc,!0)},$g={lineStyle:"stroke",itemStyle:"fill"};function Qg(t,e){t=t.visualStyleMapper||Kg[e];return t||(console.warn("Unkown style type '"+e+"'."),Kg.itemStyle)}function Jg(t,e){t=t.visualDrawType||$g[e];return t||(console.warn("Unkown style type '"+e+"'."),"fill")}var Fo={createOnAllSeries:!0,performRawSeries:!0,reset:function(r,t){var e=r.getData(),n=r.visualStyleAccessPath||"itemStyle",i=r.getModel(n),o=Qg(r,n)(i),i=i.getShallow("decal"),a=(i&&(e.setVisual("decal",i),i.dirty=!0),Jg(r,n)),i=o[a],s=S(i)?i:null,n="auto"===o.fill||"auto"===o.stroke;if(o[a]&&!s&&!n||(i=r.getColorFromPalette(r.name,null,t.getSeriesCount()),o[a]||(o[a]=i,e.setVisual("colorFromPalette",!0)),o.fill="auto"===o.fill||S(o.fill)?i:o.fill,o.stroke="auto"===o.stroke||S(o.stroke)?i:o.stroke),e.setVisual("style",o),e.setVisual("drawType",a),!t.isSeriesFiltered(r)&&s)return e.setVisual("colorFromPalette",!1),{dataEach:function(t,e){var n=r.getDataParams(e),i=L({},o);i[a]=s(n),t.setItemVisual(e,"style",i)}}}},ty=new xc,Nh={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){var i,r,o;if(!t.ignoreStyleOnData&&!e.isSeriesFiltered(t))return e=t.getData(),i=t.visualStyleAccessPath||"itemStyle",r=Qg(t,i),o=e.getVisual("drawType"),{dataEach:e.hasItemOption?function(t,e){var n=t.getRawDataItem(e);n&&n[i]&&(ty.option=n[i],n=r(ty),L(t.ensureUniqueItemVisual(e,"style"),n),ty.option.decal&&(t.setItemVisual(e,"decal",ty.option.decal),ty.option.decal.dirty=!0),o in n)&&t.setItemVisual(e,"colorFromPalette",!1)}:null}}},zc={performRawSeries:!0,overallReset:function(e){var i=z();e.eachSeries(function(t){var e,n=t.getColorBy();t.isColorBySeries()||(n=t.type+"-"+n,(e=i.get(n))||i.set(n,e={}),jg(t).scope=e)}),e.eachSeries(function(i){var r,o,a,s,t,l;i.isColorBySeries()||e.isSeriesFiltered(i)||(r=i.getRawData(),o={},a=i.getData(),s=jg(i).scope,t=i.visualStyleAccessPath||"itemStyle",l=Jg(i,t),a.each(function(t){var e=a.getRawIndex(t);o[e]=t}),r.each(function(t){var e,n=o[t];a.getItemVisual(n,"colorFromPalette")&&(n=a.ensureUniqueItemVisual(n,"style"),t=r.getName(t)||t+"",e=r.count(),n[l]=i.getColorFromPalette(t,s,e))}))})}},ey=Math.PI;iy.prototype.restoreData=function(t,e){t.restoreData(e),this._stageTaskMap.each(function(t){t=t.overallTask;t&&t.dirty()})},iy.prototype.getPerformArgs=function(t,e){var n,i;if(t.__pipeline)return i=(n=this._pipelineMap.get(t.__pipeline.id)).context,{step:e=!e&&n.progressiveEnabled&&(!i||i.progressiveRender)&&t.__idxInPipeline>n.blockIndex?n.step:null,modBy:null!=(t=i&&i.modDataCount)?Math.ceil(t/e):null,modDataCount:t}},iy.prototype.getPipeline=function(t){return this._pipelineMap.get(t)},iy.prototype.updateStreamModes=function(t,e){var n=this._pipelineMap.get(t.uid),i=t.getData().count(),e=n.progressiveEnabled&&e.incrementalPrepareRender&&i>=n.threshold,r=t.get("large")&&i>=t.get("largeThreshold"),i="mod"===t.get("progressiveChunkMode")?i:null;t.pipelineContext=n.context={progressiveRender:e,modDataCount:i,large:r}},iy.prototype.restorePipelines=function(t){var i=this,r=i._pipelineMap=z();t.eachSeries(function(t){var e=t.getProgressive(),n=t.uid;r.set(n,{id:n,head:null,tail:null,threshold:t.getProgressiveThreshold(),progressiveEnabled:e&&!(t.preventIncremental&&t.preventIncremental()),blockIndex:-1,step:Math.round(e||700),count:0}),i._pipe(t,t.dataTask)})},iy.prototype.prepareStageTasks=function(){var n=this._stageTaskMap,i=this.api.getModel(),r=this.api;O(this._allHandlers,function(t){var e=n.get(t.uid)||n.set(t.uid,{});Ct(!(t.reset&&t.overallReset),""),t.reset&&this._createSeriesStageTask(t,e,i,r),t.overallReset&&this._createOverallStageTask(t,e,i,r)},this)},iy.prototype.prepareView=function(t,e,n,i){var r=t.renderTask,o=r.context;o.model=e,o.ecModel=n,o.api=i,r.__block=!t.incrementalPrepareRender,this._pipe(e,r)},iy.prototype.performDataProcessorTasks=function(t,e){this._performStageTasks(this._dataProcessorHandlers,t,e,{block:!0})},iy.prototype.performVisualTasks=function(t,e,n){this._performStageTasks(this._visualHandlers,t,e,n)},iy.prototype._performStageTasks=function(t,s,l,u){u=u||{};var h=!1,c=this;function p(t,e){return t.setDirty&&(!t.dirtyMap||t.dirtyMap.get(e.__pipeline.id))}O(t,function(i,t){var e,n,r,o,a;u.visualType&&u.visualType!==i.visualType||(e=(n=c._stageTaskMap.get(i.uid)).seriesTaskMap,(n=n.overallTask)?((o=n.agentStubMap).each(function(t){p(u,t)&&(t.dirty(),r=!0)}),r&&n.dirty(),c.updatePayload(n,l),a=c.getPerformArgs(n,u.block),o.each(function(t){t.perform(a)}),n.perform(a)&&(h=!0)):e&&e.each(function(t,e){p(u,t)&&t.dirty();var n=c.getPerformArgs(t,u.block);n.skip=!i.performRawSeries&&s.isSeriesFiltered(t.context.model),c.updatePayload(t,l),t.perform(n)&&(h=!0)}))}),this.unfinished=h||this.unfinished},iy.prototype.performSeriesTasks=function(t){var e;t.eachSeries(function(t){e=t.dataTask.perform()||e}),this.unfinished=e||this.unfinished},iy.prototype.plan=function(){this._pipelineMap.each(function(t){var e=t.tail;do{if(e.__block){t.blockIndex=e.__idxInPipeline;break}}while(e=e.getUpstream())})},iy.prototype.updatePayload=function(t,e){"remain"!==e&&(t.context.payload=e)},iy.prototype._createSeriesStageTask=function(n,t,i,r){var o=this,a=t.seriesTaskMap,s=t.seriesTaskMap=z(),t=n.seriesType,e=n.getTargetSeries;function l(t){var e=t.uid,e=s.set(e,a&&a.get(e)||pf({plan:ly,reset:uy,count:py}));e.context={model:t,ecModel:i,api:r,useClearVisual:n.isVisual&&!n.isLayout,plan:n.plan,reset:n.reset,scheduler:o},o._pipe(t,e)}n.createOnAllSeries?i.eachRawSeries(l):t?i.eachRawSeriesByType(t,l):e&&e(i,r).each(l)},iy.prototype._createOverallStageTask=function(t,e,n,i){var r=this,o=e.overallTask=e.overallTask||pf({reset:ry}),a=(o.context={ecModel:n,api:i,overallReset:t.overallReset,scheduler:r},o.agentStubMap),s=o.agentStubMap=z(),e=t.seriesType,l=t.getTargetSeries,u=!0,h=!1;function c(t){var e=t.uid,e=s.set(e,a&&a.get(e)||(h=!0,pf({reset:oy,onDirty:sy})));e.context={model:t,overallProgress:u},e.agent=o,e.__block=u,r._pipe(t,e)}Ct(!t.createOnAllSeries,""),e?n.eachRawSeriesByType(e,c):l?l(n,i).each(c):(u=!1,O(n.getSeries(),c)),h&&o.dirty()},iy.prototype._pipe=function(t,e){t=t.uid,t=this._pipelineMap.get(t);t.head||(t.head=e),t.tail&&t.tail.pipe(e),(t.tail=e).__idxInPipeline=t.count++,e.__pipeline=t},iy.wrapStageHandler=function(t,e){return(t=S(t)?{overallReset:t,seriesType:function(t){dy=null;try{t(fy,gy)}catch(t){}return dy}(t)}:t).uid=Sc("stageHandler"),e&&(t.visualType=e),t};var ny=iy;function iy(t,e,n,i){this._stageTaskMap=z(),this.ecInstance=t,this.api=e,n=this._dataProcessorHandlers=n.slice(),i=this._visualHandlers=i.slice(),this._allHandlers=n.concat(i)}function ry(t){t.overallReset(t.ecModel,t.api,t.payload)}function oy(t){return t.overallProgress&&ay}function ay(){this.agent.dirty(),this.getDownstream().dirty()}function sy(){this.agent&&this.agent.dirty()}function ly(t){return t.plan?t.plan(t.model,t.ecModel,t.api,t.payload):null}function uy(t){t.useClearVisual&&t.data.clearAllVisual();t=t.resetDefines=ao(t.reset(t.model,t.ecModel,t.api,t.payload));return 1<t.length?B(t,function(t,e){return cy(e)}):hy}var hy=cy(0);function cy(o){return function(t,e){var n=e.data,i=e.resetDefines[o];if(i&&i.dataEach)for(var r=t.start;r<t.end;r++)i.dataEach(n,r);else i&&i.progress&&i.progress(t,n)}}function py(t){return t.data.count()}var dy,fy={},gy={};function yy(t,e){for(var n in e.prototype)t[n]=Et}yy(fy,id),yy(gy,sd),fy.eachSeriesByType=fy.eachRawSeriesByType=function(t){dy=t},fy.eachComponent=function(t){"series"===t.mainType&&t.subType&&(dy=t.subType)};function my(){return{axisLine:{lineStyle:{color:vy}},splitLine:{lineStyle:{color:"#484753"}},splitArea:{areaStyle:{color:["rgba(255,255,255,0.02)","rgba(255,255,255,0.05)"]}},minorSplitLine:{lineStyle:{color:"#20203B"}}}}var dc=["#37A2DA","#32C5E9","#67E0E3","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#E062AE","#E690D1","#e7bcf3","#9d96f5","#8378EA","#96BFFF"],mc={color:dc,colorLayer:[["#37A2DA","#ffd85c","#fd7b5f"],["#37A2DA","#67E0E3","#FFDB5C","#ff9f7f","#E062AE","#9d96f5"],["#37A2DA","#32C5E9","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#e7bcf3","#8378EA","#96BFFF"],dc]},vy="#B9B8CE",gc="#100C2A",cc=["#4992ff","#7cffb2","#fddd60","#ff6e76","#58d9f9","#05c091","#ff8a45","#8d48e3","#dd79ff"],dc={darkMode:!0,color:cc,backgroundColor:gc,axisPointer:{lineStyle:{color:"#817f91"},crossStyle:{color:"#817f91"},label:{color:"#fff"}},legend:{textStyle:{color:vy}},textStyle:{color:vy},title:{textStyle:{color:"#EEF1FA"},subtextStyle:{color:"#B9B8CE"}},toolbox:{iconStyle:{borderColor:vy}},dataZoom:{borderColor:"#71708A",textStyle:{color:vy},brushStyle:{color:"rgba(135,163,206,0.3)"},handleStyle:{color:"#353450",borderColor:"#C5CBE3"},moveHandleStyle:{color:"#B0B6C3",opacity:.3},fillerColor:"rgba(135,163,206,0.2)",emphasis:{handleStyle:{borderColor:"#91B7F2",color:"#4D587D"},moveHandleStyle:{color:"#636D9A",opacity:.7}},dataBackground:{lineStyle:{color:"#71708A",width:1},areaStyle:{color:"#71708A"}},selectedDataBackground:{lineStyle:{color:"#87A3CE"},areaStyle:{color:"#87A3CE"}}},visualMap:{textStyle:{color:vy}},timeline:{lineStyle:{color:vy},label:{color:vy},controlStyle:{color:vy,borderColor:vy}},calendar:{itemStyle:{color:gc},dayLabel:{color:vy},monthLabel:{color:vy},yearLabel:{color:vy}},timeAxis:my(),logAxis:my(),valueAxis:my(),categoryAxis:my(),line:{symbol:"circle"},graph:{color:cc},gauge:{title:{color:vy},axisLine:{lineStyle:{color:[[1,"rgba(207,212,219,0.2)"]]}},axisLabel:{color:vy},detail:{color:"#EEF1FA"}},candlestick:{itemStyle:{color:"#f64e56",color0:"#54ea92",borderColor:"#f64e56",borderColor0:"#54ea92"}}},_y=(dc.categoryAxis.splitLine.show=!1,xy.prototype.normalizeQuery=function(t){var e,a,s,l={},u={},h={};return V(t)?(e=Ao(t),l.mainType=e.main||null,l.subType=e.sub||null):(a=["Index","Name","Id"],s={name:1,dataIndex:1,dataType:1},O(t,function(t,e){for(var n=!1,i=0;i<a.length;i++){var r=a[i],o=e.lastIndexOf(r);0<o&&o===e.length-r.length&&"data"!==(o=e.slice(0,o))&&(l.mainType=o,l[r.toLowerCase()]=t,n=!0)}s.hasOwnProperty(e)&&(u[e]=t,n=!0),n||(h[e]=t)})),{cptQuery:l,dataQuery:u,otherQuery:h}},xy.prototype.filter=function(t,e){var n,i,r,o,a,s=this.eventInfo;return!s||(n=s.targetEl,i=s.packedEvent,r=s.model,s=s.view,!r)||!s||(o=e.cptQuery,a=e.dataQuery,l(o,r,"mainType")&&l(o,r,"subType")&&l(o,r,"index","componentIndex")&&l(o,r,"name")&&l(o,r,"id")&&l(a,i,"name")&&l(a,i,"dataIndex")&&l(a,i,"dataType")&&(!s.filterForExposedEvent||s.filterForExposedEvent(t,e.otherQuery,n,i)));function l(t,e,n,i){return null==t[n]||e[i||n]===t[n]}},xy.prototype.afterTrigger=function(){this.eventInfo=null},xy);function xy(){}var wy=["symbol","symbolSize","symbolRotate","symbolOffset"],by=wy.concat(["symbolKeepAspect"]),gc={createOnAllSeries:!0,performRawSeries:!0,reset:function(a,t){var e=a.getData();if(a.legendIcon&&e.setVisual("legendIcon",a.legendIcon),a.hasSymbolVisual){for(var s,n={},l={},i=!1,r=0;r<wy.length;r++){var o=wy[r],u=a.get(o);S(u)?(i=!0,l[o]=u):n[o]=u}if(n.symbol=n.symbol||a.defaultSymbol,e.setVisual(L({legendIcon:a.legendIcon||n.symbol,symbolKeepAspect:a.get("symbolKeepAspect")},n)),!t.isSeriesFiltered(a))return s=ht(l),{dataEach:i?function(t,e){for(var n=a.getRawValue(e),i=a.getDataParams(e),r=0;r<s.length;r++){var o=s[r];t.setItemVisual(e,o,l[o](n,i))}}:null}}}},cc={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){if(t.hasSymbolVisual&&!e.isSeriesFiltered(t))return{dataEach:t.getData().hasItemOption?function(t,e){for(var n=t.getItemModel(e),i=0;i<by.length;i++){var r=by[i],o=n.getShallow(r,!0);null!=o&&t.setItemVisual(e,r,o)}}:null}}};function Sy(t,e,s,n,l){var u=t+e;s.isSilent(u)||n.eachComponent({mainType:"series",subType:"pie"},function(t){for(var e,n,i=t.seriesIndex,r=t.option.selectedMap,o=l.selected,a=0;a<o.length;a++)o[a].seriesIndex===i&&(n=vo(e=t.getData(),l.fromActionPayload),s.trigger(u,{type:u,seriesId:t.id,name:F(n)?e.getName(n[0]):e.getName(n),selected:V(r)?r:L({},r)}))})}function My(t,e,n){for(var i;t&&(!e(t)||(i=t,!n));)t=t.__hostTarget||t.parent;return i}var Ty=Math.round(9*Math.random()),Cy="function"==typeof Object.defineProperty,Iy=(ky.prototype.get=function(t){return this._guard(t)[this._id]},ky.prototype.set=function(t,e){t=this._guard(t);return Cy?Object.defineProperty(t,this._id,{value:e,enumerable:!1,configurable:!0}):t[this._id]=e,this},ky.prototype.delete=function(t){return!!this.has(t)&&(delete this._guard(t)[this._id],!0)},ky.prototype.has=function(t){return!!this._guard(t)[this._id]},ky.prototype._guard=function(t){if(t!==Object(t))throw TypeError("Value of WeakMap is not a non-null object.");return t},ky);function ky(){this._id="__ec_inner_"+Ty++}var Dy=j.extend({type:"triangle",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=e.width/2,e=e.height/2;t.moveTo(n,i-e),t.lineTo(n+r,i+e),t.lineTo(n-r,i+e),t.closePath()}}),Ay=j.extend({type:"diamond",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=e.width/2,e=e.height/2;t.moveTo(n,i-e),t.lineTo(n+r,i),t.lineTo(n,i+e),t.lineTo(n-r,i),t.closePath()}}),Py=j.extend({type:"pin",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.x,i=e.y,r=e.width/5*3,e=Math.max(r,e.height),r=r/2,o=r*r/(e-r),e=i-e+r+o,a=Math.asin(o/r),s=Math.cos(a)*r,l=Math.sin(a),u=Math.cos(a),h=.6*r,c=.7*r;t.moveTo(n-s,e+o),t.arc(n,e,r,Math.PI-a,2*Math.PI+a),t.bezierCurveTo(n+s-l*h,e+o+u*h,n,i-c,n,i),t.bezierCurveTo(n,i-c,n-s+l*h,e+o+u*h,n-s,e+o),t.closePath()}}),Ly=j.extend({type:"arrow",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.height,i=e.width,r=e.x,e=e.y,i=i/3*2;t.moveTo(r,e),t.lineTo(r+i,e+n),t.lineTo(r,e+n/4*3),t.lineTo(r-i,e+n),t.lineTo(r,e),t.closePath()}}),Oy={line:function(t,e,n,i,r){r.x1=t,r.y1=e+i/2,r.x2=t+n,r.y2=e+i/2},rect:function(t,e,n,i,r){r.x=t,r.y=e,r.width=n,r.height=i},roundRect:function(t,e,n,i,r){r.x=t,r.y=e,r.width=n,r.height=i,r.r=Math.min(n,i)/4},square:function(t,e,n,i,r){n=Math.min(n,i);r.x=t,r.y=e,r.width=n,r.height=n},circle:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.r=Math.min(n,i)/2},diamond:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.width=n,r.height=i},pin:function(t,e,n,i,r){r.x=t+n/2,r.y=e+i/2,r.width=n,r.height=i},arrow:function(t,e,n,i,r){r.x=t+n/2,r.y=e+i/2,r.width=n,r.height=i},triangle:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.width=n,r.height=i}},Ry={},Ny=(O({line:Bu,rect:ms,roundRect:ms,square:ms,circle:Jl,diamond:Ay,pin:Py,arrow:Ly,triangle:Dy},function(t,e){Ry[e]=new t}),j.extend({type:"symbol",shape:{symbolType:"",x:0,y:0,width:0,height:0},calculateTextPosition:function(t,e,n){var t=vr(t,e,n),i=this.shape;return i&&"pin"===i.symbolType&&"inside"===e.position&&(t.y=n.y+.4*n.height),t},buildPath:function(t,e,n){var i,r=e.symbolType;"none"!==r&&(i=(i=Ry[r])||Ry[r="rect"],Oy[r](e.x,e.y,e.width,e.height,i.shape),i.buildPath(t,i.shape,n))}}));function zy(t,e){var n;"image"!==this.type&&(n=this.style,this.__isEmptyBrush?(n.stroke=t,n.fill=e||"#fff",n.lineWidth=2):"line"===this.shape.symbolType?n.stroke=t:n.fill=t,this.markRedraw())}function Ey(t,e,n,i,r,o,a){var s=0===t.indexOf("empty");return(a=0===(t=s?t.substr(5,1).toLowerCase()+t.substr(6):t).indexOf("image://")?Ph(t.slice(8),new X(e,n,i,r),a?"center":"cover"):0===t.indexOf("path://")?Ah(t.slice(7),{},new X(e,n,i,r),a?"center":"cover"):new Ny({shape:{symbolType:t,x:e,y:n,width:i,height:r}})).__isEmptyBrush=s,a.setColor=zy,o&&a.setColor(o),a}function By(t,e){if(null!=t)return[Gr((t=F(t)?t:[t,t])[0],e[0])||0,Gr(N(t[1],t[0]),e[1])||0]}function Fy(t,e,n){for(var i,r,o,a,s,l,u,h,c,p="radial"===e.type?(i=t,r=e,a=(o=n).width,s=o.height,l=Math.min(a,s),u=null==r.x?.5:r.x,h=null==r.y?.5:r.y,c=null==r.r?.5:r.r,r.global||(u=u*a+o.x,h=h*s+o.y,c*=l),i.createRadialGradient(u,h,0,u,h,c)):(r=t,a=n,o=null==(s=e).x?0:s.x,l=null==s.x2?1:s.x2,i=null==s.y?0:s.y,u=null==s.y2?0:s.y2,s.global||(o=o*a.width+a.x,l=l*a.width+a.x,i=i*a.height+a.y,u=u*a.height+a.y),o=isNaN(o)?0:o,l=isNaN(l)?1:l,i=isNaN(i)?0:i,u=isNaN(u)?0:u,r.createLinearGradient(o,i,l,u)),d=e.colorStops,f=0;f<d.length;f++)p.addColorStop(d[f].offset,d[f].color);return p}function Vy(t){return parseInt(t,10)}function Hy(t,e,n){var i=["width","height"][e],r=["clientWidth","clientHeight"][e],o=["paddingLeft","paddingTop"][e],e=["paddingRight","paddingBottom"][e];return null!=n[i]&&"auto"!==n[i]?parseFloat(n[i]):(n=document.defaultView.getComputedStyle(t),(t[r]||Vy(n[i])||Vy(t.style[i]))-(Vy(n[o])||0)-(Vy(n[e])||0)|0)}function Gy(t){var e,n=t.style,i=n.lineDash&&0<n.lineWidth&&(r=n.lineDash,i=n.lineWidth,r&&"solid"!==r&&0<i?"dashed"===r?[4*i,2*i]:"dotted"===r?[i]:ft(r)?[r]:F(r)?r:null:null),r=n.lineDashOffset;return i&&(e=n.strokeNoScale&&t.getLineScale?t.getLineScale():1)&&1!==e&&(i=B(i,function(t){return t/e}),r/=e),[i,r]}var Wy=new Ea(!0);function Uy(t){var e=t.stroke;return!(null==e||"none"===e||!(0<t.lineWidth))}function Xy(t){return"string"==typeof t&&"none"!==t}function Yy(t){t=t.fill;return null!=t&&"none"!==t}function qy(t,e){var n;null!=e.fillOpacity&&1!==e.fillOpacity?(n=t.globalAlpha,t.globalAlpha=e.fillOpacity*e.opacity,t.fill(),t.globalAlpha=n):t.fill()}function Zy(t,e){var n;null!=e.strokeOpacity&&1!==e.strokeOpacity?(n=t.globalAlpha,t.globalAlpha=e.strokeOpacity*e.opacity,t.stroke(),t.globalAlpha=n):t.stroke()}function jy(t,e,n){var n=Go(e.image,e.__image,n);if(Uo(n))return t=t.createPattern(n,e.repeat||"repeat"),"function"==typeof DOMMatrix&&t&&t.setTransform&&((n=new DOMMatrix).translateSelf(e.x||0,e.y||0),n.rotateSelf(0,0,(e.rotation||0)*Bt),n.scaleSelf(e.scaleX||1,e.scaleY||1),t.setTransform(n)),t}var Ky=["shadowBlur","shadowOffsetX","shadowOffsetY"],$y=[["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]];function Qy(t,e,n,i,r){var o,a=!1;if(!i&&e===(n=n||{}))return!1;!i&&e.opacity===n.opacity||(om(t,r),a=!0,o=Math.max(Math.min(e.opacity,1),0),t.globalAlpha=isNaN(o)?ra.opacity:o),!i&&e.blend===n.blend||(a||(om(t,r),a=!0),t.globalCompositeOperation=e.blend||ra.blend);for(var s=0;s<Ky.length;s++){var l=Ky[s];!i&&e[l]===n[l]||(a||(om(t,r),a=!0),t[l]=t.dpr*(e[l]||0))}return!i&&e.shadowColor===n.shadowColor||(a||(om(t,r),a=!0),t.shadowColor=e.shadowColor||ra.shadowColor),a}function Jy(t,e,n,i,r){var o=am(e,r.inHover),a=i?null:n&&am(n,r.inHover)||{};if(o!==a){var s=Qy(t,o,a,i,r);(i||o.fill!==a.fill)&&(s||(om(t,r),s=!0),Xy(o.fill))&&(t.fillStyle=o.fill),(i||o.stroke!==a.stroke)&&(s||(om(t,r),s=!0),Xy(o.stroke))&&(t.strokeStyle=o.stroke),!i&&o.opacity===a.opacity||(s||(om(t,r),s=!0),t.globalAlpha=null==o.opacity?1:o.opacity),e.hasStroke()&&(n=o.lineWidth/(o.strokeNoScale&&e.getLineScale?e.getLineScale():1),t.lineWidth!==n)&&(s||(om(t,r),s=!0),t.lineWidth=n);for(var l=0;l<$y.length;l++){var u=$y[l],h=u[0];!i&&o[h]===a[h]||(s||(om(t,r),s=!0),t[h]=o[h]||u[1])}}}function tm(t,e){var e=e.transform,n=t.dpr||1;e?t.setTransform(n*e[0],n*e[1],n*e[2],n*e[3],n*e[4],n*e[5]):t.setTransform(n,0,0,n,0,0)}var em=1,nm=2,im=3,rm=4;function om(t,e){e.batchFill&&t.fill(),e.batchStroke&&t.stroke(),e.batchFill="",e.batchStroke=""}function am(t,e){return e&&t.__hoverStyle||t.style}function sm(t,e){lm(t,e,{inHover:!1,viewWidth:0,viewHeight:0},!0)}function lm(t,e,n,z){var i=e.transform;if(e.shouldBePainted(n.viewWidth,n.viewHeight,!1,!1)){var r=e.__clipPaths,o=n.prevElClipPaths,a=!1,s=!1;if(!o||function(t,e){if(t!==e&&(t||e)){if(!t||!e||t.length!==e.length)return 1;for(var n=0;n<t.length;n++)if(t[n]!==e[n])return 1}}(r,o)){if(o&&o.length&&(om(t,n),t.restore(),s=a=!0,n.prevElClipPaths=null,n.allClipped=!1,n.prevEl=null),r&&r.length){om(t,n),t.save();for(var E=r,l=t,o=n,B=!1,F=0;F<E.length;F++){var V=E[F],B=B||V.isZeroArea();tm(l,V),l.beginPath(),V.buildPath(l,V.shape),l.clip()}o.allClipped=B,a=!0}n.prevElClipPaths=r}if(n.allClipped)e.__isRendered=!1;else{e.beforeBrush&&e.beforeBrush(),e.innerBeforeBrush();var u,h,c,p,d,f,g,y,m,v,_,x,w,H,b,S,M,T,C,I,k,D,A,o=n.prevEl,P=(o||(s=a=!0),e instanceof j&&e.autoBatch&&(r=e.style,P=Yy(r),u=Uy(r),!(r.lineDash||!(+P^+u)||P&&"string"!=typeof r.fill||u&&"string"!=typeof r.stroke||r.strokePercent<1||r.strokeOpacity<1||r.fillOpacity<1))),a=(a||(u=i,r=o.transform,u&&r?u[0]!==r[0]||u[1]!==r[1]||u[2]!==r[2]||u[3]!==r[3]||u[4]!==r[4]||u[5]!==r[5]:u||r)?(om(t,n),tm(t,e)):P||om(t,n),am(e,n.inHover));if(e instanceof j)n.lastDrawType!==em&&(s=!0,n.lastDrawType=em),Jy(t,e,o,s,n),P&&(n.batchFill||n.batchStroke)||t.beginPath(),i=t,r=e,R=P,b=Uy(p=a),S=Yy(p),M=p.strokePercent,T=M<1,C=!r.path,r.silent&&!T||!C||r.createPathProxy(),I=r.path||Wy,k=r.__dirty,R||(d=p.fill,A=p.stroke,f=S&&!!d.colorStops,g=b&&!!A.colorStops,y=S&&!!d.image,m=b&&!!A.image,D=w=x=_=v=void 0,(f||g)&&(D=r.getBoundingRect()),f&&(v=k?Fy(i,d,D):r.__canvasFillGradient,r.__canvasFillGradient=v),g&&(_=k?Fy(i,A,D):r.__canvasStrokeGradient,r.__canvasStrokeGradient=_),y&&(x=k||!r.__canvasFillPattern?jy(i,d,r):r.__canvasFillPattern,r.__canvasFillPattern=x),m&&(w=k||!r.__canvasStrokePattern?jy(i,A,r):r.__canvasStrokePattern,r.__canvasStrokePattern=x),f?i.fillStyle=v:y&&(x?i.fillStyle=x:S=!1),g?i.strokeStyle=_:m&&(w?i.strokeStyle=w:b=!1)),D=r.getGlobalScale(),I.setScale(D[0],D[1],r.segmentIgnoreThreshold),i.setLineDash&&p.lineDash&&(H=(d=Gy(r))[0],O=d[1]),A=!0,(C||k&Ye)&&(I.setDPR(i.dpr),T?I.setContext(null):(I.setContext(i),A=!1),I.reset(),r.buildPath(I,r.shape,R),I.toStatic(),r.pathUpdated()),A&&I.rebuildPath(i,T?M:1),H&&(i.setLineDash(H),i.lineDashOffset=O),R||(p.strokeFirst?(b&&Zy(i,p),S&&qy(i,p)):(S&&qy(i,p),b&&Zy(i,p))),H&&i.setLineDash([]),P&&(n.batchFill=a.fill||"",n.batchStroke=a.stroke||"");else if(e instanceof is)n.lastDrawType!==im&&(s=!0,n.lastDrawType=im),Jy(t,e,o,s,n),f=t,v=e,null!=(x=(y=a).text)&&(x+=""),x&&(f.font=y.font||K,f.textAlign=y.textAlign,f.textBaseline=y.textBaseline,_=g=void 0,f.setLineDash&&y.lineDash&&(g=(v=Gy(v))[0],_=v[1]),g&&(f.setLineDash(g),f.lineDashOffset=_),y.strokeFirst?(Uy(y)&&f.strokeText(x,y.x,y.y),Yy(y)&&f.fillText(x,y.x,y.y)):(Yy(y)&&f.fillText(x,y.x,y.y),Uy(y)&&f.strokeText(x,y.x,y.y)),g)&&f.setLineDash([]);else if(e instanceof ls)n.lastDrawType!==nm&&(s=!0,n.lastDrawType=nm),m=o,w=s,Qy(t,am(e,(D=n).inHover),m&&am(m,D.inHover),w,D),d=t,C=a,(r=(k=e).__image=Go(C.image,k.__image,k,k.onload))&&Uo(r)&&(A=C.x||0,I=C.y||0,T=k.getWidth(),k=k.getHeight(),M=r.width/r.height,null==T&&null!=k?T=k*M:null==k&&null!=T?k=T/M:null==T&&null==k&&(T=r.width,k=r.height),C.sWidth&&C.sHeight?(h=C.sx||0,c=C.sy||0,d.drawImage(r,h,c,C.sWidth,C.sHeight,A,I,T,k)):C.sx&&C.sy?(h=C.sx,c=C.sy,d.drawImage(r,h,c,T-h,k-c,A,I,T,k)):d.drawImage(r,A,I,T,k));else if(e.getTemporalDisplayables){n.lastDrawType!==rm&&(s=!0,n.lastDrawType=rm);var L,G,W=t,O=e,R=n,U=O.getDisplayables(),X=O.getTemporalDisplayables(),Y=(W.save(),{prevElClipPaths:null,prevEl:null,allClipped:!1,viewWidth:R.viewWidth,viewHeight:R.viewHeight,inHover:R.inHover});for(L=O.getCursor(),G=U.length;L<G;L++)(N=U[L]).beforeBrush&&N.beforeBrush(),N.innerBeforeBrush(),lm(W,N,Y,L===G-1),N.innerAfterBrush(),N.afterBrush&&N.afterBrush(),Y.prevEl=N;for(var N,q=0,Z=X.length;q<Z;q++)(N=X[q]).beforeBrush&&N.beforeBrush(),N.innerBeforeBrush(),lm(W,N,Y,q===Z-1),N.innerAfterBrush(),N.afterBrush&&N.afterBrush(),Y.prevEl=N;O.clearTemporalDisplayables(),O.notClear=!0,W.restore()}P&&z&&om(t,n),e.innerAfterBrush(),e.afterBrush&&e.afterBrush(),(n.prevEl=e).__dirty=0,e.__isRendered=!0}}else e.__dirty&=~Xe,e.__isRendered=!1}var um=new Iy,hm=new Dn(100),cm=["symbol","symbolSize","symbolKeepAspect","color","backgroundColor","dashArrayX","dashArrayY","maxTileWidth","maxTileHeight"];function pm(t,e){if("none"===t)return null;var a=e.getDevicePixelRatio(),s=e.getZr(),l="svg"===s.painter.type,e=(t.dirty&&um.delete(t),um.get(t));if(e)return e;for(var n,u=E(t,{symbol:"rect",symbolSize:1,symbolKeepAspect:!0,color:"rgba(0, 0, 0, 0.2)",backgroundColor:null,dashArrayX:5,dashArrayY:5,rotation:0,maxTileWidth:512,maxTileHeight:512}),e=("none"===u.backgroundColor&&(u.backgroundColor=null),{repeat:"repeat"}),i=e,r=[a],o=!0,h=0;h<cm.length;++h){var c=u[cm[h]];if(null!=c&&!F(c)&&!V(c)&&!ft(c)&&"boolean"!=typeof c){o=!1;break}r.push(c)}o&&(n=r.join(",")+(l?"-svg":""),v=hm.get(n))&&(l?i.svgElement=v:i.image=v);var p,d=function t(e){if(!e||0===e.length)return[[0,0]];if(ft(e))return[[o=Math.ceil(e),o]];var n=!0;for(var i=0;i<e.length;++i)if(!ft(e[i])){n=!1;break}if(n)return t([e]);var r=[];for(i=0;i<e.length;++i){var o;ft(e[i])?(o=Math.ceil(e[i]),r.push([o,o])):(o=B(e[i],function(t){return Math.ceil(t)})).length%2==1?r.push(o.concat(o)):r.push(o)}return r}(u.dashArrayX),f=function(t){if(!t||"object"==typeof t&&0===t.length)return[0,0];if(ft(t))return[e=Math.ceil(t),e];var e=B(t,function(t){return Math.ceil(t)});return t.length%2?e.concat(e):e}(u.dashArrayY),g=function t(e){if(!e||0===e.length)return[["rect"]];if(V(e))return[[e]];var n=!0;for(var i=0;i<e.length;++i)if(!V(e[i])){n=!1;break}if(n)return t([e]);var r=[];for(i=0;i<e.length;++i)V(e[i])?r.push([e[i]]):r.push(e[i]);return r}(u.symbol),y=function(t){return B(t,dm)}(d),m=dm(f),v=!l&&H.createCanvas(),_=l&&{tag:"g",attrs:{},key:"dcl",children:[]},x=function(){for(var t=1,e=0,n=y.length;e<n;++e)t=io(t,y[e]);for(var i=1,e=0,n=g.length;e<n;++e)i=io(i,g[e].length);t*=i;var r=m*y.length*g.length;return{width:Math.max(1,Math.min(t,u.maxTileWidth)),height:Math.max(1,Math.min(r,u.maxTileHeight))}}();v&&(v.width=x.width*a,v.height=x.height*a,p=v.getContext("2d")),p&&(p.clearRect(0,0,v.width,v.height),u.backgroundColor)&&(p.fillStyle=u.backgroundColor,p.fillRect(0,0,v.width,v.height));for(var w=0,b=0;b<f.length;++b)w+=f[b];if(!(w<=0))for(var S=-m,M=0,T=0,C=0;S<x.height;){if(M%2==0){for(var I=T/2%g.length,k=0,D=0,A=0;k<2*x.width;){for(var P,L,O,R,N,z=0,b=0;b<d[C].length;++b)z+=d[C][b];if(z<=0)break;D%2==0&&(L=.5*(1-u.symbolSize),P=k+d[C][D]*L,L=S+f[M]*L,O=d[C][D]*u.symbolSize,R=f[M]*u.symbolSize,N=A/2%g[I].length,function(t,e,n,i,r){var o=l?1:a,r=Ey(r,t*o,e*o,n*o,i*o,u.color,u.symbolKeepAspect);l?(t=s.painter.renderOneToVNode(r))&&_.children.push(t):sm(p,r)}(P,L,O,R,g[I][N])),k+=d[C][D],++A,++D===d[C].length&&(D=0)}++C===d.length&&(C=0)}S+=f[M],++T,++M===f.length&&(M=0)}return o&&hm.put(n,v||_),i.image=v,i.svgElement=_,i.svgWidth=x.width,i.svgHeight=x.height,e.rotation=u.rotation,e.scaleX=e.scaleY=l?1:1/a,um.set(t,e),t.dirty=!1,e}function dm(t){for(var e=0,n=0;n<t.length;++n)e+=t[n];return t.length%2==1?2*e:e}var fm=new ae,gm={};var ym="undefined"!=typeof window,Ay={PROCESSOR:{FILTER:1e3,SERIES_FILTER:800,STATISTIC:5e3},VISUAL:{LAYOUT:1e3,PROGRESSIVE_LAYOUT:1100,GLOBAL:2e3,CHART:3e3,POST_CHART_LAYOUT:4600,COMPONENT:4e3,BRUSH:5e3,CHART_ITEM:4500,ARIA:6e3,DECAL:7e3}},mm="__flagInMainProcess",vm="__pendingUpdate",_m="__needsUpdateStatus",xm=/^[a-zA-Z0-9_]+$/,wm="__connectUpdateStatus";function bm(n){return function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];if(!this.isDisposed())return Mm(this,n,t);this.id}}function Sm(n){return function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return Mm(this,n,t)}}function Mm(t,e,n){return n[0]=n[0]&&n[0].toLowerCase(),ae.prototype[e].apply(t,n)}u(Im,Tm=ae);var Tm,Cm=Im;function Im(){return null!==Tm&&Tm.apply(this,arguments)||this}var km,Dm,Am,Pm,Lm,Om,Rm,Nm,zm,Em,Bm,Fm,Vm,Hm,Gm,Wm,Um,Xm,Ym,Py=Cm.prototype,qm=(Py.on=Sm("on"),Py.off=Sm("off"),u(p,Ym=ae),p.prototype._onframe=function(){if(!this._disposed){Xm(this);var t=this._scheduler;if(this[vm]){var e=this[vm].silent;this[mm]=!0;try{km(this),Pm.update.call(this,null,this[vm].updateParams)}catch(t){throw this[mm]=!1,this[vm]=null,t}this._zr.flush(),this[mm]=!1,this[vm]=null,Nm.call(this,e),zm.call(this,e)}else if(t.unfinished){var n=1,i=this._model,r=this._api;t.unfinished=!1;do{var o=+new Date}while(t.performSeriesTasks(i),t.performDataProcessorTasks(i),Om(this,i),t.performVisualTasks(i),Hm(this,this._model,r,"remain",{}),0<(n-=+new Date-o)&&t.unfinished);t.unfinished||this._zr.flush()}}},p.prototype.getDom=function(){return this._dom},p.prototype.getId=function(){return this.id},p.prototype.getZr=function(){return this._zr},p.prototype.isSSR=function(){return this._ssr},p.prototype.setOption=function(t,e,n){if(!this[mm])if(this._disposed)this.id;else{R(e)&&(n=e.lazyUpdate,i=e.silent,r=e.replaceMerge,o=e.transition,e=e.notMerge),this[mm]=!0,this._model&&!e||(e=new dd(this._api),a=this._theme,(s=this._model=new id).scheduler=this._scheduler,s.ssr=this._ssr,s.init(null,null,null,a,this._locale,e)),this._model.setOption(t,{replaceMerge:r},i0);var i,r,o,a,s={seriesTransition:o,optionChanged:!0};if(n)this[vm]={silent:i,updateParams:s},this[mm]=!1,this.getZr().wakeUp();else{try{km(this),Pm.update.call(this,null,s)}catch(t){throw this[vm]=null,this[mm]=!1,t}this._ssr||this._zr.flush(),this[vm]=null,this[mm]=!1,Nm.call(this,i),zm.call(this,i)}}},p.prototype.setTheme=function(){},p.prototype.getModel=function(){return this._model},p.prototype.getOption=function(){return this._model&&this._model.getOption()},p.prototype.getWidth=function(){return this._zr.getWidth()},p.prototype.getHeight=function(){return this._zr.getHeight()},p.prototype.getDevicePixelRatio=function(){return this._zr.painter.dpr||ym&&window.devicePixelRatio||1},p.prototype.getRenderedCanvas=function(t){return this.renderToCanvas(t)},p.prototype.renderToCanvas=function(t){return this._zr.painter.getRenderedCanvas({backgroundColor:(t=t||{}).backgroundColor||this._model.get("backgroundColor"),pixelRatio:t.pixelRatio||this.getDevicePixelRatio()})},p.prototype.renderToSVGString=function(t){return this._zr.painter.renderToString({useViewBox:(t=t||{}).useViewBox})},p.prototype.getSvgDataURL=function(){var t;if(b.svgSupported)return O((t=this._zr).storage.getDisplayList(),function(t){t.stopAnimation(null,!0)}),t.painter.toDataURL()},p.prototype.getDataURL=function(t){var e,n,i,r;if(!this._disposed)return r=(t=t||{}).excludeComponents,e=this._model,n=[],i=this,O(r,function(t){e.eachComponent({mainType:t},function(t){t=i._componentsMap[t.__viewId];t.group.ignore||(n.push(t),t.group.ignore=!0)})}),r="svg"===this._zr.painter.getType()?this.getSvgDataURL():this.renderToCanvas(t).toDataURL("image/"+(t&&t.type||"png")),O(n,function(t){t.group.ignore=!1}),r;this.id},p.prototype.getConnectedDataURL=function(i){var r,o,a,s,l,u,h,c,p,e,t,n,d,f,g;if(!this._disposed)return r="svg"===i.type,o=this.group,a=Math.min,s=Math.max,l0[o]?(u=l=1/0,c=h=-1/0,p=[],e=i&&i.pixelRatio||this.getDevicePixelRatio(),O(s0,function(t,e){var n;t.group===o&&(n=r?t.getZr().painter.getSvgDom().innerHTML:t.renderToCanvas(y(i)),t=t.getDom().getBoundingClientRect(),l=a(t.left,l),u=a(t.top,u),h=s(t.right,h),c=s(t.bottom,c),p.push({dom:n,left:t.left,top:t.top}))}),t=(h*=e)-(l*=e),n=(c*=e)-(u*=e),d=H.createCanvas(),(f=Er(d,{renderer:r?"svg":"canvas"})).resize({width:t,height:n}),r?(g="",O(p,function(t){var e=t.left-l,n=t.top-u;g+='<g transform="translate('+e+","+n+')">'+t.dom+"</g>"}),f.painter.getSvgRoot().innerHTML=g,i.connectedBackgroundColor&&f.painter.setBackgroundColor(i.connectedBackgroundColor),f.refreshImmediately(),f.painter.toDataURL()):(i.connectedBackgroundColor&&f.add(new ms({shape:{x:0,y:0,width:t,height:n},style:{fill:i.connectedBackgroundColor}})),O(p,function(t){t=new ls({style:{x:t.left*e-l,y:t.top*e-u,image:t.dom}});f.add(t)}),f.refreshImmediately(),d.toDataURL("image/"+(i&&i.type||"png")))):this.getDataURL(i);this.id},p.prototype.convertToPixel=function(t,e){return Lm(this,"convertToPixel",t,e)},p.prototype.convertFromPixel=function(t,e){return Lm(this,"convertFromPixel",t,e)},p.prototype.containPixel=function(t,i){var r;if(!this._disposed)return O(wo(this._model,t),function(t,n){0<=n.indexOf("Models")&&O(t,function(t){var e=t.coordinateSystem;e&&e.containPoint?r=r||!!e.containPoint(i):"seriesModels"===n&&(e=this._chartsMap[t.__viewId])&&e.containPoint&&(r=r||e.containPoint(i,t))},this)},this),!!r;this.id},p.prototype.getVisual=function(t,e){var t=wo(this._model,t,{defaultMainType:"series"}),n=t.seriesModel.getData(),t=t.hasOwnProperty("dataIndexInside")?t.dataIndexInside:t.hasOwnProperty("dataIndex")?n.indexOfRawIndex(t.dataIndex):null;if(null!=t){var i=n,r=t,o=e;switch(o){case"color":return i.getItemVisual(r,"style")[i.getVisual("drawType")];case"opacity":return i.getItemVisual(r,"style").opacity;case"symbol":case"symbolSize":case"liftZ":return i.getItemVisual(r,o)}}else{var a=n,s=e;switch(s){case"color":return a.getVisual("style")[a.getVisual("drawType")];case"opacity":return a.getVisual("style").opacity;case"symbol":case"symbolSize":case"liftZ":return a.getVisual(s)}}},p.prototype.getViewOfComponentModel=function(t){return this._componentsMap[t.__viewId]},p.prototype.getViewOfSeriesModel=function(t){return this._chartsMap[t.__viewId]},p.prototype._initEvents=function(){var t,n,i,s=this;O(Jm,function(a){function t(t){var n,e,i,r=s.getModel(),o=t.target;"globalout"===a?n={}:o&&My(o,function(t){var e,t=k(t);return t&&null!=t.dataIndex?(e=t.dataModel||r.getSeriesByIndex(t.seriesIndex),n=e&&e.getDataParams(t.dataIndex,t.dataType)||{},1):t.eventData&&(n=L({},t.eventData),1)},!0),n&&(e=n.componentType,i=n.componentIndex,"markLine"!==e&&"markPoint"!==e&&"markArea"!==e||(e="series",i=n.seriesIndex),i=(e=e&&null!=i&&r.getComponent(e,i))&&s["series"===e.mainType?"_chartsMap":"_componentsMap"][e.__viewId],n.event=t,n.type=a,s._$eventProcessor.eventInfo={targetEl:o,packedEvent:n,model:e,view:i},s.trigger(a,n))}t.zrEventfulCallAtLast=!0,s._zr.on(a,t,s)}),O(e0,function(t,e){s._messageCenter.on(e,function(t){this.trigger(e,t)},s)}),O(["selectchanged"],function(e){s._messageCenter.on(e,function(t){this.trigger(e,t)},s)}),t=this._messageCenter,i=(n=this)._api,t.on("selectchanged",function(t){var e=i.getModel();t.isFromClick?(Sy("map","selectchanged",n,e,t),Sy("pie","selectchanged",n,e,t)):"select"===t.fromAction?(Sy("map","selected",n,e,t),Sy("pie","selected",n,e,t)):"unselect"===t.fromAction&&(Sy("map","unselected",n,e,t),Sy("pie","unselected",n,e,t))})},p.prototype.isDisposed=function(){return this._disposed},p.prototype.clear=function(){this._disposed?this.id:this.setOption({series:[]},!0)},p.prototype.dispose=function(){var t,e,n;this._disposed?this.id:(this._disposed=!0,this.getDom()&&To(this.getDom(),c0,""),e=(t=this)._api,n=t._model,O(t._componentsViews,function(t){t.dispose(n,e)}),O(t._chartsViews,function(t){t.dispose(n,e)}),t._zr.dispose(),t._dom=t._model=t._chartsMap=t._componentsMap=t._chartsViews=t._componentsViews=t._scheduler=t._api=t._zr=t._throttledZrFlush=t._theme=t._coordSysMgr=t._messageCenter=null,delete s0[t.id])},p.prototype.resize=function(t){if(!this[mm])if(this._disposed)this.id;else{this._zr.resize(t);var e=this._model;if(this._loadingFX&&this._loadingFX.resize(),e){var e=e.resetOption("media"),n=t&&t.silent;this[vm]&&(null==n&&(n=this[vm].silent),e=!0,this[vm]=null),this[mm]=!0;try{e&&km(this),Pm.update.call(this,{type:"resize",animation:L({duration:0},t&&t.animation)})}catch(t){throw this[mm]=!1,t}this[mm]=!1,Nm.call(this,n),zm.call(this,n)}}},p.prototype.showLoading=function(t,e){this._disposed?this.id:(R(t)&&(e=t,t=""),t=t||"default",this.hideLoading(),a0[t]&&(t=a0[t](this._api,e),e=this._zr,this._loadingFX=t,e.add(t)))},p.prototype.hideLoading=function(){this._disposed?this.id:(this._loadingFX&&this._zr.remove(this._loadingFX),this._loadingFX=null)},p.prototype.makeActionFromEvent=function(t){var e=L({},t);return e.type=e0[t.type],e},p.prototype.dispatchAction=function(t,e){var n;this._disposed?this.id:(R(e)||(e={silent:!!e}),t0[t.type]&&this._model&&(this[mm]?this._pendingActions.push(t):(n=e.silent,Rm.call(this,t,n),(t=e.flush)?this._zr.flush():!1!==t&&b.browser.weChat&&this._throttledZrFlush(),Nm.call(this,n),zm.call(this,n))))},p.prototype.updateLabelLayout=function(){fm.trigger("series:layoutlabels",this._model,this._api,{updatedSeries:[]})},p.prototype.appendData=function(t){var e;this._disposed?this.id:(e=t.seriesIndex,this.getModel().getSeriesByIndex(e).appendData(t),this._scheduler.unfinished=!0,this.getZr().wakeUp())},p.internalField=(km=function(t){var e=t._scheduler;e.restorePipelines(t._model),e.prepareStageTasks(),Dm(t,!0),Dm(t,!1),e.plan()},Dm=function(t,r){for(var o=t._model,a=t._scheduler,s=r?t._componentsViews:t._chartsViews,l=r?t._componentsMap:t._chartsMap,u=t._zr,h=t._api,e=0;e<s.length;e++)s[e].__alive=!1;function n(t){var e,n=t.__requireNewView,i=(t.__requireNewView=!1,"_ec_"+t.id+"_"+t.type),n=!n&&l[i];n||(e=Ao(t.type),(n=new(r?Pg.getClass(e.main,e.sub):zg.getClass(e.sub))).init(o,h),l[i]=n,s.push(n),u.add(n.group)),t.__viewId=n.__id=i,n.__alive=!0,n.__model=t,n.group.__ecComponentInfo={mainType:t.mainType,index:t.componentIndex},r||a.prepareView(n,t,o,h)}r?o.eachComponent(function(t,e){"series"!==t&&n(e)}):o.eachSeries(n);for(e=0;e<s.length;){var i=s[e];i.__alive?e++:(r||i.renderTask.dispose(),u.remove(i.group),i.dispose(o,h),s.splice(e,1),l[i.__id]===i&&delete l[i.__id],i.__id=i.group.__ecComponentInfo=null)}},Am=function(c,e,p,n,t){var i,d,r=c._model;function o(t){t&&t.__alive&&t[e]&&t[e](t.__model,r,c._api,p)}r.setUpdatePayload(p),n?((i={})[n+"Id"]=p[n+"Id"],i[n+"Index"]=p[n+"Index"],i[n+"Name"]=p[n+"Name"],i={mainType:n,query:i},t&&(i.subType=t),null!=(t=p.excludeSeriesId)&&(d=z(),O(ao(t),function(t){t=fo(t,null);null!=t&&d.set(t,!0)})),r&&r.eachComponent(i,function(t){var e,n,i=d&&null!==d.get(t.id);if(!i)if(Pl(p))if(t instanceof Sg){if(p.type===Ws&&!p.notBlur&&!t.get(["emphasis","disabled"])){var i=t,r=p,o=c._api,a=i.seriesIndex,s=i.getData(r.dataType);if(s){var r=(F(r=vo(s,r))?r[0]:r)||0,l=s.getItemGraphicEl(r);if(!l)for(var u=s.count(),h=0;!l&&h<u;)l=s.getItemGraphicEl(h++);l?_l(a,(r=k(l)).focus,r.blurScope,o):(r=i.get(["emphasis","focus"]),i=i.get(["emphasis","blurScope"]),null!=r&&_l(a,r,i,o))}}}else{a=wl(t.mainType,t.componentIndex,p.name,c._api),r=a.focusSelf,i=a.dispatchers;p.type===Ws&&r&&!p.notBlur&&xl(t.mainType,t.componentIndex,c._api),i&&O(i,function(t){(p.type===Ws?pl:dl)(t)})}else Al(p)&&t instanceof Sg&&(o=t,i=p,c._api,Al(i)&&(e=i.dataType,F(n=vo(o.getData(e),i))||(n=[n]),o[i.type===qs?"toggleSelect":i.type===Xs?"select":"unselect"](n,e)),bl(t),Um(c))},c),r&&r.eachComponent(i,function(t){d&&null!==d.get(t.id)||o(c["series"===n?"_chartsMap":"_componentsMap"][t.__viewId])},c)):O([].concat(c._componentsViews).concat(c._chartsViews),o)},Pm={prepareAndUpdate:function(t){km(this),Pm.update.call(this,t,{optionChanged:null!=t.newOption})},update:function(t,e){var n=this._model,i=this._api,r=this._zr,o=this._coordSysMgr,a=this._scheduler;n&&(n.setUpdatePayload(t),a.restoreData(n,t),a.performSeriesTasks(n),o.create(n,i),a.performDataProcessorTasks(n,t),Om(this,n),o.update(n,i),Zm(n),a.performVisualTasks(n,t),Fm(this,n,i,t,e),o=n.get("backgroundColor")||"transparent",a=n.get("darkMode"),r.setBackgroundColor(o),null!=a&&"auto"!==a&&r.setDarkMode(a),fm.trigger("afterupdate",n,i))},updateTransform:function(n){var i,r,o=this,a=this._model,s=this._api;a&&(a.setUpdatePayload(n),i=[],a.eachComponent(function(t,e){"series"!==t&&(t=o.getViewOfComponentModel(e))&&t.__alive&&(!t.updateTransform||(e=t.updateTransform(e,a,s,n))&&e.update)&&i.push(t)}),r=z(),a.eachSeries(function(t){var e=o._chartsMap[t.__viewId];(!e.updateTransform||(e=e.updateTransform(t,a,s,n))&&e.update)&&r.set(t.uid,1)}),Zm(a),this._scheduler.performVisualTasks(a,n,{setDirty:!0,dirtyMap:r}),Hm(this,a,s,n,{},r),fm.trigger("afterupdate",a,s))},updateView:function(t){var e=this._model;e&&(e.setUpdatePayload(t),zg.markUpdateMethod(t,"updateView"),Zm(e),this._scheduler.performVisualTasks(e,t,{setDirty:!0}),Fm(this,e,this._api,t,{}),fm.trigger("afterupdate",e,this._api))},updateVisual:function(n){var i=this,r=this._model;r&&(r.setUpdatePayload(n),r.eachSeries(function(t){t.getData().clearAllVisual()}),zg.markUpdateMethod(n,"updateVisual"),Zm(r),this._scheduler.performVisualTasks(r,n,{visualType:"visual",setDirty:!0}),r.eachComponent(function(t,e){"series"!==t&&(t=i.getViewOfComponentModel(e))&&t.__alive&&t.updateVisual(e,r,i._api,n)}),r.eachSeries(function(t){i._chartsMap[t.__viewId].updateVisual(t,r,i._api,n)}),fm.trigger("afterupdate",r,this._api))},updateLayout:function(t){Pm.update.call(this,t)}},Lm=function(t,e,n,i){if(t._disposed)t.id;else for(var r=t._model,o=t._coordSysMgr.getCoordinateSystems(),a=wo(r,n),s=0;s<o.length;s++){var l=o[s];if(l[e]&&null!=(l=l[e](r,a,i)))return l}},Om=function(t,e){var n=t._chartsMap,i=t._scheduler;e.eachSeries(function(t){i.updateStreamModes(t,n[t.__viewId])})},Rm=function(i,t){var r,o,a=this,e=this.getModel(),n=i.type,s=i.escapeConnect,l=t0[n],u=l.actionInfo,h=(u.update||"update").split(":"),c=h.pop(),p=null!=h[0]&&Ao(h[0]),h=(this[mm]=!0,[i]),d=!1,f=(i.batch&&(d=!0,h=B(i.batch,function(t){return(t=E(L({},t),i)).batch=null,t})),[]),g=Al(i),y=Pl(i);if(y&&vl(this._api),O(h,function(t){var e,n;(r=(r=l.action(t,a._model,a._api))||L({},t)).type=u.event||r.type,f.push(r),y?(e=(n=bo(i)).queryOptionMap,n=n.mainTypeSpecified?e.keys()[0]:"series",Am(a,c,t,n),Um(a)):g?(Am(a,c,t,"series"),Um(a)):p&&Am(a,c,t,p.main,p.sub)}),"none"!==c&&!y&&!g&&!p)try{this[vm]?(km(this),Pm.update.call(this,i),this[vm]=null):Pm[c].call(this,i)}catch(t){throw this[mm]=!1,t}r=d?{type:u.event||n,escapeConnect:s,batch:f}:f[0],this[mm]=!1,t||((h=this._messageCenter).trigger(r.type,r),g&&(d={type:"selectchanged",escapeConnect:s,selected:(o=[],e.eachSeries(function(n){O(n.getAllData(),function(t){t.data;var t=t.type,e=n.getSelectedDataIndices();0<e.length&&(e={dataIndex:e,seriesIndex:n.seriesIndex},null!=t&&(e.dataType=t),o.push(e))})}),o),isFromClick:i.isFromClick||!1,fromAction:i.type,fromActionPayload:i},h.trigger(d.type,d)))},Nm=function(t){for(var e=this._pendingActions;e.length;){var n=e.shift();Rm.call(this,n,t)}},zm=function(t){t||this.trigger("updated")},Em=function(e,n){e.on("rendered",function(t){n.trigger("rendered",t),!e.animation.isFinished()||n[vm]||n._scheduler.unfinished||n._pendingActions.length||n.trigger("finished")})},Bm=function(t,a){t.on("mouseover",function(t){var e,n,i,r,o=My(t.target,Dl);o&&(o=o,e=t,t=a._api,n=k(o),i=(r=wl(n.componentMainType,n.componentIndex,n.componentHighDownName,t)).dispatchers,r=r.focusSelf,i?(r&&xl(n.componentMainType,n.componentIndex,t),O(i,function(t){return hl(t,e)})):(_l(n.seriesIndex,n.focus,n.blurScope,t),"self"===n.focus&&xl(n.componentMainType,n.componentIndex,t),hl(o,e)),Um(a))}).on("mouseout",function(t){var e,n,i=My(t.target,Dl);i&&(i=i,e=t,vl(t=a._api),(n=wl((n=k(i)).componentMainType,n.componentIndex,n.componentHighDownName,t).dispatchers)?O(n,function(t){return cl(t,e)}):cl(i,e),Um(a))}).on("click",function(t){var e,t=My(t.target,function(t){return null!=k(t).dataIndex},!0);t&&(e=t.selected?"unselect":"select",t=k(t),a._api.dispatchAction({type:e,dataType:t.dataType,dataIndexInside:t.dataIndex,seriesIndex:t.seriesIndex,isFromClick:!0}))})},Fm=function(t,e,n,i,r){var o,a,s,l,u,h,c;u=[],c=!(h=[]),(o=e).eachComponent(function(t,e){var n=e.get("zlevel")||0,i=e.get("z")||0,r=e.getZLevelKey();c=c||!!r,("series"===t?h:u).push({zlevel:n,z:i,idx:e.componentIndex,type:t,key:r})}),c&&(Ue(a=u.concat(h),function(t,e){return t.zlevel===e.zlevel?t.z-e.z:t.zlevel-e.zlevel}),O(a,function(t){var e=o.getComponent(t.type,t.idx),n=t.zlevel,t=t.key;null!=s&&(n=Math.max(s,n)),t?(n===s&&t!==l&&n++,l=t):l&&(n===s&&n++,l=""),s=n,e.setZLevel(n)})),Vm(t,e,n,i,r),O(t._chartsViews,function(t){t.__alive=!1}),Hm(t,e,n,i,r),O(t._chartsViews,function(t){t.__alive||t.remove(e,n)})},Vm=function(t,n,i,r,e,o){O(o||t._componentsViews,function(t){var e=t.__model;$m(0,t),t.render(e,n,i,r),Km(e,t),Qm(e,t)})},Hm=function(r,t,e,o,n,a){var i,s,l,u,h=r._scheduler,c=(n=L(n||{},{updatedSeries:t.getSeries()}),fm.trigger("series:beforeupdate",t,e,n),!1);t.eachSeries(function(t){var e,n=r._chartsMap[t.__viewId],i=(n.__alive=!0,n.renderTask);h.updatePayload(i,o),$m(0,n),a&&a.get(t.uid)&&i.dirty(),i.perform(h.getPerformArgs(i))&&(c=!0),n.group.silent=!!t.get("silent"),i=n,e=t.get("blendMode")||null,i.eachRendered(function(t){t.isGroup||(t.style.blend=e)}),bl(t)}),h.unfinished=c||h.unfinished,fm.trigger("series:layoutlabels",t,e,n),fm.trigger("series:transition",t,e,n),t.eachSeries(function(t){var e=r._chartsMap[t.__viewId];Km(t,e),Qm(t,e)}),s=t,l=(i=r)._zr.storage,u=0,l.traverse(function(t){t.isGroup||u++}),u>s.get("hoverLayerThreshold")&&!b.node&&!b.worker&&s.eachSeries(function(t){t.preventUsingHoverLayer||(t=i._chartsMap[t.__viewId]).__alive&&t.eachRendered(function(t){t.states.emphasis&&(t.states.emphasis.hoverLayer=!0)})}),fm.trigger("series:afterupdate",t,e,n)},Um=function(t){t[_m]=!0,t.getZr().wakeUp()},Xm=function(t){t[_m]&&(t.getZr().storage.traverse(function(t){_h(t)||jm(t)}),t[_m]=!1)},Gm=function(n){return u(t,e=sd),t.prototype.getCoordinateSystems=function(){return n._coordSysMgr.getCoordinateSystems()},t.prototype.getComponentByElement=function(t){for(;t;){var e=t.__ecComponentInfo;if(null!=e)return n._model.getComponent(e.mainType,e.index);t=t.parent}},t.prototype.enterEmphasis=function(t,e){pl(t,e),Um(n)},t.prototype.leaveEmphasis=function(t,e){dl(t,e),Um(n)},t.prototype.enterBlur=function(t){ol(t,tl),Um(n)},t.prototype.leaveBlur=function(t){fl(t),Um(n)},t.prototype.enterSelect=function(t){gl(t),Um(n)},t.prototype.leaveSelect=function(t){yl(t),Um(n)},t.prototype.getModel=function(){return n.getModel()},t.prototype.getViewOfComponentModel=function(t){return n.getViewOfComponentModel(t)},t.prototype.getViewOfSeriesModel=function(t){return n.getViewOfSeriesModel(t)},new t(n);function t(){return null!==e&&e.apply(this,arguments)||this}var e},void(Wm=function(i){function r(t,e){for(var n=0;n<t.length;n++)t[n][wm]=e}O(e0,function(t,e){i._messageCenter.on(e,function(t){var e,n;!l0[i.group]||0===i[wm]||t&&t.escapeConnect||(e=i.makeActionFromEvent(t),n=[],O(s0,function(t){t!==i&&t.group===i.group&&n.push(t)}),r(n,0),O(n,function(t){1!==t[wm]&&t.dispatchAction(e)}),r(n,2))})})})),p);function p(t,e,n){var i=Ym.call(this,new _y)||this,t=(i._chartsViews=[],i._chartsMap={},i._componentsViews=[],i._componentsMap={},i._pendingActions=[],n=n||{},V(e)&&(e=o0[e]),i._dom=t,i._zr=Er(t,{renderer:n.renderer||"canvas",devicePixelRatio:n.devicePixelRatio,width:n.width,height:n.height,ssr:n.ssr,useDirtyRect:null!=n.useDirtyRect&&n.useDirtyRect})),n=(i._ssr=n.ssr,i._throttledZrFlush=Yg(ct(t.flush,t),17),(e=y(e))&&Od(e,!0),i._theme=e,i._locale=V(e=n.locale||Dc)?(n=Ic[e.toUpperCase()]||{},e===Mc||e===Tc?y(n):d(y(n),y(Ic[Cc]),!1)):d(y(e),y(Ic[Cc]),!1),i._coordSysMgr=new hd,i._api=Gm(i));function r(t,e){return t.__prio-e.__prio}return Ue(r0,r),Ue(n0,r),i._scheduler=new ny(i,n,n0,r0),i._messageCenter=new Cm,i._initEvents(),i.resize=ct(i.resize,i),t.animation.on("frame",i._onframe,i),Em(t,i),Bm(t,i),Dt(i),i}function Zm(t){t.clearColorPalette(),t.eachSeries(function(t){t.clearColorPalette()})}function jm(t){for(var e=[],n=t.currentStates,i=0;i<n.length;i++){var r=n[i];"emphasis"!==r&&"blur"!==r&&"select"!==r&&e.push(r)}t.selected&&t.states.select&&e.push("select"),t.hoverState===Vs&&t.states.emphasis?e.push("emphasis"):t.hoverState===Fs&&t.states.blur&&e.push("blur"),t.useStates(e)}function Km(t,e){var n,i;t.preventAutoZ||(n=t.get("z")||0,i=t.get("zlevel")||0,e.eachRendered(function(t){return function t(e,n,i,r){var o=e.getTextContent();var a=e.getTextGuideLine();var s=e.isGroup;if(s)for(var l=e.childrenRef(),u=0;u<l.length;u++)r=Math.max(t(l[u],n,i,r),r);else e.z=n,e.zlevel=i,r=Math.max(e.z2,r);o&&(o.z=n,o.zlevel=i,isFinite(r))&&(o.z2=r+2);a&&(s=e.textGuideLineConfig,a.z=n,a.zlevel=i,isFinite(r))&&(a.z2=r+(s&&s.showAbove?1:-1));return r}(t,n,i,-1/0),!0}))}function $m(t,e){e.eachRendered(function(t){var e,n;_h(t)||(e=t.getTextContent(),n=t.getTextGuideLine(),t.stateTransition&&(t.stateTransition=null),e&&e.stateTransition&&(e.stateTransition=null),n&&n.stateTransition&&(n.stateTransition=null),t.hasState()?(t.prevStates=t.currentStates,t.clearStates()):t.prevStates&&(t.prevStates=null))})}function Qm(t,e){var n=t.getModel("stateAnimation"),r=t.isAnimationEnabled(),t=n.get("duration"),o=0<t?{duration:t,delay:n.get("delay"),easing:n.get("easing")}:null;e.eachRendered(function(t){var e,n,i;t.states&&t.states.emphasis&&(_h(t)||(t instanceof j&&((i=zs(n=t)).normalFill=n.style.fill,i.normalStroke=n.style.stroke,n=n.states.select||{},i.selectFill=n.style&&n.style.fill||null,i.selectStroke=n.style&&n.style.stroke||null),t.__dirty&&(i=t.prevStates)&&t.useStates(i),r&&(t.stateTransition=o,n=t.getTextContent(),e=t.getTextGuideLine(),n&&(n.stateTransition=o),e)&&(e.stateTransition=o),t.__dirty&&jm(t)))})}var Ly=qm.prototype,Jm=(Ly.on=bm("on"),Ly.off=bm("off"),Ly.one=function(i,r,t){var o=this;this.on.call(this,i,function t(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];r&&r.apply&&r.apply(this,e),o.off(i,t)},t)},["click","dblclick","mouseover","mouseout","mousemove","mousedown","mouseup","globalout","contextmenu"]);var t0={},e0={},n0=[],i0=[],r0=[],o0={},a0={},s0={},l0={},u0=+new Date,h0=+new Date,c0="_echarts_instance_";function p0(t){l0[t]=!1}Dy=p0;function d0(t){return s0[e=c0,(t=t).getAttribute?t.getAttribute(e):t[e]];var e}function f0(t,e){o0[t]=e}function g0(t){I(i0,t)<0&&i0.push(t)}function y0(t,e){T0(n0,t,e,2e3)}function m0(t){_0("afterinit",t)}function v0(t){_0("afterupdate",t)}function _0(t,e){fm.on(t,e)}function x0(t,e,n){S(e)&&(n=e,e="");var i=R(t)?t.type:[t,t={event:e}][0];t.event=(t.event||i).toLowerCase(),e=t.event,e0[e]||(Ct(xm.test(i)&&xm.test(e)),t0[i]||(t0[i]={action:n,actionInfo:t}),e0[e]=i)}function w0(t,e){hd.register(t,e)}function b0(t,e){T0(r0,t,e,1e3,"layout")}function S0(t,e){T0(r0,t,e,3e3,"visual")}var M0=[];function T0(t,e,n,i,r){(S(e)||R(e))&&(n=e,e=i),0<=I(M0,n)||(M0.push(n),(i=ny.wrapStageHandler(n,r)).__prio=e,i.__raw=n,t.push(i))}function C0(t,e){a0[t]=e}function I0(t,e,n){var i=gm.registerMap;i&&i(t,e,n)}function k0(t){var e=(t=y(t)).type,n=(e||f(""),e.split(":")),i=(2!==n.length&&f(""),!1);"echarts"===n[0]&&(e=n[1],i=!0),t.__isBuiltIn=i,Vf.set(e,t)}S0(2e3,Fo),S0(4500,Nh),S0(4500,zc),S0(2e3,gc),S0(4500,cc),S0(7e3,function(e,i){e.eachRawSeries(function(t){var n;!e.isSeriesFiltered(t)&&((n=t.getData()).hasItemVisual()&&n.each(function(t){var e=n.getItemVisual(t,"decal");e&&(n.ensureUniqueItemVisual(t,"style").decal=pm(e,i))}),t=n.getVisual("decal"))&&(n.getVisual("style").decal=pm(t,i))})}),g0(Od),y0(900,function(t){var i=z();t.eachSeries(function(t){var e,n=t.get("stack");n&&(n=i.get(n)||i.set(n,[]),(t={stackResultDimension:(e=t.getData()).getCalculationInfo("stackResultDimension"),stackedOverDimension:e.getCalculationInfo("stackedOverDimension"),stackedDimension:e.getCalculationInfo("stackedDimension"),stackedByDimension:e.getCalculationInfo("stackedByDimension"),isStackedByIndex:e.getCalculationInfo("isStackedByIndex"),data:e,seriesModel:t}).stackedDimension)&&(t.isStackedByIndex||t.stackedByDimension)&&(n.length&&e.setCalculationInfo("stackedOnSeries",n[n.length-1].seriesModel),n.push(t))}),i.each(Rd)}),C0("default",function(i,r){E(r=r||{},{text:"loading",textColor:"#000",fontSize:12,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif",maskColor:"rgba(255, 255, 255, 0.8)",showSpinner:!0,color:"#5470c6",spinnerRadius:10,lineWidth:5,zlevel:0});var o,t=new Pr,a=new ms({style:{fill:r.maskColor},zlevel:r.zlevel,z:1e4}),s=(t.add(a),new bs({style:{text:r.text,fill:r.textColor,fontSize:r.fontSize,fontWeight:r.fontWeight,fontStyle:r.fontStyle,fontFamily:r.fontFamily},zlevel:r.zlevel,z:10001})),l=new ms({style:{fill:"none"},textContent:s,textConfig:{position:"right",distance:10},zlevel:r.zlevel,z:10001});return t.add(l),r.showSpinner&&((o=new Zu({shape:{startAngle:-ey/2,endAngle:-ey/2+.1,r:r.spinnerRadius},style:{stroke:r.color,lineCap:"round",lineWidth:r.lineWidth},zlevel:r.zlevel,z:10001})).animateShape(!0).when(1e3,{endAngle:3*ey/2}).start("circularInOut"),o.animateShape(!0).when(1e3,{startAngle:3*ey/2}).delay(300).start("circularInOut"),t.add(o)),t.resize=function(){var t=s.getBoundingRect().width,e=r.showSpinner?r.spinnerRadius:0,t=(i.getWidth()-2*e-(r.showSpinner&&t?10:0)-t)/2-(r.showSpinner&&t?0:5+t/2)+(r.showSpinner?0:t/2)+(t?0:e),n=i.getHeight()/2;r.showSpinner&&o.setShape({cx:t,cy:n}),l.setShape({x:t-e,y:n-e,width:2*e,height:2*e}),a.setShape({x:0,y:0,width:i.getWidth(),height:i.getHeight()})},t.resize(),t}),x0({type:Ws,event:Ws,update:Ws},Et),x0({type:Us,event:Us,update:Us},Et),x0({type:Xs,event:Xs,update:Xs},Et),x0({type:Ys,event:Ys,update:Ys},Et),x0({type:qs,event:qs,update:qs},Et),f0("light",mc),f0("dark",dc);function D0(t){return null==t?0:t.length||1}function A0(t){return t}L0.prototype.add=function(t){return this._add=t,this},L0.prototype.update=function(t){return this._update=t,this},L0.prototype.updateManyToOne=function(t){return this._updateManyToOne=t,this},L0.prototype.updateOneToMany=function(t){return this._updateOneToMany=t,this},L0.prototype.updateManyToMany=function(t){return this._updateManyToMany=t,this},L0.prototype.remove=function(t){return this._remove=t,this},L0.prototype.execute=function(){this[this._diffModeMultiple?"_executeMultiple":"_executeOneToOne"]()},L0.prototype._executeOneToOne=function(){var t=this._old,e=this._new,n={},i=new Array(t.length),r=new Array(e.length);this._initIndexMap(t,null,i,"_oldKeyGetter"),this._initIndexMap(e,n,r,"_newKeyGetter");for(var o=0;o<t.length;o++){var a,s=i[o],l=n[s],u=D0(l);1<u?(a=l.shift(),1===l.length&&(n[s]=l[0]),this._update&&this._update(a,o)):1===u?(n[s]=null,this._update&&this._update(l,o)):this._remove&&this._remove(o)}this._performRestAdd(r,n)},L0.prototype._executeMultiple=function(){var t=this._old,e=this._new,n={},i={},r=[],o=[];this._initIndexMap(t,n,r,"_oldKeyGetter"),this._initIndexMap(e,i,o,"_newKeyGetter");for(var a=0;a<r.length;a++){var s=r[a],l=n[s],u=i[s],h=D0(l),c=D0(u);if(1<h&&1===c)this._updateManyToOne&&this._updateManyToOne(u,l),i[s]=null;else if(1===h&&1<c)this._updateOneToMany&&this._updateOneToMany(u,l),i[s]=null;else if(1===h&&1===c)this._update&&this._update(u,l),i[s]=null;else if(1<h&&1<c)this._updateManyToMany&&this._updateManyToMany(u,l),i[s]=null;else if(1<h)for(var p=0;p<h;p++)this._remove&&this._remove(l[p]);else this._remove&&this._remove(l)}this._performRestAdd(o,i)},L0.prototype._performRestAdd=function(t,e){for(var n=0;n<t.length;n++){var i=t[n],r=e[i],o=D0(r);if(1<o)for(var a=0;a<o;a++)this._add&&this._add(r[a]);else 1===o&&this._add&&this._add(r);e[i]=null}},L0.prototype._initIndexMap=function(t,e,n,i){for(var r=this._diffModeMultiple,o=0;o<t.length;o++){var a,s,l="_ec_"+this[i](t[o],o);r||(n[o]=l),e&&(0===(s=D0(a=e[l]))?(e[l]=o,r&&n.push(l)):1===s?e[l]=[a,o]:a.push(o))}};var P0=L0;function L0(t,e,n,i,r,o){this._old=t,this._new=e,this._oldKeyGetter=n||A0,this._newKeyGetter=i||A0,this.context=r,this._diffModeMultiple="multiple"===o}R0.prototype.get=function(){return{fullDimensions:this._getFullDimensionNames(),encode:this._encode}},R0.prototype._getFullDimensionNames=function(){return this._cachedDimNames||(this._cachedDimNames=this._schema?this._schema.makeOutputDimensionNames():[]),this._cachedDimNames};var O0=R0;function R0(t,e){this._encode=t,this._schema=e}function N0(o,t){var e={},a=e.encode={},s=z(),l=[],u=[],h={},i=(O(o.dimensions,function(t){var e,n,i=o.getDimensionInfo(t),r=i.coordDim;r&&(e=i.coordDimIndex,z0(a,r)[e]=t,i.isExtraCoord||(s.set(r,1),"ordinal"!==(n=i.type)&&"time"!==n&&(l[0]=t),z0(h,r)[e]=o.getDimensionIndex(i.name)),i.defaultTooltip)&&u.push(t),Op.each(function(t,e){var n=z0(a,e),e=i.otherDims[e];null!=e&&!1!==e&&(n[e]=i.name)})}),[]),r={},n=(s.each(function(t,e){var n=a[e];r[e]=n[0],i=i.concat(n)}),e.dataDimsOnCoord=i,e.dataDimIndicesOnCoord=B(i,function(t){return o.getDimensionInfo(t).storeDimIndex}),e.encodeFirstDimNotExtra=r,a.label),n=(n&&n.length&&(l=n.slice()),a.tooltip);return n&&n.length?u=n.slice():u.length||(u=l.slice()),a.defaultedLabel=l,a.defaultedTooltip=u,e.userOutput=new O0(h,t),e}function z0(t,e){return t.hasOwnProperty(e)||(t[e]=[]),t[e]}var E0=function(t){this.otherDims={},null!=t&&L(this,t)},B0=_o(),F0={float:"f",int:"i",ordinal:"o",number:"n",time:"t"},V0=(H0.prototype.isDimensionOmitted=function(){return this._dimOmitted},H0.prototype._updateDimOmitted=function(t){(this._dimOmitted=t)&&!this._dimNameMap&&(this._dimNameMap=U0(this.source))},H0.prototype.getSourceDimensionIndex=function(t){return N(this._dimNameMap.get(t),-1)},H0.prototype.getSourceDimension=function(t){var e=this.source.dimensionsDefine;if(e)return e[t]},H0.prototype.makeStoreSchema=function(){for(var t=this._fullDimCount,e=Ud(this.source),n=!(30<t),i="",r=[],o=0,a=0;o<t;o++){var s,l=void 0,u=void 0,h=void 0,c=this.dimensions[a];c&&c.storeDimIndex===o?(l=e?c.name:null,u=c.type,h=c.ordinalMeta,a++):(s=this.getSourceDimension(o))&&(l=e?s.name:null,u=s.type),r.push({property:l,type:u,ordinalMeta:h}),!e||null==l||c&&c.isCalculationCoord||(i+=n?l.replace(/\`/g,"`1").replace(/\$/g,"`2"):l),i=i+"$"+(F0[u]||"f"),h&&(i+=h.uid),i+="$"}var p=this.source;return{dimensions:r,hash:[p.seriesLayoutBy,p.startIndex,i].join("$$")}},H0.prototype.makeOutputDimensionNames=function(){for(var t=[],e=0,n=0;e<this._fullDimCount;e++){var i=void 0,r=this.dimensions[n];r&&r.storeDimIndex===e?(r.isCalculationCoord||(i=r.name),n++):(r=this.getSourceDimension(e))&&(i=r.name),t.push(i)}return t},H0.prototype.appendCalculationDimension=function(t){this.dimensions.push(t),t.isCalculationCoord=!0,this._fullDimCount++,this._updateDimOmitted(!0)},H0);function H0(t){this.dimensions=t.dimensions,this._dimOmitted=t.dimensionOmitted,this.source=t.source,this._fullDimCount=t.fullDimensionCount,this._updateDimOmitted(t.dimensionOmitted)}function G0(t){return t instanceof V0}function W0(t){for(var e=z(),n=0;n<(t||[]).length;n++){var i=t[n],i=R(i)?i.name:i;null!=i&&null==e.get(i)&&e.set(i,n)}return e}function U0(t){var e=B0(t);return e.dimNameMap||(e.dimNameMap=W0(t.dimensionsDefine))}var X0,Y0,q0,Z0,j0,K0,$0,Q0=R,J0=B,tv="undefined"==typeof Int32Array?Array:Int32Array,ev=["hasItemOption","_nameList","_idList","_invertedIndicesMap","_dimSummary","userOutput","_rawData","_dimValueGetter","_nameDimIdx","_idDimIdx","_nameRepeatCount"],nv=["_approximateExtent"],iv=(m.prototype.getDimension=function(t){var e;return null==(e=this._recognizeDimIndex(t))?t:(e=t,this._dimOmitted?null!=(t=this._dimIdxToName.get(e))?t:(t=this._schema.getSourceDimension(e))?t.name:void 0:this.dimensions[e])},m.prototype.getDimensionIndex=function(t){var e=this._recognizeDimIndex(t);return null!=e?e:null==t?-1:(e=this._getDimInfo(t))?e.storeDimIndex:this._dimOmitted?this._schema.getSourceDimensionIndex(t):-1},m.prototype._recognizeDimIndex=function(t){if(ft(t)||null!=t&&!isNaN(t)&&!this._getDimInfo(t)&&(!this._dimOmitted||this._schema.getSourceDimensionIndex(t)<0))return+t},m.prototype._getStoreDimIndex=function(t){return this.getDimensionIndex(t)},m.prototype.getDimensionInfo=function(t){return this._getDimInfo(this.getDimension(t))},m.prototype._initGetDimensionInfo=function(t){var e=this._dimInfos;this._getDimInfo=t?function(t){return e.hasOwnProperty(t)?e[t]:void 0}:function(t){return e[t]}},m.prototype.getDimensionsOnCoord=function(){return this._dimSummary.dataDimsOnCoord.slice()},m.prototype.mapDimension=function(t,e){var n=this._dimSummary;return null==e?n.encodeFirstDimNotExtra[t]:(n=n.encode[t])?n[e]:null},m.prototype.mapDimensionsAll=function(t){return(this._dimSummary.encode[t]||[]).slice()},m.prototype.getStore=function(){return this._store},m.prototype.initData=function(t,e,n){var i,r,o=this;(i=t instanceof $f?t:i)||(r=this.dimensions,t=Bd(t)||st(t)?new Zd(t,r.length):t,i=new $f,r=J0(r,function(t){return{type:o._dimInfos[t].type,property:t}}),i.initData(t,r,n)),this._store=i,this._nameList=(e||[]).slice(),this._idList=[],this._nameRepeatCount={},this._doInit(0,i.count()),this._dimSummary=N0(this,this._schema),this.userOutput=this._dimSummary.userOutput},m.prototype.appendData=function(t){t=this._store.appendData(t);this._doInit(t[0],t[1])},m.prototype.appendValues=function(t,e){var t=this._store.appendValues(t,e.length),n=t.start,i=t.end,r=this._shouldMakeIdFromName();if(this._updateOrdinalMeta(),e)for(var o=n;o<i;o++)this._nameList[o]=e[o-n],r&&$0(this,o)},m.prototype._updateOrdinalMeta=function(){for(var t=this._store,e=this.dimensions,n=0;n<e.length;n++){var i=this._dimInfos[e[n]];i.ordinalMeta&&t.collectOrdinalMeta(i.storeDimIndex,i.ordinalMeta)}},m.prototype._shouldMakeIdFromName=function(){var t=this._store.getProvider();return null==this._idDimIdx&&t.getSource().sourceFormat!==Bp&&!t.fillStorage},m.prototype._doInit=function(t,e){if(!(e<=t)){var n=this._store.getProvider(),i=(this._updateOrdinalMeta(),this._nameList),r=this._idList;if(n.getSource().sourceFormat===Rp&&!n.pure)for(var o=[],a=t;a<e;a++){var s,l=n.getItem(a,o);this.hasItemOption||!R(s=l)||s instanceof Array||(this.hasItemOption=!0),l&&(s=l.name,null==i[a]&&null!=s&&(i[a]=fo(s,null)),l=l.id,null==r[a])&&null!=l&&(r[a]=fo(l,null))}if(this._shouldMakeIdFromName())for(a=t;a<e;a++)$0(this,a);X0(this)}},m.prototype.getApproximateExtent=function(t){return this._approximateExtent[t]||this._store.getDataExtent(this._getStoreDimIndex(t))},m.prototype.setApproximateExtent=function(t,e){e=this.getDimension(e),this._approximateExtent[e]=t.slice()},m.prototype.getCalculationInfo=function(t){return this._calculationInfo[t]},m.prototype.setCalculationInfo=function(t,e){Q0(t)?L(this._calculationInfo,t):this._calculationInfo[t]=e},m.prototype.getName=function(t){var t=this.getRawIndex(t),e=this._nameList[t];return e=null==(e=null==e&&null!=this._nameDimIdx?q0(this,this._nameDimIdx,t):e)?"":e},m.prototype._getCategory=function(t,e){e=this._store.get(t,e),t=this._store.getOrdinalMeta(t);return t?t.categories[e]:e},m.prototype.getId=function(t){return Y0(this,this.getRawIndex(t))},m.prototype.count=function(){return this._store.count()},m.prototype.get=function(t,e){var n=this._store,t=this._dimInfos[t];if(t)return n.get(t.storeDimIndex,e)},m.prototype.getByRawIndex=function(t,e){var n=this._store,t=this._dimInfos[t];if(t)return n.getByRawIndex(t.storeDimIndex,e)},m.prototype.getIndices=function(){return this._store.getIndices()},m.prototype.getDataExtent=function(t){return this._store.getDataExtent(this._getStoreDimIndex(t))},m.prototype.getSum=function(t){return this._store.getSum(this._getStoreDimIndex(t))},m.prototype.getMedian=function(t){return this._store.getMedian(this._getStoreDimIndex(t))},m.prototype.getValues=function(t,e){var n=this,i=this._store;return F(t)?i.getValues(J0(t,function(t){return n._getStoreDimIndex(t)}),e):i.getValues(t)},m.prototype.hasValue=function(t){for(var e=this._dimSummary.dataDimIndicesOnCoord,n=0,i=e.length;n<i;n++)if(isNaN(this._store.get(e[n],t)))return!1;return!0},m.prototype.indexOfName=function(t){for(var e=0,n=this._store.count();e<n;e++)if(this.getName(e)===t)return e;return-1},m.prototype.getRawIndex=function(t){return this._store.getRawIndex(t)},m.prototype.indexOfRawIndex=function(t){return this._store.indexOfRawIndex(t)},m.prototype.rawIndexOf=function(t,e){t=(t&&this._invertedIndicesMap[t])[e];return null==t||isNaN(t)?-1:t},m.prototype.indicesOfNearest=function(t,e,n){return this._store.indicesOfNearest(this._getStoreDimIndex(t),e,n)},m.prototype.each=function(t,e,n){S(t)&&(n=e,e=t,t=[]);n=n||this,t=J0(Z0(t),this._getStoreDimIndex,this);this._store.each(t,n?ct(e,n):e)},m.prototype.filterSelf=function(t,e,n){S(t)&&(n=e,e=t,t=[]);n=n||this,t=J0(Z0(t),this._getStoreDimIndex,this);return this._store=this._store.filter(t,n?ct(e,n):e),this},m.prototype.selectRange=function(n){var i=this,r={};return O(ht(n),function(t){var e=i._getStoreDimIndex(t);r[e]=n[t]}),this._store=this._store.selectRange(r),this},m.prototype.mapArray=function(t,e,n){S(t)&&(n=e,e=t,t=[]);var i=[];return this.each(t,function(){i.push(e&&e.apply(this,arguments))},n=n||this),i},m.prototype.map=function(t,e,n,i){n=n||i||this,i=J0(Z0(t),this._getStoreDimIndex,this),t=K0(this);return t._store=this._store.map(i,n?ct(e,n):e),t},m.prototype.modify=function(t,e,n,i){n=n||i||this,i=J0(Z0(t),this._getStoreDimIndex,this);this._store.modify(i,n?ct(e,n):e)},m.prototype.downSample=function(t,e,n,i){var r=K0(this);return r._store=this._store.downSample(this._getStoreDimIndex(t),e,n,i),r},m.prototype.lttbDownSample=function(t,e){var n=K0(this);return n._store=this._store.lttbDownSample(this._getStoreDimIndex(t),e),n},m.prototype.getRawDataItem=function(t){return this._store.getRawDataItem(t)},m.prototype.getItemModel=function(t){var e=this.hostModel,t=this.getRawDataItem(t);return new xc(t,e,e&&e.ecModel)},m.prototype.diff=function(e){var n=this;return new P0(e?e.getStore().getIndices():[],this.getStore().getIndices(),function(t){return Y0(e,t)},function(t){return Y0(n,t)})},m.prototype.getVisual=function(t){var e=this._visual;return e&&e[t]},m.prototype.setVisual=function(t,e){this._visual=this._visual||{},Q0(t)?L(this._visual,t):this._visual[t]=e},m.prototype.getItemVisual=function(t,e){t=this._itemVisuals[t],t=t&&t[e];return null==t?this.getVisual(e):t},m.prototype.hasItemVisual=function(){return 0<this._itemVisuals.length},m.prototype.ensureUniqueItemVisual=function(t,e){var n=this._itemVisuals,i=n[t],n=(i=i||(n[t]={}))[e];return null==n&&(F(n=this.getVisual(e))?n=n.slice():Q0(n)&&(n=L({},n)),i[e]=n),n},m.prototype.setItemVisual=function(t,e,n){var i=this._itemVisuals[t]||{};this._itemVisuals[t]=i,Q0(e)?L(i,e):i[e]=n},m.prototype.clearAllVisual=function(){this._visual={},this._itemVisuals=[]},m.prototype.setLayout=function(t,e){Q0(t)?L(this._layout,t):this._layout[t]=e},m.prototype.getLayout=function(t){return this._layout[t]},m.prototype.getItemLayout=function(t){return this._itemLayouts[t]},m.prototype.setItemLayout=function(t,e,n){this._itemLayouts[t]=n?L(this._itemLayouts[t]||{},e):e},m.prototype.clearItemLayouts=function(){this._itemLayouts.length=0},m.prototype.setItemGraphicEl=function(t,e){var n,i,r,o,a=this.hostModel&&this.hostModel.seriesIndex;n=a,i=this.dataType,r=t,(a=e)&&((o=k(a)).dataIndex=r,o.dataType=i,o.seriesIndex=n,"group"===a.type)&&a.traverse(function(t){t=k(t);t.seriesIndex=n,t.dataIndex=r,t.dataType=i}),this._graphicEls[t]=e},m.prototype.getItemGraphicEl=function(t){return this._graphicEls[t]},m.prototype.eachItemGraphicEl=function(n,i){O(this._graphicEls,function(t,e){t&&n&&n.call(i,t,e)})},m.prototype.cloneShallow=function(t){return t=t||new m(this._schema||J0(this.dimensions,this._getDimInfo,this),this.hostModel),j0(t,this),t._store=this._store,t},m.prototype.wrapMethod=function(t,e){var n=this[t];S(n)&&(this.__wrappedMethods=this.__wrappedMethods||[],this.__wrappedMethods.push(t),this[t]=function(){var t=n.apply(this,arguments);return e.apply(this,[t].concat(Mt(arguments)))})},m.internalField=(X0=function(a){var s=a._invertedIndicesMap;O(s,function(t,e){var n=a._dimInfos[e],i=n.ordinalMeta,r=a._store;if(i){t=s[e]=new tv(i.categories.length);for(var o=0;o<t.length;o++)t[o]=-1;for(o=0;o<r.count();o++)t[r.get(n.storeDimIndex,o)]=o}})},q0=function(t,e,n){return fo(t._getCategory(e,n),null)},Y0=function(t,e){var n=t._idList[e];return n=null==(n=null==n&&null!=t._idDimIdx?q0(t,t._idDimIdx,e):n)?"e\0\0"+e:n},Z0=function(t){return t=F(t)?t:null!=t?[t]:[]},K0=function(t){var e=new m(t._schema||J0(t.dimensions,t._getDimInfo,t),t.hostModel);return j0(e,t),e},j0=function(e,n){O(ev.concat(n.__wrappedMethods||[]),function(t){n.hasOwnProperty(t)&&(e[t]=n[t])}),e.__wrappedMethods=n.__wrappedMethods,O(nv,function(t){e[t]=y(n[t])}),e._calculationInfo=L({},n._calculationInfo)},void($0=function(t,e){var n=t._nameList,i=t._idList,r=t._nameDimIdx,o=t._idDimIdx,a=n[e],s=i[e];null==a&&null!=r&&(n[e]=a=q0(t,r,e)),null==s&&null!=o&&(i[e]=s=q0(t,o,e)),null==s&&null!=a&&(s=a,1<(r=(n=t._nameRepeatCount)[a]=(n[a]||0)+1)&&(s+="__ec__"+r),i[e]=s)})),m);function m(t,e){this.type="list",this._dimOmitted=!1,this._nameList=[],this._idList=[],this._visual={},this._layout={},this._itemVisuals=[],this._itemLayouts=[],this._graphicEls=[],this._approximateExtent={},this._calculationInfo={},this.hasItemOption=!1,this.TRANSFERABLE_METHODS=["cloneShallow","downSample","lttbDownSample","map"],this.CHANGABLE_METHODS=["filterSelf","selectRange"];for(var n,i,r=!(this.DOWNSAMPLE_METHODS=["downSample","lttbDownSample"]),o=(G0(t)?(n=t.dimensions,this._dimOmitted=t.isDimensionOmitted(),this._schema=t):(r=!0,n=t),n=n||["x","y"],{}),a=[],s={},l=!1,u={},h=0;h<n.length;h++){var c=n[h],c=V(c)?new E0({name:c}):c instanceof E0?c:new E0(c),p=c.name,d=(c.type=c.type||"float",c.coordDim||(c.coordDim=p,c.coordDimIndex=0),c.otherDims=c.otherDims||{});a.push(p),null!=u[p]&&(l=!0),(o[p]=c).createInvertedIndices&&(s[p]=[]),0===d.itemName&&(this._nameDimIdx=h),0===d.itemId&&(this._idDimIdx=h),r&&(c.storeDimIndex=h)}this.dimensions=a,this._dimInfos=o,this._initGetDimensionInfo(l),this.hostModel=e,this._invertedIndicesMap=s,this._dimOmitted&&(i=this._dimIdxToName=z(),O(a,function(t){i.set(o[t].storeDimIndex,t)}))}function rv(t,e){Bd(t)||(t=Vd(t));for(var n,i,r=(e=e||{}).coordDimensions||[],o=e.dimensionsDefine||t.dimensionsDefine||[],a=z(),s=[],l=(u=t,n=r,p=e.dimensionsCount,i=Math.max(u.dimensionsDetectedCount||1,n.length,o.length,p||0),O(n,function(t){R(t)&&(t=t.dimsDef)&&(i=Math.max(i,t.length))}),i),u=e.canOmitUnusedDimensions&&30<l,h=o===t.dimensionsDefine,c=h?U0(t):W0(o),p=e.encodeDefine,d=z(p=!p&&e.encodeDefaulter?e.encodeDefaulter(t,l):p),f=new Yf(l),g=0;g<f.length;g++)f[g]=-1;function y(t){var e,n,i,r=f[t];return r<0?(e=R(e=o[t])?e:{name:e},n=new E0,null!=(i=e.name)&&null!=c.get(i)&&(n.name=n.displayName=i),null!=e.type&&(n.type=e.type),null!=e.displayName&&(n.displayName=e.displayName),f[t]=s.length,n.storeDimIndex=t,s.push(n),n):s[r]}if(!u)for(g=0;g<l;g++)y(g);d.each(function(t,n){var i,t=ao(t).slice();1===t.length&&!V(t[0])&&t[0]<0?d.set(n,!1):(i=d.set(n,[]),O(t,function(t,e){t=V(t)?c.get(t):t;null!=t&&t<l&&v(y(i[e]=t),n,e)}))});var m=0;function v(t,e,n){null!=Op.get(e)?t.otherDims[e]=n:(t.coordDim=e,t.coordDimIndex=n,a.set(e,!0))}O(r,function(t){V(t)?(o=t,r={}):(o=(r=t).name,t=r.ordinalMeta,r.ordinalMeta=null,(r=L({},r)).ordinalMeta=t,n=r.dimsDef,i=r.otherDims,r.name=r.coordDim=r.coordDimIndex=r.dimsDef=r.otherDims=null);var n,i,r,o,e=d.get(o);if(!1!==e){if(!(e=ao(e)).length)for(var a=0;a<(n&&n.length||1);a++){for(;m<l&&null!=y(m).coordDim;)m++;m<l&&e.push(m++)}O(e,function(t,e){t=y(t);h&&null!=r.type&&(t.type=r.type),v(E(t,r),o,e),null==t.name&&n&&(R(e=n[e])||(e={name:e}),t.name=t.displayName=e.name,t.defaultTooltip=e.defaultTooltip),i&&E(t.otherDims,i)})}});var _=e.generateCoord,x=null!=(w=e.generateCoordCount),w=_?w||1:0,b=_||"value";function S(t){null==t.name&&(t.name=t.coordDim)}if(u)O(s,function(t){S(t)}),s.sort(function(t,e){return t.storeDimIndex-e.storeDimIndex});else for(var M=0;M<l;M++){var T=y(M);null==T.coordDim&&(T.coordDim=function(t,e,n){var i=e.data;if(n||i.hasOwnProperty(t)){for(var r=0;i.hasOwnProperty(t+r);)r++;t+=r}return e.set(t,!0),t}(b,a,x),T.coordDimIndex=0,(!_||w<=0)&&(T.isExtraCoord=!0),w--),S(T),null!=T.type||Yp(t,M)!==Gp.Must&&(!T.isExtraCoord||null==T.otherDims.itemName&&null==T.otherDims.seriesName)||(T.type="ordinal")}for(var C=s,I=z(),k=0;k<C.length;k++){var D=C[k],A=D.name,P=I.get(A)||0;0<P&&(D.name=A+(P-1)),P++,I.set(A,P)}return new V0({source:t,dimensions:s,fullDimensionCount:l,dimensionOmitted:u})}var ov=function(t){this.coordSysDims=[],this.axisMap=z(),this.categoryAxisMap=z(),this.coordSysName=t};var av={cartesian2d:function(t,e,n,i){var r=t.getReferringComponents("xAxis",So).models[0],t=t.getReferringComponents("yAxis",So).models[0];e.coordSysDims=["x","y"],n.set("x",r),n.set("y",t),sv(r)&&(i.set("x",r),e.firstCategoryDimIndex=0),sv(t)&&(i.set("y",t),null==e.firstCategoryDimIndex)&&(e.firstCategoryDimIndex=1)},singleAxis:function(t,e,n,i){t=t.getReferringComponents("singleAxis",So).models[0];e.coordSysDims=["single"],n.set("single",t),sv(t)&&(i.set("single",t),e.firstCategoryDimIndex=0)},polar:function(t,e,n,i){var t=t.getReferringComponents("polar",So).models[0],r=t.findAxisModel("radiusAxis"),t=t.findAxisModel("angleAxis");e.coordSysDims=["radius","angle"],n.set("radius",r),n.set("angle",t),sv(r)&&(i.set("radius",r),e.firstCategoryDimIndex=0),sv(t)&&(i.set("angle",t),null==e.firstCategoryDimIndex)&&(e.firstCategoryDimIndex=1)},geo:function(t,e,n,i){e.coordSysDims=["lng","lat"]},parallel:function(t,i,r,o){var a=t.ecModel,t=a.getComponent("parallel",t.get("parallelIndex")),s=i.coordSysDims=t.dimensions.slice();O(t.parallelAxisIndex,function(t,e){var t=a.getComponent("parallelAxis",t),n=s[e];r.set(n,t),sv(t)&&(o.set(n,t),null==i.firstCategoryDimIndex)&&(i.firstCategoryDimIndex=e)})}};function sv(t){return"category"===t.get("type")}function lv(t,e,n){var i,r,o,a,s,l,u,h,c,p=(n=n||{}).byIndex,d=n.stackedCoordDimension,f=(G0(e.schema)?(r=e.schema,i=r.dimensions,o=e.store):i=e,!(!t||!t.get("stack")));return O(i,function(t,e){V(t)&&(i[e]=t={name:t}),f&&!t.isExtraCoord&&(p||a||!t.ordinalMeta||(a=t),s||"ordinal"===t.type||"time"===t.type||d&&d!==t.coordDim||(s=t))}),!s||p||a||(p=!0),s&&(l="__\0ecstackresult_"+t.id,u="__\0ecstackedover_"+t.id,a&&(a.createInvertedIndices=!0),h=s.coordDim,n=s.type,c=0,O(i,function(t){t.coordDim===h&&c++}),e={name:l,coordDim:h,coordDimIndex:c,type:n,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:i.length},t={name:u,coordDim:u,coordDimIndex:c+1,type:n,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:i.length+1},r?(o&&(e.storeDimIndex=o.ensureCalculationDimension(u,n),t.storeDimIndex=o.ensureCalculationDimension(l,n)),r.appendCalculationDimension(e),r.appendCalculationDimension(t)):(i.push(e),i.push(t))),{stackedDimension:s&&s.name,stackedByDimension:a&&a.name,isStackedByIndex:p,stackedOverDimension:u,stackResultDimension:l}}function uv(t,e){return!!e&&e===t.getCalculationInfo("stackedDimension")}function hv(t,e){return uv(t,e)?t.getCalculationInfo("stackResultDimension"):e}function cv(t,e,n){n=n||{};var i,r,o,a,s,l,u=e.getSourceManager(),h=!1,t=(t?(h=!0,i=Vd(t)):h=(i=u.getSource()).sourceFormat===Rp,function(t){var e=t.get("coordinateSystem"),n=new ov(e);if(e=av[e])return e(t,n,n.axisMap,n.categoryAxisMap),n}(e)),c=(r=t,c=(c=e).get("coordinateSystem"),c=hd.get(c),p=(p=r&&r.coordSysDims?B(r.coordSysDims,function(t){var e={name:t},t=r.axisMap.get(t);return t&&(t=t.get("type"),e.type="category"===(t=t)?"ordinal":"time"===t?"time":"float"),e}):p)||c&&(c.getDimensionsInfo?c.getDimensionsInfo():c.dimensions.slice())||["x","y"]),p=n.useEncodeDefaulter,p=S(p)?p:p?pt(Up,c,e):null,c={coordDimensions:c,generateCoord:n.generateCoord,encodeDefine:e.getEncode(),encodeDefaulter:p,canOmitUnusedDimensions:!h},p=rv(i,c),d=(c=p.dimensions,o=n.createInvertedIndices,(a=t)&&O(c,function(t,e){var n=t.coordDim,n=a.categoryAxisMap.get(n);n&&(null==s&&(s=e),t.ordinalMeta=n.getOrdinalMeta(),o)&&(t.createInvertedIndices=!0),null!=t.otherDims.itemName&&(l=!0)}),l||null==s||(c[s].otherDims.itemName=0),s),n=h?null:u.getSharedDataStore(p),t=lv(e,{schema:p,store:n}),c=new iv(p,e),p=(c.setCalculationInfo(t),null==d||(u=i).sourceFormat!==Rp||F(uo(function(t){var e=0;for(;e<t.length&&null==t[e];)e++;return t[e]}(u.data||[])))?null:function(t,e,n,i){return i===d?n:this.defaultDimValueGetter(t,e,n,i)});return c.hasItemOption=!1,c.initData(h?i:n,null,p),c}dv.prototype.getSetting=function(t){return this._setting[t]},dv.prototype.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1])},dv.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},dv.prototype.getExtent=function(){return this._extent.slice()},dv.prototype.setExtent=function(t,e){var n=this._extent;isNaN(t)||(n[0]=t),isNaN(e)||(n[1]=e)},dv.prototype.isInExtentRange=function(t){return this._extent[0]<=t&&this._extent[1]>=t},dv.prototype.isBlank=function(){return this._isBlank},dv.prototype.setBlank=function(t){this._isBlank=t};var pv=dv;function dv(t){this._setting=t||{},this._extent=[1/0,-1/0]}zo(pv);var fv=0,gv=(yv.createByAxisModel=function(t){var t=t.option,e=t.data,e=e&&B(e,mv);return new yv({categories:e,needCollect:!e,deduplication:!1!==t.dedplication})},yv.prototype.getOrdinal=function(t){return this._getOrCreateMap().get(t)},yv.prototype.parseAndCollect=function(t){var e,n,i=this._needCollect;return V(t)||i?(i&&!this._deduplication?(n=this.categories.length,this.categories[n]=t):null==(n=(e=this._getOrCreateMap()).get(t))&&(i?(n=this.categories.length,this.categories[n]=t,e.set(t,n)):n=NaN),n):t},yv.prototype._getOrCreateMap=function(){return this._map||(this._map=z(this.categories))},yv);function yv(t){this.categories=t.categories||[],this._needCollect=t.needCollect,this._deduplication=t.deduplication,this.uid=++fv}function mv(t){return R(t)&&null!=t.value?t.value:t+""}function vv(t){return"interval"===t.type||"log"===t.type}function _v(t,e,n,i){var r={},o=t[1]-t[0],o=r.interval=Jr(o/e,!0),e=(null!=n&&o<n&&(o=r.interval=n),null!=i&&i<o&&(o=r.interval=i),r.intervalPrecision=wv(o)),n=r.niceTickExtent=[Wr(Math.ceil(t[0]/o)*o,e),Wr(Math.floor(t[1]/o)*o,e)];return i=n,o=t,isFinite(i[0])||(i[0]=o[0]),isFinite(i[1])||(i[1]=o[1]),bv(i,0,o),bv(i,1,o),i[0]>i[1]&&(i[0]=i[1]),r}function xv(t){var e=Math.pow(10,Qr(t)),t=t/e;return t?2===t?t=3:3===t?t=5:t*=2:t=1,Wr(t*e)}function wv(t){return Ur(t)+2}function bv(t,e,n){t[e]=Math.max(Math.min(t[e],n[1]),n[0])}function Sv(t,e){return t>=e[0]&&t<=e[1]}function Mv(t,e){return e[1]===e[0]?.5:(t-e[0])/(e[1]-e[0])}function Tv(t,e){return t*(e[1]-e[0])+e[0]}u(kv,Cv=pv),kv.prototype.parse=function(t){return V(t)?this._ordinalMeta.getOrdinal(t):Math.round(t)},kv.prototype.contain=function(t){return Sv(t=this.parse(t),this._extent)&&null!=this._ordinalMeta.categories[t]},kv.prototype.normalize=function(t){return Mv(t=this._getTickNumber(this.parse(t)),this._extent)},kv.prototype.scale=function(t){return t=Math.round(Tv(t,this._extent)),this.getRawOrdinalNumber(t)},kv.prototype.getTicks=function(){for(var t=[],e=this._extent,n=e[0];n<=e[1];)t.push({value:n}),n++;return t},kv.prototype.getMinorTicks=function(t){},kv.prototype.setSortInfo=function(t){if(null==t)this._ordinalNumbersByTick=this._ticksByOrdinalNumber=null;else{for(var e=t.ordinalNumbers,n=this._ordinalNumbersByTick=[],i=this._ticksByOrdinalNumber=[],r=0,o=this._ordinalMeta.categories.length,a=Math.min(o,e.length);r<a;++r){var s=e[r];i[n[r]=s]=r}for(var l=0;r<o;++r){for(;null!=i[l];)l++;n.push(l),i[l]=r}}},kv.prototype._getTickNumber=function(t){var e=this._ticksByOrdinalNumber;return e&&0<=t&&t<e.length?e[t]:t},kv.prototype.getRawOrdinalNumber=function(t){var e=this._ordinalNumbersByTick;return e&&0<=t&&t<e.length?e[t]:t},kv.prototype.getLabel=function(t){if(!this.isBlank())return t=this.getRawOrdinalNumber(t.value),null==(t=this._ordinalMeta.categories[t])?"":t+""},kv.prototype.count=function(){return this._extent[1]-this._extent[0]+1},kv.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},kv.prototype.isInExtentRange=function(t){return t=this._getTickNumber(t),this._extent[0]<=t&&this._extent[1]>=t},kv.prototype.getOrdinalMeta=function(){return this._ordinalMeta},kv.prototype.calcNiceTicks=function(){},kv.prototype.calcNiceExtent=function(){},kv.type="ordinal";var Cv,Iv=kv;function kv(t){var t=Cv.call(this,t)||this,e=(t.type="ordinal",t.getSetting("ordinalMeta"));return F(e=e||new gv({}))&&(e=new gv({categories:B(e,function(t){return R(t)?t.value:t})})),t._ordinalMeta=e,t._extent=t.getSetting("extent")||[0,e.categories.length-1],t}pv.registerClass(Iv);var Dv,Av=Wr,Pv=(u(Lv,Dv=pv),Lv.prototype.parse=function(t){return t},Lv.prototype.contain=function(t){return Sv(t,this._extent)},Lv.prototype.normalize=function(t){return Mv(t,this._extent)},Lv.prototype.scale=function(t){return Tv(t,this._extent)},Lv.prototype.setExtent=function(t,e){var n=this._extent;isNaN(t)||(n[0]=parseFloat(t)),isNaN(e)||(n[1]=parseFloat(e))},Lv.prototype.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1]),this.setExtent(e[0],e[1])},Lv.prototype.getInterval=function(){return this._interval},Lv.prototype.setInterval=function(t){this._interval=t,this._niceExtent=this._extent.slice(),this._intervalPrecision=wv(t)},Lv.prototype.getTicks=function(t){var e=this._interval,n=this._extent,i=this._niceExtent,r=this._intervalPrecision,o=[];if(e){n[0]<i[0]&&o.push(t?{value:Av(i[0]-e,r)}:{value:n[0]});for(var a=i[0];a<=i[1]&&(o.push({value:a}),(a=Av(a+e,r))!==o[o.length-1].value);)if(1e4<o.length)return[];var s=o.length?o[o.length-1].value:i[1];n[1]>s&&o.push(t?{value:Av(s+e,r)}:{value:n[1]})}return o},Lv.prototype.getMinorTicks=function(t){for(var e=this.getTicks(!0),n=[],i=this.getExtent(),r=1;r<e.length;r++){for(var o=e[r],a=e[r-1],s=0,l=[],u=(o.value-a.value)/t;s<t-1;){var h=Av(a.value+(s+1)*u);h>i[0]&&h<i[1]&&l.push(h),s++}n.push(l)}return n},Lv.prototype.getLabel=function(t,e){return null==t?"":(null==(e=e&&e.precision)?e=Ur(t.value)||0:"auto"===e&&(e=this._intervalPrecision),rp(Av(t.value,e,!0)))},Lv.prototype.calcNiceTicks=function(t,e,n){t=t||5;var i=this._extent,r=i[1]-i[0];isFinite(r)&&(r<0&&i.reverse(),r=_v(i,t,e,n),this._intervalPrecision=r.intervalPrecision,this._interval=r.interval,this._niceExtent=r.niceTickExtent)},Lv.prototype.calcNiceExtent=function(t){var e=this._extent,n=(e[0]===e[1]&&(0!==e[0]?(n=e[0],t.fixMax||(e[1]+=n/2),e[0]-=n/2):e[1]=1),e[1]-e[0]),n=(isFinite(n)||(e[0]=0,e[1]=1),this.calcNiceTicks(t.splitNumber,t.minInterval,t.maxInterval),this._interval);t.fixMin||(e[0]=Av(Math.floor(e[0]/n)*n)),t.fixMax||(e[1]=Av(Math.ceil(e[1]/n)*n))},Lv.prototype.setNiceExtent=function(t,e){this._niceExtent=[t,e]},Lv.type="interval",Lv);function Lv(){var t=null!==Dv&&Dv.apply(this,arguments)||this;return t.type="interval",t._interval=0,t._intervalPrecision=2,t}pv.registerClass(Pv);var Ov="undefined"!=typeof Float32Array,Rv=Ov?Float32Array:Array;function Nv(t){return F(t)?Ov?new Float32Array(t):t:new Rv(t)}function zv(t){return t.get("stack")||"__ec_stack_"+t.seriesIndex}function Ev(t){return t.dim+t.index}function Bv(t,e){var n=[];return e.eachSeriesByType(t,function(t){var e;(e=t).coordinateSystem&&"cartesian2d"===e.coordinateSystem.type&&n.push(t)}),n}function Fv(t){var a,d,u=function(t){var e,l={},n=(O(t,function(t){var e=t.coordinateSystem.getBaseAxis();if("time"===e.type||"value"===e.type)for(var t=t.getData(),n=e.dim+"_"+e.index,i=t.getDimensionIndex(t.mapDimension(e.dim)),r=t.getStore(),o=0,a=r.count();o<a;++o){var s=r.get(i,o);l[n]?l[n].push(s):l[n]=[s]}}),{});for(e in l)if(l.hasOwnProperty(e)){var i=l[e];if(i){i.sort(function(t,e){return t-e});for(var r=null,o=1;o<i.length;++o){var a=i[o]-i[o-1];0<a&&(r=null===r?a:Math.min(r,a))}n[e]=r}}return n}(t),h=[];return O(t,function(t){var e,n,i=t.coordinateSystem.getBaseAxis(),r=i.getExtent(),o=(e="category"===i.type?i.getBandWidth():"value"===i.type||"time"===i.type?(e=i.dim+"_"+i.index,e=u[e],o=Math.abs(r[1]-r[0]),n=i.scale.getExtent(),n=Math.abs(n[1]-n[0]),e?o/n*e:o):(n=t.getData(),Math.abs(r[1]-r[0])/n.count()),Gr(t.get("barWidth"),e)),r=Gr(t.get("barMaxWidth"),e),a=Gr(t.get("barMinWidth")||((n=t).pipelineContext&&n.pipelineContext.large?.5:1),e),s=t.get("barGap"),l=t.get("barCategoryGap");h.push({bandWidth:e,barWidth:o,barMaxWidth:r,barMinWidth:a,barGap:s,barCategoryGap:l,axisKey:Ev(i),stackId:zv(t)})}),a={},O(h,function(t,e){var n=t.axisKey,i=t.bandWidth,i=a[n]||{bandWidth:i,remainedWidth:i,autoWidthCount:0,categoryGap:null,gap:"20%",stacks:{}},r=i.stacks,n=(a[n]=i,t.stackId),o=(r[n]||i.autoWidthCount++,r[n]=r[n]||{width:0,maxWidth:0},t.barWidth),o=(o&&!r[n].width&&(r[n].width=o,o=Math.min(i.remainedWidth,o),i.remainedWidth-=o),t.barMaxWidth),o=(o&&(r[n].maxWidth=o),t.barMinWidth),r=(o&&(r[n].minWidth=o),t.barGap),n=(null!=r&&(i.gap=r),t.barCategoryGap);null!=n&&(i.categoryGap=n)}),d={},O(a,function(t,n){d[n]={};var i,e=t.stacks,r=t.bandWidth,o=t.categoryGap,a=(null==o&&(a=ht(e).length,o=Math.max(35-4*a,15)+"%"),Gr(o,r)),s=Gr(t.gap,1),l=t.remainedWidth,u=t.autoWidthCount,h=(l-a)/(u+(u-1)*s),h=Math.max(h,0),c=(O(e,function(t){var e,n=t.maxWidth,i=t.minWidth;t.width?(e=t.width,n&&(e=Math.min(e,n)),i&&(e=Math.max(e,i)),t.width=e,l-=e+s*e,u--):(e=h,n&&n<e&&(e=Math.min(n,l)),(e=i&&e<i?i:e)!==h&&(t.width=e,l-=e+s*e,u--))}),h=(l-a)/(u+(u-1)*s),h=Math.max(h,0),0),p=(O(e,function(t,e){t.width||(t.width=h),c+=(i=t).width*(1+s)}),i&&(c-=i.width*s),-c/2);O(e,function(t,e){d[n][e]=d[n][e]||{bandWidth:r,offset:p,width:t.width},p+=t.width*(1+s)})}),d}u(Gv,Vv=Pv),Gv.prototype.getLabel=function(t){var e=this.getSetting("useUTC");return Gc(t.value,Ec[function(t){switch(t){case"year":case"month":return"day";case"millisecond":return"millisecond";default:return"second"}}(Hc(this._minLevelUnit))]||Ec.second,e,this.getSetting("locale"))},Gv.prototype.getFormattedLabel=function(t,e,n){var i=this.getSetting("useUTC"),r=this.getSetting("locale"),o=null;if(V(n))o=n;else if(S(n))o=n(t.value,e,{level:t.level});else{var a=L({},Nc);if(0<t.level)for(var s=0;s<Bc.length;++s)a[Bc[s]]="{primary|"+a[Bc[s]]+"}";var l=n?!1===n.inherit?n:E(n,a):a,u=Wc(t.value,i);if(l[u])o=l[u];else if(l.inherit){for(s=Fc.indexOf(u)-1;0<=s;--s)if(l[u]){o=l[u];break}o=o||a.none}F(o)&&(e=null==t.level?0:0<=t.level?t.level:o.length+t.level,o=o[e=Math.min(e,o.length-1)])}return Gc(new Date(t.value),o,i,r)},Gv.prototype.getTicks=function(){var t=this._interval,e=this._extent,n=[];return t&&(n.push({value:e[0],level:0}),t=this.getSetting("useUTC"),t=function(t,b,S,M){var e=Fc,n=0;function i(t,e,n){var i=[],r=!e.length;if(!function(t,e,n,i){function r(t){return Uc(c,t,i)===Uc(p,t,i)}function o(){return r("year")}function a(){return o()&&r("month")}function s(){return a()&&r("day")}function l(){return s()&&r("hour")}function u(){return l()&&r("minute")}function h(){return u()&&r("second")}var c=Kr(e),p=Kr(n);switch(t){case"year":return o();case"month":return a();case"day":return s();case"hour":return l();case"minute":return u();case"second":return h();case"millisecond":return h()&&r("millisecond")}}(Hc(t),M[0],M[1],S)){r&&(e=[{value:function(t,e,n){var i=new Date(t);switch(Hc(e)){case"year":case"month":i[Qc(n)](0);case"day":i[Jc(n)](1);case"hour":i[tp(n)](0);case"minute":i[ep(n)](0);case"second":i[np(n)](0),i[ip(n)](0)}return i.getTime()}(new Date(M[0]),t,S)},{value:M[1]}]);for(var o,a,s=0;s<e.length-1;s++){var l=e[s].value,u=e[s+1].value;if(l!==u){var h=void 0,c=void 0,p=void 0;switch(t){case"year":h=Math.max(1,Math.round(b/Rc/365)),c=Xc(S),p=S?"setUTCFullYear":"setFullYear";break;case"half-year":case"quarter":case"month":a=b,h=6<(a/=30*Rc)?6:3<a?3:2<a?2:1,c=Yc(S),p=Qc(S);break;case"week":case"half-week":case"day":a=b,h=16<(a/=Rc)?16:7.5<a?7:3.5<a?4:1.5<a?2:1,c=qc(S),p=Jc(S),0;break;case"half-day":case"quarter-day":case"hour":o=b,h=12<(o/=Oc)?12:6<o?6:3.5<o?4:2<o?2:1,c=Zc(S),p=tp(S);break;case"minute":h=Uv(b,!0),c=jc(S),p=ep(S);break;case"second":h=Uv(b,!1),c=Kc(S),p=np(S);break;case"millisecond":h=Jr(b,!0),c=$c(S),p=ip(S)}w=x=_=v=m=y=g=f=d=void 0;for(var d=h,f=l,g=u,y=c,m=p,v=i,_=new Date(f),x=f,w=_[y]();x<g&&x<=M[1];)v.push({value:x}),_[m](w+=d),x=_.getTime();v.push({value:x,notAdd:!0}),"year"===t&&1<n.length&&0===s&&n.unshift({value:n[0].value-h})}}for(s=0;s<i.length;s++)n.push(i[s])}}for(var r=[],o=[],a=0,s=0,l=0;l<e.length&&n++<1e4;++l){var u=Hc(e[l]);if(function(t){return t===Hc(t)}(e[l])){i(e[l],r[r.length-1]||[],o);var h=e[l+1]?Hc(e[l+1]):null;if(u!==h){if(o.length){s=a,o.sort(function(t,e){return t.value-e.value});for(var c=[],p=0;p<o.length;++p){var d=o[p].value;0!==p&&o[p-1].value===d||(c.push(o[p]),d>=M[0]&&d<=M[1]&&a++)}u=(M[1]-M[0])/b;if(1.5*u<a&&u/1.5<s)break;if(r.push(c),u<a||t===e[l])break}o=[]}}}for(var f=ut(B(r,function(t){return ut(t,function(t){return t.value>=M[0]&&t.value<=M[1]&&!t.notAdd})}),function(t){return 0<t.length}),g=[],y=f.length-1,l=0;l<f.length;++l)for(var m=f[l],v=0;v<m.length;++v)g.push({value:m[v].value,level:y-l});g.sort(function(t,e){return t.value-e.value});for(var _=[],l=0;l<g.length;++l)0!==l&&g[l].value===g[l-1].value||_.push(g[l]);return _}(this._minLevelUnit,this._approxInterval,t,e),(n=n.concat(t)).push({value:e[1],level:0})),n},Gv.prototype.calcNiceExtent=function(t){var e,n=this._extent;n[0]===n[1]&&(n[0]-=Rc,n[1]+=Rc),n[1]===-1/0&&n[0]===1/0&&(e=new Date,n[1]=+new Date(e.getFullYear(),e.getMonth(),e.getDate()),n[0]=n[1]-Rc),this.calcNiceTicks(t.splitNumber,t.minInterval,t.maxInterval)},Gv.prototype.calcNiceTicks=function(t,e,n){var i=this._extent,i=i[1]-i[0],i=(this._approxInterval=i/(t=t||10),null!=e&&this._approxInterval<e&&(this._approxInterval=e),null!=n&&this._approxInterval>n&&(this._approxInterval=n),Wv.length),t=Math.min(function(t,e,n,i){for(;n<i;){var r=n+i>>>1;t[r][1]<e?n=1+r:i=r}return n}(Wv,this._approxInterval,0,i),i-1);this._interval=Wv[t][1],this._minLevelUnit=Wv[Math.max(t-1,0)][0]},Gv.prototype.parse=function(t){return ft(t)?t:+Kr(t)},Gv.prototype.contain=function(t){return Sv(this.parse(t),this._extent)},Gv.prototype.normalize=function(t){return Mv(this.parse(t),this._extent)},Gv.prototype.scale=function(t){return Tv(t,this._extent)},Gv.type="time";var Vv,Hv=Gv;function Gv(t){t=Vv.call(this,t)||this;return t.type="time",t}var Wv=[["second",Pc],["minute",Lc],["hour",Oc],["quarter-day",6*Oc],["half-day",12*Oc],["day",1.2*Rc],["half-week",3.5*Rc],["week",7*Rc],["month",31*Rc],["quarter",95*Rc],["half-year",Oo/2],["year",Oo]];function Uv(t,e){return 30<(t/=e?Lc:Pc)?30:20<t?20:15<t?15:10<t?10:5<t?5:2<t?2:1}pv.registerClass(Hv);var Xv,Yv=pv.prototype,qv=Pv.prototype,Zv=Wr,jv=Math.floor,Kv=Math.ceil,$v=Math.pow,Qv=Math.log,Jv=(u(t_,Xv=pv),t_.prototype.getTicks=function(t){var e=this._originalScale,n=this._extent,i=e.getExtent();return B(qv.getTicks.call(this,t),function(t){var t=t.value,e=Wr($v(this.base,t)),e=t===n[0]&&this._fixMin?e_(e,i[0]):e;return{value:t===n[1]&&this._fixMax?e_(e,i[1]):e}},this)},t_.prototype.setExtent=function(t,e){var n=this.base;t=Qv(t)/Qv(n),e=Qv(e)/Qv(n),qv.setExtent.call(this,t,e)},t_.prototype.getExtent=function(){var t=this.base,e=Yv.getExtent.call(this);e[0]=$v(t,e[0]),e[1]=$v(t,e[1]);t=this._originalScale.getExtent();return this._fixMin&&(e[0]=e_(e[0],t[0])),this._fixMax&&(e[1]=e_(e[1],t[1])),e},t_.prototype.unionExtent=function(t){this._originalScale.unionExtent(t);var e=this.base;t[0]=Qv(t[0])/Qv(e),t[1]=Qv(t[1])/Qv(e),Yv.unionExtent.call(this,t)},t_.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},t_.prototype.calcNiceTicks=function(t){t=t||10;var e=this._extent,n=e[1]-e[0];if(!(n==1/0||n<=0)){var i=$r(n);for(t/n*i<=.5&&(i*=10);!isNaN(i)&&Math.abs(i)<1&&0<Math.abs(i);)i*=10;t=[Wr(Kv(e[0]/i)*i),Wr(jv(e[1]/i)*i)];this._interval=i,this._niceExtent=t}},t_.prototype.calcNiceExtent=function(t){qv.calcNiceExtent.call(this,t),this._fixMin=t.fixMin,this._fixMax=t.fixMax},t_.prototype.parse=function(t){return t},t_.prototype.contain=function(t){return Sv(t=Qv(t)/Qv(this.base),this._extent)},t_.prototype.normalize=function(t){return Mv(t=Qv(t)/Qv(this.base),this._extent)},t_.prototype.scale=function(t){return t=Tv(t,this._extent),$v(this.base,t)},t_.type="log",t_);function t_(){var t=null!==Xv&&Xv.apply(this,arguments)||this;return t.type="log",t.base=10,t._originalScale=new Pv,t._interval=0,t}Iy=Jv.prototype;function e_(t,e){return Zv(t,Ur(e))}Iy.getMinorTicks=qv.getMinorTicks,Iy.getLabel=qv.getLabel,pv.registerClass(Jv);i_.prototype._prepareParams=function(t,e,n){n[1]<n[0]&&(n=[NaN,NaN]),this._dataMin=n[0],this._dataMax=n[1];var i=this._isOrdinal="ordinal"===t.type,r=(this._needCrossZero="interval"===t.type&&e.getNeedCrossZero&&e.getNeedCrossZero(),this._modelMinRaw=e.get("min",!0)),r=(S(r)?this._modelMinNum=a_(t,r({min:n[0],max:n[1]})):"dataMin"!==r&&(this._modelMinNum=a_(t,r)),this._modelMaxRaw=e.get("max",!0));S(r)?this._modelMaxNum=a_(t,r({min:n[0],max:n[1]})):"dataMax"!==r&&(this._modelMaxNum=a_(t,r)),i?this._axisDataLen=e.getCategories().length:"boolean"==typeof(t=F(n=e.get("boundaryGap"))?n:[n||0,n||0])[0]||"boolean"==typeof t[1]?this._boundaryGapInner=[0,0]:this._boundaryGapInner=[mr(t[0],1),mr(t[1],1)]},i_.prototype.calculate=function(){var t=this._isOrdinal,e=this._dataMin,n=this._dataMax,i=this._axisDataLen,r=this._boundaryGapInner,o=t?null:n-e||Math.abs(e),a="dataMin"===this._modelMinRaw?e:this._modelMinNum,s="dataMax"===this._modelMaxRaw?n:this._modelMaxNum,l=null!=a,u=null!=s,e=(null==a&&(a=t?i?0:NaN:e-r[0]*o),null==s&&(s=t?i?i-1:NaN:n+r[1]*o),null!=a&&isFinite(a)||(a=NaN),null!=s&&isFinite(s)||(s=NaN),wt(a)||wt(s)||t&&!i),n=(this._needCrossZero&&(a=0<a&&0<s&&!l?0:a)<0&&s<0&&!u&&(s=0),this._determinedMin),r=this._determinedMax;return null!=n&&(a=n,l=!0),null!=r&&(s=r,u=!0),{min:a,max:s,minFixed:l,maxFixed:u,isBlank:e}},i_.prototype.modifyDataMinMax=function(t,e){this[o_[t]]=e},i_.prototype.setDeterminedMinMax=function(t,e){this[r_[t]]=e},i_.prototype.freeze=function(){this.frozen=!0};var n_=i_;function i_(t,e,n){this._prepareParams(t,e,n)}var r_={min:"_determinedMin",max:"_determinedMax"},o_={min:"_dataMin",max:"_dataMax"};function a_(t,e){return null==e?null:wt(e)?NaN:t.parse(e)}function s_(t,e){var n,i,r,o,a,s=t.type,l=(l=e,u=(h=t).getExtent(),(c=h.rawExtentInfo)||(c=new n_(h,l,u),h.rawExtentInfo=c),c.calculate()),u=(t.setBlank(l.isBlank),l.min),h=l.max,c=e.ecModel;return c&&"time"===s&&(t=Bv("bar",c),n=!1,O(t,function(t){n=n||t.getBaseAxis()===e.axis}),n)&&(s=Fv(t),c=u,t=h,s=s,a=(a=(i=e).axis.getExtent())[1]-a[0],void 0!==(s=function(t,e,n){if(t&&e)return null!=(t=t[Ev(e)])&&null!=n?t[zv(n)]:t}(s,i.axis))&&(r=1/0,O(s,function(t){r=Math.min(t.offset,r)}),o=-1/0,O(s,function(t){o=Math.max(t.offset+t.width,o)}),r=Math.abs(r),o=Math.abs(o),t+=o/(i=r+o)*(a=(s=t-c)/(1-(r+o)/a)-s),c-=r/i*a),u=(s={min:c,max:t}).min,h=s.max),{extent:[u,h],fixMin:l.minFixed,fixMax:l.maxFixed}}function l_(t,e){var n=s_(t,e),i=n.extent,r=e.get("splitNumber"),o=(t instanceof Jv&&(t.base=e.get("logBase")),t.type),a=e.get("interval"),o="interval"===o||"time"===o;t.setExtent(i[0],i[1]),t.calcNiceExtent({splitNumber:r,fixMin:n.fixMin,fixMax:n.fixMax,minInterval:o?e.get("minInterval"):null,maxInterval:o?e.get("maxInterval"):null}),null!=a&&t.setInterval&&t.setInterval(a)}function u_(t,e){if(e=e||t.get("type"))switch(e){case"category":return new Iv({ordinalMeta:t.getOrdinalMeta?t.getOrdinalMeta():t.getCategories(),extent:[1/0,-1/0]});case"time":return new Hv({locale:t.ecModel.getLocaleModel(),useUTC:t.ecModel.get("useUTC")});default:return new(pv.getClass(e)||Pv)}}function h_(n){var i,e,r,t=n.getLabelModel().get("formatter"),o="category"===n.type?n.scale.getExtent()[0]:null;return"time"===n.scale.type?(r=t,function(t,e){return n.scale.getFormattedLabel(t,e,r)}):V(t)?(e=t,function(t){t=n.scale.getLabel(t);return e.replace("{value}",null!=t?t:"")}):S(t)?(i=t,function(t,e){return null!=o&&(e=t.value-o),i(c_(n,t),e,null!=t.level?{level:t.level}:null)}):function(t){return n.scale.getLabel(t)}}function c_(t,e){return"category"===t.type?t.scale.getLabel(e):e.value}function p_(t){var e=t.model,n=t.scale;if(e.get(["axisLabel","show"])&&!n.isBlank()){var i,r,o=n.getExtent(),a=n instanceof Iv?n.count():(i=n.getTicks()).length,s=t.getLabelModel(),l=h_(t),u=1;40<a&&(u=Math.ceil(a/40));for(var h,c,p,d=0;d<a;d+=u){var f=l(i?i[d]:{value:o[0]+d},d),f=s.getTextRect(f),g=(f=f,h=s.get("rotate")||0,c=p=g=c=void 0,h=h*Math.PI/180,c=f.width,g=f.height,p=c*Math.abs(Math.cos(h))+Math.abs(g*Math.sin(h)),c=c*Math.abs(Math.sin(h))+Math.abs(g*Math.cos(h)),new X(f.x,f.y,p,c));r?r.union(g):r=g}return r}}function d_(t){t=t.get("interval");return null==t?"auto":t}function f_(t){return"category"===t.type&&0===d_(t.getLabelModel())}y_.prototype.getNeedCrossZero=function(){return!this.option.scale},y_.prototype.getCoordSysModel=function(){};var g_=y_;function y_(){}var Py=Object.freeze({__proto__:null,createDimensions:function(t,e){return rv(t,e).dimensions},createList:function(t){return cv(null,t)},createScale:function(t,e){var n=e;return(e=u_(n=e instanceof xc?n:new xc(e))).setExtent(t[0],t[1]),l_(e,n),e},createSymbol:Ey,createTextStyle:function(t,e){return Jh(t,null,null,"normal"!==(e=e||{}).state)},dataStack:{isDimensionStacked:uv,enableDataStack:lv,getStackedDimension:hv},enableHoverEmphasis:Sl,getECData:k,getLayoutRect:wp,mixinAxisModelCommonMethods:function(t){at(t,g_)}}),m_=[],v_={registerPreprocessor:g0,registerProcessor:y0,registerPostInit:m0,registerPostUpdate:v0,registerUpdateLifecycle:_0,registerAction:x0,registerCoordinateSystem:w0,registerLayout:b0,registerVisual:S0,registerTransform:k0,registerLoading:C0,registerMap:I0,registerImpl:function(t,e){gm[t]=e},PRIORITY:Ay,ComponentModel:g,ComponentView:Pg,SeriesModel:Sg,ChartView:zg,registerComponentModel:function(t){g.registerClass(t)},registerComponentView:function(t){Pg.registerClass(t)},registerSeriesModel:function(t){Sg.registerClass(t)},registerChartView:function(t){zg.registerClass(t)},registerSubTypeDefaulter:function(t,e){g.registerSubTypeDefaulter(t,e)},registerPainter:function(t,e){Br(t,e)}};function __(t){F(t)?O(t,function(t){__(t)}):0<=I(m_,t)||(m_.push(t),(t=S(t)?{install:t}:t).install(v_))}var x_=1e-8;function w_(t,e){return Math.abs(t-e)<x_}function b_(t,e,n){var i=0,r=t[0];if(r){for(var o=1;o<t.length;o++){var a=t[o];i+=Ga(r[0],r[1],a[0],a[1],e,n),r=a}var s=t[0];return w_(r[0],s[0])&&w_(r[1],s[1])||(i+=Ga(r[0],r[1],s[0],s[1],e,n)),0!==i}}var S_=[];function M_(t,e){for(var n=0;n<t.length;n++)Jt(t[n],t[n],e)}function T_(t,e,n,i){for(var r=0;r<t.length;r++){var o=t[r];(o=i?i.project(o):o)&&isFinite(o[0])&&isFinite(o[1])&&(te(e,e,o),ee(n,n,o))}}C_.prototype.setCenter=function(t){this._center=t},C_.prototype.getCenter=function(){return this._center||(this._center=this.calcCenter())};Ly=C_;function C_(t){this.name=t}var I_,k_,D_=function(t,e){this.type="polygon",this.exterior=t,this.interiors=e},A_=function(t){this.type="linestring",this.points=t},P_=(u(L_,I_=Ly),L_.prototype.calcCenter=function(){for(var t,e=this.geometries,n=0,i=0;i<e.length;i++){var r=e[i],o=r.exterior,o=o&&o.length;n<o&&(t=r,n=o)}if(t){for(var a=t.exterior,s=0,l=0,u=0,h=a.length,c=a[h-1][0],p=a[h-1][1],d=0;d<h;d++){var f=a[d][0],g=a[d][1],y=c*g-f*p;s+=y,l+=(c+f)*y,u+=(p+g)*y,c=f,p=g}return s?[l/s/3,u/s/3,s]:[a[0][0]||0,a[0][1]||0]}var m=this.getBoundingRect();return[m.x+m.width/2,m.y+m.height/2]},L_.prototype.getBoundingRect=function(e){var n,i,t=this._rect;return t&&!e||(n=[1/0,1/0],i=[-1/0,-1/0],O(this.geometries,function(t){"polygon"===t.type?T_(t.exterior,n,i,e):O(t.points,function(t){T_(t,n,i,e)})}),isFinite(n[0])&&isFinite(n[1])&&isFinite(i[0])&&isFinite(i[1])||(n[0]=n[1]=i[0]=i[1]=0),t=new X(n[0],n[1],i[0]-n[0],i[1]-n[1]),e)||(this._rect=t),t},L_.prototype.contain=function(t){var e=this.getBoundingRect(),n=this.geometries;if(e.contain(t[0],t[1]))t:for(var i=0,r=n.length;i<r;i++){var o=n[i];if("polygon"===o.type){var a=o.exterior,s=o.interiors;if(b_(a,t[0],t[1])){for(var l=0;l<(s?s.length:0);l++)if(b_(s[l],t[0],t[1]))continue t;return!0}}}return!1},L_.prototype.transformTo=function(t,e,n,i){for(var r=this.getBoundingRect(),o=r.width/r.height,o=(n?i=i||n/o:n=o*i,new X(t,e,n,i)),a=r.calculateTransform(o),s=this.geometries,l=0;l<s.length;l++){var u=s[l];"polygon"===u.type?(M_(u.exterior,a),O(u.interiors,function(t){M_(t,a)})):O(u.points,function(t){M_(t,a)})}(r=this._rect).copy(o),this._center=[r.x+r.width/2,r.y+r.height/2]},L_.prototype.cloneShallow=function(t){t=new L_(t=null==t?this.name:t,this.geometries,this._center);return t._rect=this._rect,t.transformTo=null,t},L_);function L_(t,e,n){t=I_.call(this,t)||this;return t.type="geoJSON",t.geometries=e,t._center=n&&[n[0],n[1]],t}function O_(t,e){t=k_.call(this,t)||this;return t.type="geoSVG",t._elOnlyForCalculate=e,t}function R_(t,e,n){for(var i=0;i<t.length;i++)t[i]=N_(t[i],e[i],n)}function N_(t,e,n){for(var i=[],r=e[0],o=e[1],a=0;a<t.length;a+=2){var s=(s=t.charCodeAt(a)-64)>>1^-(1&s),l=(l=t.charCodeAt(a+1)-64)>>1^-(1&l);i.push([(r=s+=r)/n,(o=l+=o)/n])}return i}function z_(t,o){var e,n,r;return B(ut((t=(e=t).UTF8Encoding?(null==(r=(n=e).UTF8Scale)&&(r=1024),O(n.features,function(t){var e=t.geometry,n=e.encodeOffsets,i=e.coordinates;if(n)switch(e.type){case"LineString":e.coordinates=N_(i,n,r);break;case"Polygon":case"MultiLineString":R_(i,n,r);break;case"MultiPolygon":O(i,function(t,e){return R_(t,n[e],r)})}}),n.UTF8Encoding=!1,n):e).features,function(t){return t.geometry&&t.properties&&0<t.geometry.coordinates.length}),function(t){var e=t.properties,n=t.geometry,i=[];switch(n.type){case"Polygon":var r=n.coordinates;i.push(new D_(r[0],r.slice(1)));break;case"MultiPolygon":O(n.coordinates,function(t){t[0]&&i.push(new D_(t[0],t.slice(1)))});break;case"LineString":i.push(new A_([n.coordinates]));break;case"MultiLineString":i.push(new A_(n.coordinates))}t=new P_(e[o||"name"],i,e.cp);return t.properties=e,t})}u(O_,k_=Ly),O_.prototype.calcCenter=function(){for(var t=this._elOnlyForCalculate,e=t.getBoundingRect(),e=[e.x+e.width/2,e.y+e.height/2],n=Fi(S_),i=t;i&&!i.isGeoSVGGraphicRoot;)Hi(n,i.getLocalTransform(),n),i=i.parent;return Xi(n,n),Jt(e,e,n),e};var Fo=Object.freeze({__proto__:null,MAX_SAFE_INTEGER:9007199254740991,asc:function(t){return t.sort(function(t,e){return t-e}),t},getPercentWithPrecision:function(t,e,n){if(!t[e])return 0;var i=lt(t,function(t,e){return t+(isNaN(e)?0:e)},0);if(0===i)return 0;for(var r=Math.pow(10,n),n=B(t,function(t){return(isNaN(t)?0:t)/i*r*100}),o=100*r,a=B(n,function(t){return Math.floor(t)}),s=lt(a,function(t,e){return t+e},0),l=B(n,function(t,e){return t-a[e]});s<o;){for(var u=Number.NEGATIVE_INFINITY,h=null,c=0,p=l.length;c<p;++c)l[c]>u&&(u=l[c],h=c);++a[h],l[h]=0,++s}return a[e]/r},getPixelPrecision:Yr,getPrecision:Ur,getPrecisionSafe:Xr,isNumeric:eo,isRadianAroundZero:Zr,linearMap:Hr,nice:Jr,numericToNumber:to,parseDate:Kr,quantile:function(t,e){var e=(t.length-1)*e+1,n=Math.floor(e),i=+t[n-1];return(e=e-n)?i+e*(t[n]-i):i},quantity:$r,quantityExponent:Qr,reformIntervals:function(t){t.sort(function(t,e){return function t(e,n,i){return e.interval[i]<n.interval[i]||e.interval[i]===n.interval[i]&&(e.close[i]-n.close[i]==(i?-1:1)||!i&&t(e,n,1))}(t,e,0)?-1:1});for(var e=-1/0,n=1,i=0;i<t.length;){for(var r=t[i].interval,o=t[i].close,a=0;a<2;a++)r[a]<=e&&(r[a]=e,o[a]=a?1:1-n),e=r[a],n=o[a];r[0]===r[1]&&o[0]*o[1]!=1?t.splice(i,1):i++}return t},remRadian:qr,round:Wr}),Nh=Object.freeze({__proto__:null,format:Gc,parse:Kr}),zc=Object.freeze({__proto__:null,Arc:Zu,BezierCurve:Uu,BoundingRect:X,Circle:Jl,CompoundPath:$u,Ellipse:iu,Group:Pr,Image:ls,IncrementalDisplayable:i,Line:Bu,LinearGradient:nh,Polygon:Du,Polyline:Ou,RadialGradient:Ju,Rect:ms,Ring:Mu,Sector:xu,Text:bs,clipPointsByRect:Vh,clipRectByRect:Hh,createIcon:Gh,extendPath:Ih,extendShape:Th,getShapeClass:Dh,getTransform:zh,initProps:vh,makeImage:Ph,makePath:Ah,mergePath:Oh,registerShape:kh,resizePath:Rh,updateProps:mh}),gc=Object.freeze({__proto__:null,addCommas:rp,capitalFirst:function(t){return t&&t.charAt(0).toUpperCase()+t.substr(1)},encodeHTML:up,formatTime:function(t,e,n){"week"!==t&&"month"!==t&&"quarter"!==t&&"half-year"!==t&&"year"!==t||(t="MM-dd\nyyyy");var i=(e=Kr(e))[(n=n?"getUTC":"get")+"FullYear"](),r=e[n+"Month"]()+1,o=e[n+"Date"](),a=e[n+"Hours"](),s=e[n+"Minutes"](),l=e[n+"Seconds"](),e=e[n+"Milliseconds"]();return t=t.replace("MM",Vc(r,2)).replace("M",r).replace("yyyy",i).replace("yy",i%100+"").replace("dd",Vc(o,2)).replace("d",o).replace("hh",Vc(a,2)).replace("h",a).replace("mm",Vc(s,2)).replace("m",s).replace("ss",Vc(l,2)).replace("s",l).replace("SSS",Vc(e,3))},formatTpl:dp,getTextRect:function(t,e,n,i,r,o,a,s){return new bs({style:{text:t,font:e,align:n,verticalAlign:i,padding:r,rich:o,overflow:a?"truncate":null,lineHeight:s}}).getBoundingRect()},getTooltipMarker:fp,normalizeCssArray:ap,toCamelCase:op,truncateText:Yo}),cc=Object.freeze({__proto__:null,bind:ct,clone:y,curry:pt,defaults:E,each:O,extend:L,filter:ut,indexOf:I,inherits:ot,isArray:F,isFunction:S,isObject:R,isString:V,map:B,merge:d,reduce:lt}),E_=_o();function B_(t){return"category"===t.type?(r=(e=t).getLabelModel(),o=V_(e,r),!r.get("show")||e.scale.isBlank()?{labels:[],labelCategoryInterval:o.labelCategoryInterval}:o):(r=(n=t).scale.getTicks(),i=h_(n),{labels:B(r,function(t,e){return{level:t.level,formattedLabel:i(t,e),rawLabel:n.scale.getLabel(t),tickValue:t.value}})});var n,i,e,r,o}function F_(t,e){var n,i,r,o,a,s;return"category"===t.type?(e=e,o=H_(n=t,"ticks"),a=d_(e),(s=G_(o,a))||(e.get("show")&&!n.scale.isBlank()||(i=[]),i=S(a)?X_(n,a,!0):"auto"===a?(s=V_(n,n.getLabelModel()),r=s.labelCategoryInterval,B(s.labels,function(t){return t.tickValue})):U_(n,r=a,!0),W_(o,a,{ticks:i,tickCategoryInterval:r}))):{ticks:B(t.scale.getTicks(),function(t){return t.value})}}function V_(t,e){var n,i=H_(t,"labels"),e=d_(e),r=G_(i,e);return r||W_(i,e,{labels:S(e)?X_(t,e):U_(t,n="auto"===e?null!=(i=E_(r=t).autoInterval)?i:E_(r).autoInterval=r.calculateCategoryInterval():e),labelCategoryInterval:n})}function H_(t,e){return E_(t)[e]||(E_(t)[e]=[])}function G_(t,e){for(var n=0;n<t.length;n++)if(t[n].key===e)return t[n].value}function W_(t,e,n){return t.push({key:e,value:n}),n}function U_(t,e,n){for(var i=h_(t),r=t.scale,o=r.getExtent(),a=t.getLabelModel(),s=[],l=Math.max((e||0)+1,1),e=o[0],u=r.count(),u=(0!==e&&1<l&&2<u/l&&(e=Math.round(Math.ceil(e/l)*l)),f_(t)),t=a.get("showMinLabel")||u,a=a.get("showMaxLabel")||u,h=(t&&e!==o[0]&&c(o[0]),e);h<=o[1];h+=l)c(h);function c(t){var e={value:t};s.push(n?t:{formattedLabel:i(e),rawLabel:r.getLabel(e),tickValue:t})}return a&&h-l!==o[1]&&c(o[1]),s}function X_(t,i,r){var o=t.scale,a=h_(t),s=[];return O(o.getTicks(),function(t){var e=o.getLabel(t),n=t.value;i(t.value,e)&&s.push(r?n:{formattedLabel:a(t),rawLabel:e,tickValue:n})}),s}var Y_=[0,1],mc=(q_.prototype.contain=function(t){var e=this._extent,n=Math.min(e[0],e[1]),e=Math.max(e[0],e[1]);return n<=t&&t<=e},q_.prototype.containData=function(t){return this.scale.contain(t)},q_.prototype.getExtent=function(){return this._extent.slice()},q_.prototype.getPixelPrecision=function(t){return Yr(t||this.scale.getExtent(),this._extent)},q_.prototype.setExtent=function(t,e){var n=this._extent;n[0]=t,n[1]=e},q_.prototype.dataToCoord=function(t,e){var n=this._extent,i=this.scale;return t=i.normalize(t),this.onBand&&"ordinal"===i.type&&Z_(n=n.slice(),i.count()),Hr(t,Y_,n,e)},q_.prototype.coordToData=function(t,e){var n=this._extent,i=this.scale,i=(this.onBand&&"ordinal"===i.type&&Z_(n=n.slice(),i.count()),Hr(t,n,Y_,e));return this.scale.scale(i)},q_.prototype.pointToData=function(t,e){},q_.prototype.getTicksCoords=function(t){var e,n,i,r,o,a,s,l=(t=t||{}).tickModel||this.getTickModel(),u=B(F_(this,l).ticks,function(t){return{coord:this.dataToCoord("ordinal"===this.scale.type?this.scale.getRawOrdinalNumber(t):t),tickValue:t}},this),l=l.get("alignWithLabel");function h(t,e){return t=Wr(t),e=Wr(e),a?e<t:t<e}return e=this,n=u,l=l,t=t.clamp,s=n.length,e.onBand&&!l&&s&&(l=e.getExtent(),1===s?(n[0].coord=l[0],i=n[1]={coord:l[0]}):(o=n[s-1].tickValue-n[0].tickValue,r=(n[s-1].coord-n[0].coord)/o,O(n,function(t){t.coord-=r/2}),e=1+(o=e.scale.getExtent())[1]-n[s-1].tickValue,i={coord:n[s-1].coord+r*e},n.push(i)),a=l[0]>l[1],h(n[0].coord,l[0])&&(t?n[0].coord=l[0]:n.shift()),t&&h(l[0],n[0].coord)&&n.unshift({coord:l[0]}),h(l[1],i.coord)&&(t?i.coord=l[1]:n.pop()),t)&&h(i.coord,l[1])&&n.push({coord:l[1]}),u},q_.prototype.getMinorTicksCoords=function(){var t;return"ordinal"===this.scale.type?[]:(t=this.model.getModel("minorTick").get("splitNumber"),B(this.scale.getMinorTicks(t=0<t&&t<100?t:5),function(t){return B(t,function(t){return{coord:this.dataToCoord(t),tickValue:t}},this)},this))},q_.prototype.getViewLabels=function(){return B_(this).labels},q_.prototype.getLabelModel=function(){return this.model.getModel("axisLabel")},q_.prototype.getTickModel=function(){return this.model.getModel("axisTick")},q_.prototype.getBandWidth=function(){var t=this._extent,e=this.scale.getExtent(),e=e[1]-e[0]+(this.onBand?1:0),t=(0===e&&(e=1),Math.abs(t[1]-t[0]));return Math.abs(t)/e},q_.prototype.calculateCategoryInterval=function(){r=(n=d=this).getLabelModel();var t={axisRotate:n.getRotate?n.getRotate():n.isHorizontal&&!n.isHorizontal()?90:0,labelRotate:r.get("rotate")||0,font:r.getFont()},e=h_(d),n=(t.axisRotate-t.labelRotate)/180*Math.PI,i=(r=d.scale).getExtent(),r=r.count();if(i[1]-i[0]<1)return 0;for(var o=1,a=(40<r&&(o=Math.max(1,Math.floor(r/40))),i[0]),s=d.dataToCoord(a+1)-d.dataToCoord(a),l=Math.abs(s*Math.cos(n)),s=Math.abs(s*Math.sin(n)),u=0,h=0;a<=i[1];a+=o)var c=1.3*(p=dr(e({value:a}),t.font,"center","top")).width,p=1.3*p.height,u=Math.max(u,c,7),h=Math.max(h,p,7);var n=u/l,l=h/s,s=(isNaN(n)&&(n=1/0),isNaN(l)&&(l=1/0),Math.max(0,Math.floor(Math.min(n,l)))),n=E_(d.model),l=d.getExtent(),d=n.lastAutoInterval,f=n.lastTickCount;return null!=d&&null!=f&&Math.abs(d-s)<=1&&Math.abs(f-r)<=1&&s<d&&n.axisExtent0===l[0]&&n.axisExtent1===l[1]?s=d:(n.lastTickCount=r,n.lastAutoInterval=s,n.axisExtent0=l[0],n.axisExtent1=l[1]),s},q_);function q_(t,e,n){this.onBand=!1,this.inverse=!1,this.dim=t,this.scale=e,this._extent=n||[0,0]}function Z_(t,e){e=(t[1]-t[0])/e/2;t[0]+=e,t[1]-=e}var j_=2*Math.PI,K_=Ea.CMD,$_=["top","right","bottom","left"];function Q_(t,e,n,i,r,o,a,s){var l=r-t,u=o-e,n=n-t,i=i-e,h=Math.sqrt(n*n+i*i),l=(l*(n/=h)+u*(i/=h))/h,u=(s&&(l=Math.min(Math.max(l,0),1)),a[0]=t+(l*=h)*n),s=a[1]=e+l*i;return Math.sqrt((u-r)*(u-r)+(s-o)*(s-o))}function J_(t,e,n,i,r,o,a){n<0&&(t+=n,n=-n),i<0&&(e+=i,i=-i);n=t+n,i=e+i,t=a[0]=Math.min(Math.max(r,t),n),n=a[1]=Math.min(Math.max(o,e),i);return Math.sqrt((t-r)*(t-r)+(n-o)*(n-o))}var t1=[];function e1(t,e,n){for(var i,r,o,a,s,l,u,h,c,p=0,d=0,f=0,g=0,y=1/0,m=e.data,v=t.x,_=t.y,x=0;x<m.length;){var w=m[x++],b=(1===x&&(f=p=m[x],g=d=m[x+1]),y);switch(w){case K_.M:p=f=m[x++],d=g=m[x++];break;case K_.L:b=Q_(p,d,m[x],m[x+1],v,_,t1,!0),p=m[x++],d=m[x++];break;case K_.C:b=yn(p,d,m[x++],m[x++],m[x++],m[x++],m[x],m[x+1],v,_,t1),p=m[x++],d=m[x++];break;case K_.Q:b=wn(p,d,m[x++],m[x++],m[x],m[x+1],v,_,t1),p=m[x++],d=m[x++];break;case K_.A:var S=m[x++],M=m[x++],T=m[x++],C=m[x++],I=m[x++],k=m[x++],D=(x+=1,!!(1-m[x++])),A=Math.cos(I)*T+S,P=Math.sin(I)*C+M;x<=1&&(f=A,g=P),P=(A=I)+k,D=D,a=(v-S)*(o=C)/T+S,s=_,l=t1,c=h=u=void 0,a-=i=S,s-=r=M,u=Math.sqrt(a*a+s*s),h=(a/=u)*o+i,c=(s/=u)*o+r,b=Math.abs(A-P)%j_<1e-4||((P=D?(D=A,A=Va(P),Va(D)):(A=Va(A),Va(P)))<A&&(P+=j_),(D=Math.atan2(s,a))<0&&(D+=j_),A<=D&&D<=P)||A<=D+j_&&D+j_<=P?(l[0]=h,l[1]=c,u-o):(c=((D=o*Math.cos(A)+i)-a)*(D-a)+((h=o*Math.sin(A)+r)-s)*(h-s))<(i=((u=o*Math.cos(P)+i)-a)*(u-a)+((A=o*Math.sin(P)+r)-s)*(A-s))?(l[0]=D,l[1]=h,Math.sqrt(c)):(l[0]=u,l[1]=A,Math.sqrt(i)),p=Math.cos(I+k)*T+S,d=Math.sin(I+k)*C+M;break;case K_.R:b=J_(f=p=m[x++],g=d=m[x++],m[x++],m[x++],v,_,t1);break;case K_.Z:b=Q_(p,d,f,g,v,_,t1,!0),p=f,d=g}b<y&&(y=b,n.set(t1[0],t1[1]))}return y}var n1=new M,i1=new M,r1=new M,o1=new M,a1=new M;function s1(t,e){if(t){var n=t.getTextGuideLine(),i=t.getTextContent();if(i&&n){var r=t.textGuideLineConfig||{},o=[[0,0],[0,0],[0,0]],a=r.candidates||$_,s=i.getBoundingRect().clone(),l=(s.applyTransform(i.getComputedTransform()),1/0),u=r.anchor,h=t.getComputedTransform(),c=h&&Xi([],h),p=e.get("length2")||0;u&&r1.copy(u);for(var d,f,g=0;g<a.length;g++){var y=a[g],m=(S=b=w=x=_=v=m=void 0,y),v=0,_=s,x=n1,w=o1,b=_.width,S=_.height;switch(m){case"top":x.set(_.x+b/2,_.y-v),w.set(0,-1);break;case"bottom":x.set(_.x+b/2,_.y+S+v),w.set(0,1);break;case"left":x.set(_.x-v,_.y+S/2),w.set(-1,0);break;case"right":x.set(_.x+b+v,_.y+S/2),w.set(1,0)}M.scaleAndAdd(i1,n1,o1,p),i1.transform(c);y=t.getBoundingRect(),y=u?u.distance(i1):t instanceof j?e1(i1,t.path,r1):(m=r1,d=J_((d=y).x,y.y,y.width,y.height,i1.x,i1.y,t1),m.set(t1[0],t1[1]),d);y<l&&(l=y,i1.transform(h),r1.transform(h),r1.toArray(o[0]),i1.toArray(o[1]),n1.toArray(o[2]))}i=o,(r=e.get("minTurnAngle"))<=180&&0<r&&(r=r/180*Math.PI,n1.fromArray(i[0]),i1.fromArray(i[1]),r1.fromArray(i[2]),M.sub(o1,n1,i1),M.sub(a1,r1,i1),e=o1.len(),f=a1.len(),e<.001||f<.001||(o1.scale(1/e),a1.scale(1/f),e=o1.dot(a1),Math.cos(r)<e&&(f=Q_(i1.x,i1.y,r1.x,r1.y,n1.x,n1.y,l1,!1),u1.fromArray(l1),u1.scaleAndAdd(a1,f/Math.tan(Math.PI-r)),e=r1.x!==i1.x?(u1.x-i1.x)/(r1.x-i1.x):(u1.y-i1.y)/(r1.y-i1.y),isNaN(e)||(e<0?M.copy(u1,i1):1<e&&M.copy(u1,r1),u1.toArray(i[1]))))),n.setShape({points:o})}}}var l1=[],u1=new M;function h1(t,e,n,i){var r="normal"===n,n=r?t:t.ensureState(n),e=(n.ignore=e,i.get("smooth")),e=(e&&!0===e&&(e=.3),n.shape=n.shape||{},0<e&&(n.shape.smooth=e),i.getModel("lineStyle").getLineStyle());r?t.useStyle(e):n.style=e}function c1(t,e){var n=e.smooth,i=e.points;if(i)if(t.moveTo(i[0][0],i[0][1]),0<n&&3<=i.length){var e=jt(i[0],i[1]),r=jt(i[1],i[2]);e&&r?(n=Math.min(e,r)*n,e=Qt([],i[1],i[0],n/e),n=Qt([],i[1],i[2],n/r),r=Qt([],e,n,.5),t.bezierCurveTo(e[0],e[1],e[0],e[1],r[0],r[1]),t.bezierCurveTo(n[0],n[1],n[0],n[1],i[2][0],i[2][1])):(t.lineTo(i[1][0],i[1][1]),t.lineTo(i[2][0],i[2][1]))}else for(var o=1;o<i.length;o++)t.lineTo(i[o][0],i[o][1])}function p1(t){for(var e=[],n=0;n<t.length;n++){var i,r,o,a,s,l,u=t[n];u.defaultAttr.ignore||(r=(i=u.label).getComputedTransform(),o=i.getBoundingRect(),a=!r||r[1]<1e-5&&r[2]<1e-5,l=i.style.margin||0,(s=o.clone()).applyTransform(r),s.x-=l/2,s.y-=l/2,s.width+=l,s.height+=l,l=a?new hh(o,r):null,e.push({label:i,labelLine:u.labelLine,rect:s,localRect:o,obb:l,priority:u.priority,defaultAttr:u.defaultAttr,layoutOption:u.computedLayoutOption,axisAligned:a,transform:r}))}return e}function d1(s,l,u,t,e,n){var h=s.length;if(!(h<2)){s.sort(function(t,e){return t.rect[l]-e.rect[l]});for(var i=0,o=!1,r=0,a=0;a<h;a++){var c,p=s[a],d=p.rect;(c=d[l]-i)<0&&(d[l]-=c,p.label[l]-=c,o=!0),r+=Math.max(-c,0),i=d[l]+d[u]}0<r&&n&&x(-r/h,0,h);var f,g,y=s[0],m=s[h-1];return v(),f<0&&w(-f,.8),g<0&&w(g,.8),v(),_(f,g,1),_(g,f,-1),v(),f<0&&b(-f),g<0&&b(g),o}function v(){f=y.rect[l]-t,g=e-m.rect[l]-m.rect[u]}function _(t,e,n){t<0&&(0<(e=Math.min(e,-t))?(x(e*n,0,h),(e=e+t)<0&&w(-e*n,1)):w(-t*n,1))}function x(t,e,n){0!==t&&(o=!0);for(var i=e;i<n;i++){var r=s[i];r.rect[l]+=t,r.label[l]+=t}}function w(t,e){for(var n=[],i=0,r=1;r<h;r++){var o=s[r-1].rect,o=Math.max(s[r].rect[l]-o[l]-o[u],0);n.push(o),i+=o}if(i){var a=Math.min(Math.abs(t)/i,e);if(0<t)for(r=0;r<h-1;r++)x(n[r]*a,0,r+1);else for(r=h-1;0<r;r--)x(-(n[r-1]*a),r,h)}}function b(t){for(var e=t<0?-1:1,n=(t=Math.abs(t),Math.ceil(t/(h-1))),i=0;i<h-1;i++)if(0<e?x(n,0,i+1):x(-n,h-i-1,h),(t-=n)<=0)return}}function f1(t){var e=[],n=(t.sort(function(t,e){return e.priority-t.priority}),new X(0,0,0,0));function i(t){var e;t.ignore||null==(e=t.ensureState("emphasis")).ignore&&(e.ignore=!1),t.ignore=!0}for(var r=0;r<t.length;r++){for(var o=t[r],a=o.axisAligned,s=o.localRect,l=o.transform,u=o.label,h=o.labelLine,c=(n.copy(o.rect),n.width-=.1,n.height-=.1,n.x+=.05,n.y+=.05,o.obb),p=!1,d=0;d<e.length;d++){var f=e[d];if(n.intersect(f.rect)){if(a&&f.axisAligned){p=!0;break}if(f.obb||(f.obb=new hh(f.localRect,f.transform)),(c=c||new hh(s,l)).intersect(f.obb)){p=!0;break}}}p?(i(u),h&&i(h)):(u.attr("ignore",o.defaultAttr.ignore),h&&h.attr("ignore",o.defaultAttr.labelGuideIgnore),e.push(o))}}function g1(t,e){var n=t.label,e=e&&e.getTextGuideLine();return{dataIndex:t.dataIndex,dataType:t.dataType,seriesIndex:t.seriesModel.seriesIndex,text:t.label.style.text,rect:t.hostRect,labelRect:t.rect,align:n.style.align,verticalAlign:n.style.verticalAlign,labelLinePoints:function(t){if(t){for(var e=[],n=0;n<t.length;n++)e.push(t[n].slice());return e}}(e&&e.shape.points)}}var y1=["align","verticalAlign","width","height","fontSize"],m1=new Qi,v1=_o(),_1=_o();function x1(t,e,n){for(var i=0;i<n.length;i++){var r=n[i];null!=e[r]&&(t[r]=e[r])}}var w1=["x","y","rotation"],b1=(S1.prototype.clearLabels=function(){this._labelList=[],this._chartViewList=[]},S1.prototype._addLabel=function(t,e,n,i,r){var o,a=i.style,s=i.__hostTarget.textConfig||{},l=i.getComputedTransform(),u=i.getBoundingRect().plain(),l=(X.applyTransform(u,u,l),l?m1.setLocalTransform(l):(m1.x=m1.y=m1.rotation=m1.originX=m1.originY=0,m1.scaleX=m1.scaleY=1),i.__hostTarget),h=(l&&(o=l.getBoundingRect().plain(),h=l.getComputedTransform(),X.applyTransform(o,o,h)),o&&l.getTextGuideLine());this._labelList.push({label:i,labelLine:h,seriesModel:n,dataIndex:t,dataType:e,layoutOption:r,computedLayoutOption:null,rect:u,hostRect:o,priority:o?o.width*o.height:0,defaultAttr:{ignore:i.ignore,labelGuideIgnore:h&&h.ignore,x:m1.x,y:m1.y,scaleX:m1.scaleX,scaleY:m1.scaleY,rotation:m1.rotation,style:{x:a.x,y:a.y,align:a.align,verticalAlign:a.verticalAlign,width:a.width,height:a.height,fontSize:a.fontSize},cursor:i.cursor,attachedPos:s.position,attachedRot:s.rotation}})},S1.prototype.addLabelsOfSeries=function(t){var n=this,i=(this._chartViewList.push(t),t.__model),r=i.get("labelLayout");(S(r)||ht(r).length)&&t.group.traverse(function(t){if(t.ignore)return!0;var e=t.getTextContent(),t=k(t);e&&!e.disableLabelLayout&&n._addLabel(t.dataIndex,t.dataType,i,e,r)})},S1.prototype.updateLayoutConfig=function(t){var e=t.getWidth(),n=t.getHeight();for(var i=0;i<this._labelList.length;i++){var r=this._labelList[i],o=r.label,a=o.__hostTarget,s=r.defaultAttr,l=void 0,l=S(r.layoutOption)?r.layoutOption(g1(r,a)):r.layoutOption,u=(r.computedLayoutOption=l=l||{},Math.PI/180),h=(a&&a.setTextConfig({local:!1,position:null!=l.x||null!=l.y?null:s.attachedPos,rotation:null!=l.rotate?l.rotate*u:s.attachedRot,offset:[l.dx||0,l.dy||0]}),!1);null!=l.x?(o.x=Gr(l.x,e),o.setStyle("x",0),h=!0):(o.x=s.x,o.setStyle("x",s.style.x)),null!=l.y?(o.y=Gr(l.y,n),o.setStyle("y",0),h=!0):(o.y=s.y,o.setStyle("y",s.style.y)),l.labelLinePoints&&(c=a.getTextGuideLine())&&(c.setShape({points:l.labelLinePoints}),h=!1),v1(o).needsUpdateLabelLine=h,o.rotation=null!=l.rotate?l.rotate*u:s.rotation,o.scaleX=s.scaleX,o.scaleY=s.scaleY;for(var c,p=0;p<y1.length;p++){var d=y1[p];o.setStyle(d,(null!=l[d]?l:s.style)[d])}l.draggable?(o.draggable=!0,o.cursor="move",a&&(c=r.seriesModel,null!=r.dataIndex&&(c=r.seriesModel.getData(r.dataType).getItemModel(r.dataIndex)),o.on("drag",function(t,e){return function(){s1(t,e)}}(a,c.getModel("labelLine"))))):(o.off("drag"),o.cursor=s.cursor)}},S1.prototype.layout=function(t){var e,n,i=t.getWidth(),t=t.getHeight(),r=p1(this._labelList),o=ut(r,function(t){return"shiftX"===t.layoutOption.moveOverlap}),a=ut(r,function(t){return"shiftY"===t.layoutOption.moveOverlap});d1(o,"x","width",0,i,e),d1(a,"y","height",0,t,n),f1(ut(r,function(t){return t.layoutOption.hideOverlap}))},S1.prototype.processLabelsOverall=function(){var a=this;O(this._chartViewList,function(t){var i=t.__model,r=t.ignoreLabelLineUpdate,o=i.isAnimationEnabled();t.group.traverse(function(t){if(t.ignore&&!t.forceLabelAnimation)return!0;var e=!r,n=t.getTextContent();(e=!e&&n?v1(n).needsUpdateLabelLine:e)&&a._updateLabelLine(t,i),o&&a._animateLabels(t,i)})})},S1.prototype._updateLabelLine=function(t,e){var n=t.getTextContent(),i=k(t),r=i.dataIndex;if(n&&null!=r){var n=e.getData(i.dataType),e=n.getItemModel(r),i={},r=n.getItemVisual(r,"style"),n=n.getVisual("drawType"),r=(i.stroke=r[n],e.getModel("labelLine")),o=t,a=function(t,e){for(var n={normal:t.getModel(e=e||"labelLine")},i=0;i<Hs.length;i++){var r=Hs[i];n[r]=t.getModel([r,e])}return n}(e),n=i,s=o.getTextGuideLine(),l=o.getTextContent();if(l){for(var e=a.normal,u=e.get("show"),h=l.ignore,c=0;c<Gs.length;c++){var p,d=Gs[c],f=a[d],g="normal"===d;f&&(p=f.get("show"),(g?h:N(l.states[d]&&l.states[d].ignore,h))||!N(p,u)?(p=g?s:s&&s.states.normal)&&(p.ignore=!0):(s||(s=new Ou,o.setTextGuideLine(s),g||!h&&u||h1(s,!0,"normal",a.normal),o.stateProxy&&(s.stateProxy=o.stateProxy)),h1(s,!1,d,f)))}s&&(E(s.style,n),s.style.fill=null,n=e.get("showAbove"),(o.textGuideLineConfig=o.textGuideLineConfig||{}).showAbove=n||!1,s.buildPath=c1)}else s&&o.removeTextGuideLine();s1(t,r)}},S1.prototype._animateLabels=function(t,e){var n,i,r,o,a,s=t.getTextContent(),l=t.getTextGuideLine();!s||!t.forceLabelAnimation&&(s.ignore||s.invisible||t.disableLabelAnimation||_h(t))||(o=(r=v1(s)).oldLayout,n=(i=k(t)).dataIndex,a={x:s.x,y:s.y,rotation:s.rotation},i=e.getData(i.dataType),o?(s.attr(o),(t=t.prevStates)&&(0<=I(t,"select")&&s.attr(r.oldLayoutSelect),0<=I(t,"emphasis"))&&s.attr(r.oldLayoutEmphasis),mh(s,a,e,n)):(s.attr(a),oc(s).valueAnimation||(t=N(s.style.opacity,1),s.style.opacity=0,vh(s,{style:{opacity:t}},e,n))),r.oldLayout=a,s.states.select&&(x1(t=r.oldLayoutSelect={},a,w1),x1(t,s.states.select,w1)),s.states.emphasis&&(x1(t=r.oldLayoutEmphasis={},a,w1),x1(t,s.states.emphasis,w1)),ac(s,n,i,e,e)),!l||l.ignore||l.invisible||(o=(r=_1(l)).oldLayout,a={points:l.shape.points},o?(l.attr({shape:o}),mh(l,{shape:a},e)):(l.setShape(a),l.style.strokePercent=0,vh(l,{style:{strokePercent:1}},e)),r.oldLayout=a)},S1);function S1(){this._labelList=[],this._chartViewList=[]}var M1=_o();function T1(t){t.registerUpdateLifecycle("series:beforeupdate",function(t,e,n){(M1(e).labelManager||(M1(e).labelManager=new b1)).clearLabels()}),t.registerUpdateLifecycle("series:layoutlabels",function(t,e,n){var i=M1(e).labelManager;n.updatedSeries.forEach(function(t){i.addLabelsOfSeries(e.getViewOfSeriesModel(t))}),i.updateLayoutConfig(e),i.layout(e),i.processLabelsOverall()})}function C1(t,e,n){var i=H.createCanvas(),r=e.getWidth(),e=e.getHeight(),o=i.style;return o&&(o.position="absolute",o.left="0",o.top="0",o.width=r+"px",o.height=e+"px",i.setAttribute("data-zr-dom-id",t)),i.width=r*n,i.height=e*n,i}__(T1);u(D1,I1=ae),D1.prototype.getElementCount=function(){return this.__endIndex-this.__startIndex},D1.prototype.afterBrush=function(){this.__prevStartIndex=this.__startIndex,this.__prevEndIndex=this.__endIndex},D1.prototype.initContext=function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},D1.prototype.setUnpainted=function(){this.__firstTimePaint=!0},D1.prototype.createBackBuffer=function(){var t=this.dpr;this.domBack=C1("back-"+this.id,this.painter,t),this.ctxBack=this.domBack.getContext("2d"),1!==t&&this.ctxBack.scale(t,t)},D1.prototype.createRepaintRects=function(t,e,n,i){if(this.__firstTimePaint)return this.__firstTimePaint=!1,null;var l=[],u=this.maxRepaintRectCount,h=!1,c=new X(0,0,0,0);function r(t){if(t.isFinite()&&!t.isZero())if(0===l.length)(e=new X(0,0,0,0)).copy(t),l.push(e);else{for(var e,n=!1,i=1/0,r=0,o=0;o<l.length;++o){var a=l[o];if(a.intersect(t)){var s=new X(0,0,0,0);s.copy(a),s.union(t),l[o]=s,n=!0;break}h&&(c.copy(t),c.union(a),s=t.width*t.height,a=a.width*a.height,(a=c.width*c.height-s-a)<i)&&(i=a,r=o)}h&&(l[r].union(t),n=!0),n||((e=new X(0,0,0,0)).copy(t),l.push(e)),h=h||l.length>=u}}for(var o,a=this.__startIndex;a<this.__endIndex;++a)(s=t[a])&&(d=s.shouldBePainted(n,i,!0,!0),(p=s.__isRendered&&(s.__dirty&Xe||!d)?s.getPrevPaintRect():null)&&r(p),o=d&&(s.__dirty&Xe||!s.__isRendered)?s.getPaintRect():null)&&r(o);for(a=this.__prevStartIndex;a<this.__prevEndIndex;++a){var s,p,d=(s=e[a]).shouldBePainted(n,i,!0,!0);!s||d&&s.__zr||!s.__isRendered||(p=s.getPrevPaintRect())&&r(p)}do{for(var f=!1,a=0;a<l.length;)if(l[a].isZero())l.splice(a,1);else{for(var g=a+1;g<l.length;)l[a].intersect(l[g])?(f=!0,l[a].union(l[g]),l.splice(g,1)):g++;a++}}while(f);return this._paintRects=l},D1.prototype.debugGetPaintRects=function(){return(this._paintRects||[]).slice()},D1.prototype.resize=function(t,e){var n=this.dpr,i=this.dom,r=i.style,o=this.domBack;r&&(r.width=t+"px",r.height=e+"px"),i.width=t*n,i.height=e*n,o&&(o.width=t*n,o.height=e*n,1!==n)&&this.ctxBack.scale(n,n)},D1.prototype.clear=function(t,o,e){var n=this.dom,a=this.ctx,i=n.width,r=n.height,s=(o=o||this.clearColor,this.motionBlur&&!t),l=this.lastFrameAlpha,u=this.dpr,h=this,c=(s&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(n,0,0,i/u,r/u)),this.domBack);function p(t,e,n,i){var r;a.clearRect(t,e,n,i),o&&"transparent"!==o&&(r=void 0,vt(o)?(r=o.__canvasGradient||Fy(a,o,{x:0,y:0,width:n,height:i}),o.__canvasGradient=r):_t(o)&&(r=jy(a,o,{dirty:function(){h.setUnpainted(),h.__painter.refresh()}})),a.save(),a.fillStyle=r||o,a.fillRect(t,e,n,i),a.restore()),s&&(a.save(),a.globalAlpha=l,a.drawImage(c,t,e,n,i),a.restore())}!e||s?p(0,0,i,r):e.length&&O(e,function(t){p(t.x*u,t.y*u,t.width*u,t.height*u)})};var I1,k1=D1;function D1(t,e,n){var i,r=I1.call(this)||this,t=(r.motionBlur=!1,r.lastFrameAlpha=.7,r.dpr=1,r.virtual=!1,r.config={},r.incremental=!1,r.zlevel=0,r.maxRepaintRectCount=5,r.__dirty=!0,r.__firstTimePaint=!0,r.__used=!1,r.__drawIndex=0,r.__startIndex=0,r.__endIndex=0,r.__prevStartIndex=null,r.__prevEndIndex=null,n=n||Ni,"string"==typeof t?i=C1(t,e,n):R(t)&&(t=(i=t).id),r.id=t,(r.dom=i).style);return t&&(Nt(i),i.onselectstart=function(){return!1},t.padding="0",t.margin="0",t.borderWidth="0"),r.painter=e,r.dpr=n,r}var A1=314159;v.prototype.getType=function(){return"canvas"},v.prototype.isSingleCanvas=function(){return this._singleCanvas},v.prototype.getViewportRoot=function(){return this._domRoot},v.prototype.getViewportRootOffset=function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},v.prototype.refresh=function(t){var e=this.storage.getDisplayList(!0),n=this._prevDisplayList,i=this._zlevelList;this._redrawId=Math.random(),this._paintList(e,n,t,this._redrawId);for(var r=0;r<i.length;r++){var o,a=i[r],a=this._layers[a];!a.__builtin__&&a.refresh&&(o=0===r?this._backgroundColor:null,a.refresh(o))}return this._opts.useDirtyRect&&(this._prevDisplayList=e.slice()),this},v.prototype.refreshHover=function(){this._paintHoverList(this.storage.getDisplayList(!1))},v.prototype._paintHoverList=function(t){var e=t.length,n=this._hoverlayer;if(n&&n.clear(),e){for(var i,r={inHover:!0,viewWidth:this._width,viewHeight:this._height},o=0;o<e;o++){var a=t[o];a.__inHover&&(n=n||(this._hoverlayer=this.getLayer(1e5)),i||(i=n.ctx).save(),lm(i,a,r,o===e-1))}i&&i.restore()}},v.prototype.getHoverLayer=function(){return this.getLayer(1e5)},v.prototype.paintOne=function(t,e){sm(t,e)},v.prototype._paintList=function(t,e,n,i){var r,o,a;this._redrawId===i&&(n=n||!1,this._updateLayerStatus(t),r=(o=this._doPaintList(t,e,n)).finished,o=o.needsRefreshHover,this._needsManuallyCompositing&&this._compositeManually(),o&&this._paintHoverList(t),r?this.eachLayer(function(t){t.afterBrush&&t.afterBrush()}):(a=this,Qe(function(){a._paintList(t,e,n,i)})))},v.prototype._compositeManually=function(){var e=this.getLayer(A1).ctx,n=this._domRoot.width,i=this._domRoot.height;e.clearRect(0,0,n,i),this.eachBuiltinLayer(function(t){t.virtual&&e.drawImage(t.dom,0,0,n,i)})},v.prototype._doPaintList=function(d,f,g){for(var y=this,m=[],v=this._opts.useDirtyRect,t=0;t<this._zlevelList.length;t++){var e=this._zlevelList[t],e=this._layers[e];e.__builtin__&&e!==this._hoverlayer&&(e.__dirty||g)&&m.push(e)}for(var _=!0,x=!1,n=function(t){function e(t){var e={inHover:!1,allClipped:!1,prevEl:null,viewWidth:y._width,viewHeight:y._height};for(i=s;i<r.__endIndex;i++){var n=d[i];if(n.__inHover&&(x=!0),y._doPaintEl(n,r,v,t,e,i===r.__endIndex-1),l)if(15<Date.now()-u)break}e.prevElClipPaths&&o.restore()}var n,i,r=m[t],o=r.ctx,a=v&&r.createRepaintRects(d,f,w._width,w._height),s=g?r.__startIndex:r.__drawIndex,l=!g&&r.incremental&&Date.now,u=l&&Date.now(),t=r.zlevel===w._zlevelList[0]?w._backgroundColor:null;r.__startIndex!==r.__endIndex&&(s!==r.__startIndex||(n=d[s]).incremental&&n.notClear&&!g)||r.clear(!1,t,a),-1===s&&(console.error("For some unknown reason. drawIndex is -1"),s=r.__startIndex);if(a)if(0===a.length)i=r.__endIndex;else for(var h=w.dpr,c=0;c<a.length;++c){var p=a[c];o.save(),o.beginPath(),o.rect(p.x*h,p.y*h,p.width*h,p.height*h),o.clip(),e(p),o.restore()}else o.save(),e(),o.restore();r.__drawIndex=i,r.__drawIndex<r.__endIndex&&(_=!1)},w=this,i=0;i<m.length;i++)n(i);return b.wxa&&O(this._layers,function(t){t&&t.ctx&&t.ctx.draw&&t.ctx.draw()}),{finished:_,needsRefreshHover:x}},v.prototype._doPaintEl=function(t,e,n,i,r,o){e=e.ctx;n?(n=t.getPaintRect(),(!i||n&&n.intersect(i))&&(lm(e,t,r,o),t.setPrevPaintRect(n))):lm(e,t,r,o)},v.prototype.getLayer=function(t,e){this._singleCanvas&&!this._needsManuallyCompositing&&(t=A1);var n=this._layers[t];return n||((n=new k1("zr_"+t,this,this.dpr)).zlevel=t,n.__builtin__=!0,this._layerConfig[t]?d(n,this._layerConfig[t],!0):this._layerConfig[t-.01]&&d(n,this._layerConfig[t-.01],!0),e&&(n.virtual=e),this.insertLayer(t,n),n.initContext()),n},v.prototype.insertLayer=function(t,e){var n,i=this._layers,r=this._zlevelList,o=r.length,a=this._domRoot,s=null,l=-1;if(!i[t]&&(n=e)&&(n.__builtin__||"function"==typeof n.resize&&"function"==typeof n.refresh)){if(0<o&&t>r[0]){for(l=0;l<o-1&&!(r[l]<t&&r[l+1]>t);l++);s=i[r[l]]}r.splice(l+1,0,t),(i[t]=e).virtual||(s?(n=s.dom).nextSibling?a.insertBefore(e.dom,n.nextSibling):a.appendChild(e.dom):a.firstChild?a.insertBefore(e.dom,a.firstChild):a.appendChild(e.dom)),e.__painter=this}},v.prototype.eachLayer=function(t,e){for(var n=this._zlevelList,i=0;i<n.length;i++){var r=n[i];t.call(e,this._layers[r],r)}},v.prototype.eachBuiltinLayer=function(t,e){for(var n=this._zlevelList,i=0;i<n.length;i++){var r=n[i],o=this._layers[r];o.__builtin__&&t.call(e,o,r)}},v.prototype.eachOtherLayer=function(t,e){for(var n=this._zlevelList,i=0;i<n.length;i++){var r=n[i],o=this._layers[r];o.__builtin__||t.call(e,o,r)}},v.prototype.getLayers=function(){return this._layers},v.prototype._updateLayerStatus=function(t){function e(t){r&&(r.__endIndex!==t&&(r.__dirty=!0),r.__endIndex=t)}if(this.eachBuiltinLayer(function(t,e){t.__dirty=t.__used=!1}),this._singleCanvas)for(var n=1;n<t.length;n++)if((s=t[n]).zlevel!==t[n-1].zlevel||s.incremental){this._needsManuallyCompositing=!0;break}for(var i,r=null,o=0,a=0;a<t.length;a++){var s,l=(s=t[a]).zlevel,u=void 0;i!==l&&(i=l,o=0),s.incremental?((u=this.getLayer(l+.001,this._needsManuallyCompositing)).incremental=!0,o=1):u=this.getLayer(l+(0<o?.01:0),this._needsManuallyCompositing),u.__builtin__||it("ZLevel "+l+" has been used by unkown layer "+u.id),u!==r&&(u.__used=!0,u.__startIndex!==a&&(u.__dirty=!0),u.__startIndex=a,u.incremental?u.__drawIndex=-1:u.__drawIndex=a,e(a),r=u),s.__dirty&Xe&&!s.__inHover&&(u.__dirty=!0,u.incremental)&&u.__drawIndex<0&&(u.__drawIndex=a)}e(a),this.eachBuiltinLayer(function(t,e){!t.__used&&0<t.getElementCount()&&(t.__dirty=!0,t.__startIndex=t.__endIndex=t.__drawIndex=0),t.__dirty&&t.__drawIndex<0&&(t.__drawIndex=t.__startIndex)})},v.prototype.clear=function(){return this.eachBuiltinLayer(this._clearLayer),this},v.prototype._clearLayer=function(t){t.clear()},v.prototype.setBackgroundColor=function(t){this._backgroundColor=t,O(this._layers,function(t){t.setUnpainted()})},v.prototype.configLayer=function(t,e){if(e){var n=this._layerConfig;n[t]?d(n[t],e,!0):n[t]=e;for(var i=0;i<this._zlevelList.length;i++){var r=this._zlevelList[i];r!==t&&r!==t+.01||d(this._layers[r],n[t],!0)}}},v.prototype.delLayer=function(t){var e=this._layers,n=this._zlevelList,i=e[t];i&&(i.dom.parentNode.removeChild(i.dom),delete e[t],n.splice(I(n,t),1))},v.prototype.resize=function(t,e){if(this._domRoot.style){var n=this._domRoot,i=(n.style.display="none",this._opts),r=this.root;if(null!=t&&(i.width=t),null!=e&&(i.height=e),t=Hy(r,0,i),e=Hy(r,1,i),n.style.display="",this._width!==t||e!==this._height){for(var o in n.style.width=t+"px",n.style.height=e+"px",this._layers)this._layers.hasOwnProperty(o)&&this._layers[o].resize(t,e);this.refresh(!0)}this._width=t,this._height=e}else{if(null==t||null==e)return;this._width=t,this._height=e,this.getLayer(A1).resize(t,e)}return this},v.prototype.clearLayer=function(t){t=this._layers[t];t&&t.clear()},v.prototype.dispose=function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},v.prototype.getRenderedCanvas=function(t){if(this._singleCanvas&&!this._compositeManually)return this._layers[A1].dom;var e=new k1("image",this,(t=t||{}).pixelRatio||this.dpr),n=(e.initContext(),e.clear(!1,t.backgroundColor||this._backgroundColor),e.ctx);if(t.pixelRatio<=this.dpr){this.refresh();var i=e.dom.width,r=e.dom.height;this.eachLayer(function(t){t.__builtin__?n.drawImage(t.dom,0,0,i,r):t.renderToCanvas&&(n.save(),t.renderToCanvas(n),n.restore())})}else for(var o={inHover:!1,viewWidth:this._width,viewHeight:this._height},a=this.storage.getDisplayList(!0),s=0,l=a.length;s<l;s++){var u=a[s];lm(n,u,o,s===l-1)}return e.dom},v.prototype.getWidth=function(){return this._width},v.prototype.getHeight=function(){return this._height};var P1=v;function v(t,e,n,i){this.type="canvas",this._zlevelList=[],this._prevDisplayList=[],this._layers={},this._layerConfig={},this._needsManuallyCompositing=!1,this.type="canvas";var r=!t.nodeName||"CANVAS"===t.nodeName.toUpperCase();this._opts=n=L({},n||{}),this.dpr=n.devicePixelRatio||Ni,this._singleCanvas=r;(this.root=t).style&&(Nt(t),t.innerHTML=""),this.storage=e;var o,a,e=this._zlevelList,s=(this._prevDisplayList=[],this._layers);r?(o=(r=t).width,a=r.height,null!=n.width&&(o=n.width),null!=n.height&&(a=n.height),this.dpr=n.devicePixelRatio||1,r.width=o*this.dpr,r.height=a*this.dpr,this._width=o,this._height=a,(o=new k1(r,this,this.dpr)).__builtin__=!0,o.initContext(),(s[A1]=o).zlevel=A1,e.push(A1),this._domRoot=t):(this._width=Hy(t,0,n),this._height=Hy(t,1,n),o=this._domRoot=(a=this._width,r=this._height,(s=document.createElement("div")).style.cssText=["position:relative","width:"+a+"px","height:"+r+"px","padding:0","margin:0","border-width:0"].join(";")+";",s),t.appendChild(o))}u(R1,L1=g),R1.prototype.init=function(t,e,n){L1.prototype.init.call(this,t,e,n),this._sourceManager=new Jf(this),eg(this)},R1.prototype.mergeOption=function(t,e){L1.prototype.mergeOption.call(this,t,e),eg(this)},R1.prototype.optionUpdated=function(){this._sourceManager.dirty()},R1.prototype.getSourceManager=function(){return this._sourceManager},R1.type="dataset",R1.defaultOption={seriesLayoutBy:Vp};var L1,O1=R1;function R1(){var t=null!==L1&&L1.apply(this,arguments)||this;return t.type="dataset",t}u(E1,N1=Pg),E1.type="dataset";var N1,z1=E1;function E1(){var t=null!==N1&&N1.apply(this,arguments)||this;return t.type="dataset",t}function B1(t){t.registerComponentModel(O1),t.registerComponentView(z1)}__([function(t){t.registerPainter("canvas",P1)},B1]),__(T1);u(H1,F1=Sg),H1.prototype.getInitialData=function(t){return cv(null,this,{useEncodeDefaulter:!0})},H1.prototype.getLegendIcon=function(t){var e=new Pr,n=Ey("line",0,t.itemHeight/2,t.itemWidth,0,t.lineStyle.stroke,!1),n=(e.add(n),n.setStyle(t.lineStyle),this.getData().getVisual("symbol")),i=this.getData().getVisual("symbolRotate"),n="none"===n?"circle":n,r=.8*t.itemHeight,r=Ey(n,(t.itemWidth-r)/2,(t.itemHeight-r)/2,r,r,t.itemStyle.fill),i=(e.add(r),r.setStyle(t.itemStyle),"inherit"===t.iconRotate?i:t.iconRotate||0);return r.rotation=i*Math.PI/180,r.setOrigin([t.itemWidth/2,t.itemHeight/2]),-1<n.indexOf("empty")&&(r.style.stroke=r.style.fill,r.style.fill="#fff",r.style.lineWidth=2),e},H1.type="series.line",H1.dependencies=["grid","polar"],H1.defaultOption={z:3,coordinateSystem:"cartesian2d",legendHoverLink:!0,clip:!0,label:{position:"top"},endLabel:{show:!1,valueAnimation:!0,distance:8},lineStyle:{width:2,type:"solid"},emphasis:{scale:!0},step:!1,smooth:!1,smoothMonotone:null,symbol:"emptyCircle",symbolSize:4,symbolRotate:null,showSymbol:!0,showAllSymbol:"auto",connectNulls:!1,sampling:"none",animationEasing:"linear",progressive:0,hoverLayerThreshold:1/0,universalTransition:{divideShape:"clone"},triggerLineEvent:!1};var F1,V1=H1;function H1(){var t=null!==F1&&F1.apply(this,arguments)||this;return t.type=H1.type,t.hasSymbolVisual=!0,t}function G1(t,e){var n,i=t.mapDimensionsAll("defaultedLabel"),r=i.length;if(1===r)return null!=(n=lf(t,e,i[0]))?n+"":null;if(r){for(var o=[],a=0;a<i.length;a++)o.push(lf(t,e,i[a]));return o.join(" ")}}u(X1,W1=Pr),X1.prototype._createSymbol=function(t,e,n,i,r){this.removeAll();r=Ey(t,-1,-1,2,2,null,r);r.attr({z2:100,culling:!0,scaleX:i[0]/2,scaleY:i[1]/2}),r.drift=Y1,this._symbolType=t,this.add(r)},X1.prototype.stopSymbolAnimation=function(t){this.childAt(0).stopAnimation(null,t)},X1.prototype.getSymbolType=function(){return this._symbolType},X1.prototype.getSymbolPath=function(){return this.childAt(0)},X1.prototype.highlight=function(){pl(this.childAt(0))},X1.prototype.downplay=function(){dl(this.childAt(0))},X1.prototype.setZ=function(t,e){var n=this.childAt(0);n.zlevel=t,n.z=e},X1.prototype.setDraggable=function(t){var e=this.childAt(0);e.draggable=t,e.cursor=t?"move":e.cursor},X1.prototype.updateData=function(t,e,n,i){this.silent=!1;var r,o,a,s=t.getItemVisual(e,"symbol")||"circle",l=t.hostModel,u=X1.getSymbolSize(t,e),h=s!==this._symbolType,c=i&&i.disableAnimation;h?(r=t.getItemVisual(e,"symbolKeepAspect"),this._createSymbol(s,t,e,u,r)):((o=this.childAt(0)).silent=!1,a={scaleX:u[0]/2,scaleY:u[1]/2},c?o.attr(a):mh(o,a,l,e),gh(s=o).oldStyle=s.style),this._updateCommon(t,e,u,n,i),h&&(o=this.childAt(0),c||(a={scaleX:this._sizeX,scaleY:this._sizeY,style:{opacity:o.style.opacity}},o.scaleX=o.scaleY=0,o.style.opacity=0,vh(o,a,l,e))),c&&this.childAt(0).stopAnimation("leave"),this._seriesModel=l},X1.prototype._updateCommon=function(e,t,n,i,r){var o,a,s,l,u,h,c,p,d=this.childAt(0),f=e.hostModel,g=(i&&(o=i.emphasisItemStyle,s=i.blurItemStyle,a=i.selectItemStyle,l=i.focus,u=i.blurScope,c=i.labelStatesModels,p=i.hoverScale,y=i.cursorStyle,h=i.emphasisDisabled),i&&!e.hasItemOption||(o=(g=(i=i&&i.itemModel?i.itemModel:e.getItemModel(t)).getModel("emphasis")).getModel("itemStyle").getItemStyle(),a=i.getModel(["select","itemStyle"]).getItemStyle(),s=i.getModel(["blur","itemStyle"]).getItemStyle(),l=g.get("focus"),u=g.get("blurScope"),h=g.get("disabled"),c=Qh(i),p=g.getShallow("scale"),y=i.getShallow("cursor")),e.getItemVisual(t,"symbolRotate")),i=(d.attr("rotation",(g||0)*Math.PI/180||0),By(e.getItemVisual(t,"symbolOffset"),n)),g=(i&&(d.x=i[0],d.y=i[1]),y&&d.attr("cursor",y),e.getItemVisual(t,"style")),i=g.fill,y=(d instanceof ls?(y=d.style,d.useStyle(L({image:y.image,x:y.x,y:y.y,width:y.width,height:y.height},g))):(d.__isEmptyBrush?d.useStyle(L({},g)):d.useStyle(g),d.style.decal=null,d.setColor(i,r&&r.symbolInnerColor),d.style.strokeNoScale=!0),e.getItemVisual(t,"liftZ")),m=this._z2,v=(null!=y?null==m&&(this._z2=d.z2,d.z2+=y):null!=m&&(d.z2=m,this._z2=null),r&&r.useNameLabel);$h(d,c,{labelFetcher:f,labelDataIndex:t,defaultText:function(t){return v?e.getName(t):G1(e,t)},inheritColor:i,defaultOpacity:g.opacity}),this._sizeX=n[0]/2,this._sizeY=n[1]/2;y=d.ensureState("emphasis");y.style=o,d.ensureState("select").style=a,d.ensureState("blur").style=s,p&&(m=Math.max(1.1,3/this._sizeY),y.scaleX=this._sizeX*m,y.scaleY=this._sizeY*m),this.setSymbolScale(1),Ml(this,l,u,h)},X1.prototype.setSymbolScale=function(t){this.scaleX=this.scaleY=t},X1.prototype.fadeOut=function(t,e){var n=this.childAt(0),i=this._seriesModel,r=k(this).dataIndex,o=e&&e.animation;this.silent=n.silent=!0,e&&e.fadeLabel?(e=n.getTextContent())&&xh(e,{style:{opacity:0}},i,{dataIndex:r,removeOpt:o,cb:function(){n.removeTextContent()}}):n.removeTextContent(),xh(n,{style:{opacity:0},scaleX:0,scaleY:0},i,{dataIndex:r,cb:t,removeOpt:o})},X1.getSymbolSize=function(t,e){return[(t=F(t=t.getItemVisual(e,"symbolSize"))?t:[+t,+t])[0]||0,t[1]||0]};var W1,U1=X1;function X1(t,e,n,i){var r=W1.call(this)||this;return r.updateData(t,e,n,i),r}function Y1(t,e){this.parent.drift(t,e)}function q1(t,e,n,i){return e&&!isNaN(e[0])&&!isNaN(e[1])&&(!i.isIgnore||!i.isIgnore(n))&&(!i.clipShape||i.clipShape.contain(e[0],e[1]))&&"none"!==t.getItemVisual(n,"symbol")}function Z1(t){return(t=null==t||R(t)?t:{isIgnore:t})||{}}function j1(t){var t=t.hostModel,e=t.getModel("emphasis");return{emphasisItemStyle:e.getModel("itemStyle").getItemStyle(),blurItemStyle:t.getModel(["blur","itemStyle"]).getItemStyle(),selectItemStyle:t.getModel(["select","itemStyle"]).getItemStyle(),focus:e.get("focus"),blurScope:e.get("blurScope"),emphasisDisabled:e.get("disabled"),hoverScale:e.get("scale"),labelStatesModels:Qh(t),cursorStyle:t.get("cursor")}}$1.prototype.updateData=function(o,a){this._progressiveEls=null,a=Z1(a);var s=this.group,l=o.hostModel,u=this._data,h=this._SymbolCtor,c=a.disableAnimation,p=j1(o),d={disableAnimation:c},f=a.getSymbolPoint||function(t){return o.getItemLayout(t)};u||s.removeAll(),o.diff(u).add(function(t){var e,n=f(t);q1(o,n,t,a)&&((e=new h(o,t,p,d)).setPosition(n),o.setItemGraphicEl(t,e),s.add(e))}).update(function(t,e){var n,i,e=u.getItemGraphicEl(e),r=f(t);q1(o,r,t,a)?(n=o.getItemVisual(t,"symbol")||"circle",i=e&&e.getSymbolType&&e.getSymbolType(),!e||i&&i!==n?(s.remove(e),(e=new h(o,t,p,d)).setPosition(r)):(e.updateData(o,t,p,d),i={x:r[0],y:r[1]},c?e.attr(i):mh(e,i,l)),s.add(e),o.setItemGraphicEl(t,e)):s.remove(e)}).remove(function(t){var e=u.getItemGraphicEl(t);e&&e.fadeOut(function(){s.remove(e)})}).execute(),this._getSymbolPoint=f,this._data=o},$1.prototype.updateLayout=function(){var n=this,t=this._data;t&&t.eachItemGraphicEl(function(t,e){e=n._getSymbolPoint(e);t.setPosition(e),t.markRedraw()})},$1.prototype.incrementalPrepareUpdate=function(t){this._seriesScope=j1(t),this._data=null,this.group.removeAll()},$1.prototype.incrementalUpdate=function(t,e,n){function i(t){t.isGroup||(t.incremental=!0,t.ensureState("emphasis").hoverLayer=!0)}this._progressiveEls=[],n=Z1(n);for(var r=t.start;r<t.end;r++){var o,a=e.getItemLayout(r);q1(e,a,r,n)&&((o=new this._SymbolCtor(e,r,this._seriesScope)).traverse(i),o.setPosition(a),this.group.add(o),e.setItemGraphicEl(r,o),this._progressiveEls.push(o))}},$1.prototype.eachRendered=function(t){Yh(this._progressiveEls||this.group,t)},$1.prototype.remove=function(t){var e=this.group,n=this._data;n&&t?n.eachItemGraphicEl(function(t){t.fadeOut(function(){e.remove(t)})}):e.removeAll()};var K1=$1;function $1(t){this.group=new Pr,this._SymbolCtor=t||U1}function Q1(t,e,n){var i=t.getBaseAxis(),r=t.getOtherAxis(i),n=function(t,e){var n=0,t=t.scale.getExtent();"start"===e?n=t[0]:"end"===e?n=t[1]:0<t[0]?n=t[0]:t[1]<0&&(n=t[1]);return n}(r,n),i=i.dim,r=r.dim,o=e.mapDimension(r),a=e.mapDimension(i),s="x"===r||"radius"===r?1:0,t=B(t.dimensions,function(t){return e.mapDimension(t)}),l=!1,u=e.getCalculationInfo("stackResultDimension");return uv(e,t[0])&&(l=!0,t[0]=u),uv(e,t[1])&&(l=!0,t[1]=u),{dataDimsForPoint:t,valueStart:n,valueAxisDim:r,baseAxisDim:i,stacked:!!l,valueDim:o,baseDim:a,baseDataOffset:s,stackedOverDimension:e.getCalculationInfo("stackedOverDimension")}}function J1(t,e,n,i){var r=NaN,o=(t.stacked&&(r=n.get(n.getCalculationInfo("stackedOverDimension"),i)),isNaN(r)&&(r=t.valueStart),t.baseDataOffset),a=[];return a[o]=n.get(t.baseDim,i),a[1-o]=r,e.dataToPoint(a)}var tx=Math.min,ex=Math.max;function nx(t,e){return isNaN(t)||isNaN(e)}function ix(t,e,n,i,r,o,a,s,l){for(var u,h,c,p,d=n,f=0;f<i;f++){var g=e[2*d],y=e[2*d+1];if(r<=d||d<0)break;if(nx(g,y)){if(l){d+=o;continue}break}if(d===n)t[0<o?"moveTo":"lineTo"](g,y),c=g,p=y;else{var m=g-u,v=y-h;if(m*m+v*v<.5){d+=o;continue}if(0<a){for(var _=d+o,x=e[2*_],w=e[2*_+1];x===g&&w===y&&f<i;)f++,d+=o,x=e[2*(_+=o)],w=e[2*_+1],g=e[2*d],y=e[2*d+1];var b=f+1;if(l)for(;nx(x,w)&&b<i;)b++,x=e[2*(_+=o)],w=e[2*_+1];var S,M,T,C,I,k,D,A,P,m=0,v=0,L=void 0,O=void 0;i<=b||nx(x,w)?(D=g,A=y):(m=x-u,v=w-h,S=g-u,M=x-g,T=y-h,C=w-y,k=I=void 0,O="x"===s?(D=g-(P=0<m?1:-1)*(I=Math.abs(S))*a,A=y,L=g+P*(k=Math.abs(M))*a,y):"y"===s?(A=y-(P=0<v?1:-1)*(I=Math.abs(T))*a,L=D=g,y+P*(k=Math.abs(C))*a):(I=Math.sqrt(S*S+T*T),D=g-m*a*(1-(S=(k=Math.sqrt(M*M+C*C))/(k+I))),A=y-v*a*(1-S),O=y+v*a*S,L=tx(L=g+m*a*S,ex(x,g)),O=tx(O,ex(w,y)),L=ex(L,tx(x,g)),A=y-(v=(O=ex(O,tx(w,y)))-y)*I/k,D=tx(D=g-(m=L-g)*I/k,ex(u,g)),A=tx(A,ex(h,y)),L=g+(m=g-(D=ex(D,tx(u,g))))*k/I,y+(v=y-(A=ex(A,tx(h,y))))*k/I)),t.bezierCurveTo(c,p,D,A,g,y),c=L,p=O}else t.lineTo(g,y)}u=g,h=y,d+=o}return f}var rx,ox=function(){this.smooth=0,this.smoothConstraint=!0},ax=(u(sx,rx=j),sx.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},sx.prototype.getDefaultShape=function(){return new ox},sx.prototype.buildPath=function(t,e){var n=e.points,i=0,r=n.length/2;if(e.connectNulls){for(;0<r&&nx(n[2*r-2],n[2*r-1]);r--);for(;i<r&&nx(n[2*i],n[2*i+1]);i++);}for(;i<r;)i+=ix(t,n,i,r,r,1,e.smooth,e.smoothMonotone,e.connectNulls)+1},sx.prototype.getPointOn=function(t,e){this.path||(this.createPathProxy(),this.buildPath(this.path,this.shape));for(var n,i,r=this.path.data,o=Ea.CMD,a="x"===e,s=[],l=0;l<r.length;){var u=void 0,h=void 0;switch(r[l++]){case o.M:n=r[l++],i=r[l++];break;case o.L:var c,u=r[l++],h=r[l++];if((c=a?(t-n)/(u-n):(t-i)/(h-i))<=1&&0<=c)return v=a?(h-i)*c+i:(u-n)*c+n,a?[t,v]:[v,t];n=u,i=h;break;case o.C:u=r[l++],h=r[l++];var p=r[l++],d=r[l++],f=r[l++],g=r[l++],y=a?dn(n,u,p,f,t,s):dn(i,h,d,g,t,s);if(0<y)for(var m=0;m<y;m++){var v,_=s[m];if(_<=1&&0<=_)return v=a?cn(i,h,d,g,_):cn(n,u,p,f,_),a?[t,v]:[v,t]}n=f,i=g}}},sx);function sx(t){t=rx.call(this,t)||this;return t.type="ec-polyline",t}u(hx,lx=ox);var lx,ux=hx;function hx(){return null!==lx&&lx.apply(this,arguments)||this}u(dx,cx=j),dx.prototype.getDefaultShape=function(){return new ux},dx.prototype.buildPath=function(t,e){var n=e.points,i=e.stackedOnPoints,r=0,o=n.length/2,a=e.smoothMonotone;if(e.connectNulls){for(;0<o&&nx(n[2*o-2],n[2*o-1]);o--);for(;r<o&&nx(n[2*r],n[2*r+1]);r++);}for(;r<o;){var s=ix(t,n,r,o,o,1,e.smooth,a,e.connectNulls);ix(t,i,r+s-1,s,o,-1,e.stackedOnSmooth,a,e.connectNulls),r+=s+1,t.closePath()}};var cx,px=dx;function dx(t){t=cx.call(this,t)||this;return t.type="ec-polygon",t}function fx(t,e){if(t.length===e.length){for(var n=0;n<t.length;n++)if(t[n]!==e[n])return;return 1}}function gx(t){for(var e=1/0,n=1/0,i=-1/0,r=-1/0,o=0;o<t.length;){var a=t[o++],s=t[o++];isNaN(a)||(e=Math.min(a,e),i=Math.max(a,i)),isNaN(s)||(n=Math.min(s,n),r=Math.max(s,r))}return[[e,n],[i,r]]}function yx(t,e){var t=gx(t),n=t[0],t=t[1],e=gx(e),i=e[0],e=e[1];return Math.max(Math.abs(n[0]-i[0]),Math.abs(n[1]-i[1]),Math.abs(t[0]-e[0]),Math.abs(t[1]-e[1]))}function mx(t){return ft(t)?t:t?.5:0}function vx(t,e,n){for(var e=e.getBaseAxis(),i="x"===e.dim||"radius"===e.dim?0:1,r=[],o=0,a=[],s=[],l=[];o<t.length-2;o+=2)switch(l[0]=t[o+2],l[1]=t[o+3],s[0]=t[o],s[1]=t[o+1],r.push(s[0],s[1]),n){case"end":a[i]=l[i],a[1-i]=s[1-i],r.push(a[0],a[1]);break;case"middle":var u=[];a[i]=u[i]=(s[i]+l[i])/2,a[1-i]=s[1-i],u[1-i]=l[1-i],r.push(a[0],a[1]),r.push(u[0],u[1]);break;default:a[i]=s[i],a[1-i]=l[1-i],r.push(a[0],a[1])}return r.push(t[o++],t[o++]),r}function _x(t,e,n){var i=t.getVisual("visualMeta");if(i&&i.length&&t.count()&&"cartesian2d"===e.type){for(var r,o=i.length-1;0<=o;o--){var a,s=t.getDimensionInfo(i[o].dimension);if("x"===(a=s&&s.coordDim)||"y"===a){r=i[o];break}}if(r){var l=e.getAxis(a),e=B(r.stops,function(t){return{coord:l.toGlobalCoord(l.dataToCoord(t.value)),color:t.color}}),u=e.length,h=r.outerColors.slice(),n=(u&&e[0].coord>e[u-1].coord&&(e.reverse(),h.reverse()),function(t,e){var n,i,r=[],o=t.length;function a(t,e,n){var i=t.coord;return{coord:n,color:qn((n-i)/(e.coord-i),[t.color,e.color])}}for(var s=0;s<o;s++){var l=t[s],u=l.coord;if(u<0)n=l;else{if(e<u){i?r.push(a(i,l,e)):n&&r.push(a(n,l,0),a(n,l,e));break}n&&(r.push(a(n,l,0)),n=null),r.push(l),i=l}}return r}(e,"x"===a?n.getWidth():n.getHeight())),c=n.length;if(!c&&u)return e[0].coord<0?h[1]||e[u-1].color:h[0]||e[0].color;var p=n[0].coord-10,u=n[c-1].coord+10,d=u-p;if(d<.001)return"transparent";O(n,function(t){t.offset=(t.coord-p)/d}),n.push({offset:c?n[c-1].offset:.5,color:h[1]||"transparent"}),n.unshift({offset:c?n[0].offset:.5,color:h[0]||"transparent"});e=new nh(0,0,0,0,n,!0);return e[a]=p,e[a+"2"]=u,e}}}function xx(t,e,n){var t=t.get("showAllSymbol"),i="auto"===t;if(!t||i){var r,o,a=n.getAxesByScale("ordinal")[0];if(a)if(!i||!function(t,e){for(var n=t.getExtent(),i=Math.abs(n[1]-n[0])/t.scale.count(),r=(isNaN(i)&&(i=0),e.count()),o=Math.max(1,Math.round(r/5)),a=0;a<r;a+=o)if(1.5*U1.getSymbolSize(e,a)[t.isHorizontal()?1:0]>i)return;return 1}(a,e))return r=e.mapDimension(a.dim),o={},O(a.getViewLabels(),function(t){t=a.scale.getRawOrdinalNumber(t.tickValue);o[t]=1}),function(t){return!o.hasOwnProperty(e.get(r,t))}}}function bx(t){for(var e,n,i=t.length/2;0<i&&(e=t[2*i-2],n=t[2*i-1],isNaN(e)||isNaN(n));i--);return i-1}function Sx(t,e){return[t[2*e],t[2*e+1]]}function Mx(t){if(t.get(["endLabel","show"]))return 1;for(var e=0;e<Hs.length;e++)if(t.get([Hs[e],"endLabel","show"]))return 1}function Tx(n,i,e,t){var r,o,a,s,l,u,h,c,p,d,f,g,y,m,v,_,x;return x="cartesian2d",i.type===x?(r=t.getModel("endLabel"),o=r.get("valueAnimation"),a=t.getData(),s={lastFrameIndex:0},x=Mx(t)?function(t,e){n._endLabelOnDuring(t,e,a,s,o,r,i)}:null,l=i.getBaseAxis().isHorizontal(),h=e,c=t,p=function(){var t=n._endLabel;t&&e&&null!=s.originalX&&t.attr({x:s.originalX,y:s.originalY})},d=x,f=(m=(u=i).getArea()).x,g=m.y,y=m.width,m=m.height,v=c.get(["lineStyle","width"])||2,f-=v/2,g-=v/2,y+=v,m+=v,f=Math.floor(f),y=Math.round(y),_=new ms({shape:{x:f,y:g,width:y,height:m}}),h&&(h=(v=u.getBaseAxis()).isHorizontal(),u=v.inverse,h?(u&&(_.shape.x+=y),_.shape.width=0):(u||(_.shape.y+=m),_.shape.height=0),v=S(d)?function(t){d(t,_)}:null,vh(_,{shape:{width:y,height:m,x:f,y:g}},c,null,p,v)),h=_,t.get("clip",!0)||(u=h.shape,y=Math.max(u.width,u.height),l?(u.y-=y,u.height+=2*y):(u.x-=y,u.width+=2*y)),x&&x(1,h),h):(m=e,f=t,c=(g=i).getArea(),p=Wr(c.r0,1),v=Wr(c.r,1),l=new xu({shape:{cx:Wr(g.cx,1),cy:Wr(g.cy,1),r0:p,r:v,startAngle:c.startAngle,endAngle:c.endAngle,clockwise:c.clockwise}}),m&&("angle"===g.getBaseAxis().dim?l.shape.endAngle=c.startAngle:l.shape.r=p,vh(l,{shape:{endAngle:c.endAngle,r:v}},f)),l)}u(kx,Cx=zg),kx.prototype.init=function(){var t=new Pr,e=new K1;this.group.add(e.group),this._symbolDraw=e,this._lineGroup=t},kx.prototype.render=function(t,e,n){function i(t){r._changePolyState(t)}var r=this,o=t.coordinateSystem,a=this.group,s=t.getData(),l=t.getModel("lineStyle"),u=t.getModel("areaStyle"),h=s.getLayout("points")||[],c="polar"===o.type,p=this._coordSys,d=this._symbolDraw,f=this._polyline,g=this._polygon,y=this._lineGroup,m=t.get("animation"),v=!u.isEmpty(),_=u.get("origin"),x=Q1(o,s,_),x=v&&function(t,e,n){if(!n.valueDim)return[];for(var i=e.count(),r=Nv(2*i),o=0;o<i;o++){var a=J1(n,t,e,o);r[2*o]=a[0],r[2*o+1]=a[1]}return r}(o,s,x),w=t.get("showSymbol"),b=w&&!c&&xx(t,s,o),S=this._data,M=(S&&S.eachItemGraphicEl(function(t,e){t.__temp&&(a.remove(t),S.setItemGraphicEl(e,null))}),w||d.remove(),a.add(y),!c&&t.get("step")),T=(o&&o.getArea&&t.get("clip",!0)&&(null!=(C=o.getArea()).width?(C.x-=.1,C.y-=.1,C.width+=.2,C.height+=.2):C.r0&&(C.r0-=.5,C.r+=.5)),this._clipShapeForSymbol=C,_x(s,o,n)||s.getVisual("style")[s.getVisual("drawType")]),p=(f&&p.type===o.type&&M===this._step?(v&&!g?g=this._newPolygon(h,x):g&&!v&&(y.remove(g),g=this._polygon=null),c||this._initOrUpdateEndLabel(t,o,gp(T)),(p=y.getClipPath())?vh(p,{shape:Tx(this,o,!1,t).shape},t):y.setClipPath(Tx(this,o,!0,t)),w&&d.updateData(s,{isIgnore:b,clipShape:C,disableAnimation:!0,getSymbolPoint:function(t){return[h[2*t],h[2*t+1]]}}),fx(this._stackedOnPoints,x)&&fx(this._points,h)||(m?this._doUpdateAnimation(s,x,o,n,M,_):(M&&(h=vx(h,o,M),x=x&&vx(x,o,M)),f.setShape({points:h}),g&&g.setShape({points:h,stackedOnPoints:x})))):(w&&d.updateData(s,{isIgnore:b,clipShape:C,disableAnimation:!0,getSymbolPoint:function(t){return[h[2*t],h[2*t+1]]}}),m&&this._initSymbolLabelAnimation(s,o,C),M&&(h=vx(h,o,M),x=x&&vx(x,o,M)),f=this._newPolyline(h),v&&(g=this._newPolygon(h,x)),c||this._initOrUpdateEndLabel(t,o,gp(T)),y.setClipPath(Tx(this,o,!0,t))),t.getModel("emphasis")),n=p.get("focus"),w=p.get("blurScope"),d=p.get("disabled"),b=(f.useStyle(E(l.getLineStyle(),{fill:"none",stroke:T,lineJoin:"bevel"})),Il(f,t,"lineStyle"),0<f.style.lineWidth&&"bolder"===t.get(["emphasis","lineStyle","width"])&&(f.getState("emphasis").style.lineWidth=+f.style.lineWidth+1),k(f).seriesIndex=t.seriesIndex,Ml(f,n,w,d),mx(t.get("smooth"))),m=t.get("smoothMonotone"),C=t.get("connectNulls");f.setShape({smooth:b,smoothMonotone:m,connectNulls:C}),g&&(v=s.getCalculationInfo("stackedOnSeries"),c=0,g.useStyle(E(u.getAreaStyle(),{fill:T,opacity:.7,lineJoin:"bevel",decal:s.getVisual("style").decal})),v&&(c=mx(v.get("smooth"))),g.setShape({smooth:b,stackedOnSmooth:c,smoothMonotone:m,connectNulls:C}),Il(g,t,"areaStyle"),k(g).seriesIndex=t.seriesIndex,Ml(g,n,w,d));s.eachItemGraphicEl(function(t){t&&(t.onHoverStateChange=i)}),this._polyline.onHoverStateChange=i,this._data=s,this._coordSys=o,this._stackedOnPoints=x,this._points=h,this._step=M,this._valueOrigin=_,t.get("triggerLineEvent")&&(this.packEventData(t,f),g)&&this.packEventData(t,g)},kx.prototype.packEventData=function(t,e){k(e).eventData={componentType:"series",componentSubType:"line",componentIndex:t.componentIndex,seriesIndex:t.seriesIndex,seriesName:t.name,seriesType:"line"}},kx.prototype.highlight=function(t,e,n,i){var r=t.getData(),o=vo(r,i);if(this._changePolyState("emphasis"),!(o instanceof Array)&&null!=o&&0<=o){var a=r.getLayout("points");if(!(l=r.getItemGraphicEl(o))){var s=a[2*o],a=a[2*o+1];if(isNaN(s)||isNaN(a))return;if(this._clipShapeForSymbol&&!this._clipShapeForSymbol.contain(s,a))return;var l,u=t.get("zlevel"),h=t.get("z"),s=((l=new U1(r,o)).x=s,l.y=a,l.setZ(u,h),l.getSymbolPath().getTextContent());s&&(s.zlevel=u,s.z=h,s.z2=this._polyline.z2+1),l.__temp=!0,r.setItemGraphicEl(o,l),l.stopSymbolAnimation(!0),this.group.add(l)}l.highlight()}else zg.prototype.highlight.call(this,t,e,n,i)},kx.prototype.downplay=function(t,e,n,i){var r,o=t.getData(),a=vo(o,i);this._changePolyState("normal"),null!=a&&0<=a?(r=o.getItemGraphicEl(a))&&(r.__temp?(o.setItemGraphicEl(a,null),this.group.remove(r)):r.downplay()):zg.prototype.downplay.call(this,t,e,n,i)},kx.prototype._changePolyState=function(t){var e=this._polygon;al(this._polyline,t),e&&al(e,t)},kx.prototype._newPolyline=function(t){var e=this._polyline;return e&&this._lineGroup.remove(e),e=new ax({shape:{points:t},segmentIgnoreThreshold:2,z2:10}),this._lineGroup.add(e),this._polyline=e},kx.prototype._newPolygon=function(t,e){var n=this._polygon;return n&&this._lineGroup.remove(n),n=new px({shape:{points:t,stackedOnPoints:e},segmentIgnoreThreshold:2}),this._lineGroup.add(n),this._polygon=n},kx.prototype._initSymbolLabelAnimation=function(t,l,u){var h,c,e=l.getBaseAxis(),p=e.inverse,e=("cartesian2d"===l.type?(h=e.isHorizontal(),c=!1):"polar"===l.type&&(h="angle"===e.dim,c=!0),t.hostModel),d=e.get("animationDuration"),f=(S(d)&&(d=d(null)),e.get("animationDelay")||0),g=S(f)?f(null):f;t.eachItemGraphicEl(function(t,e){var n,i,r,o,a,s=t;s&&(o=[t.x,t.y],a=i=n=void 0,u&&(a=c?(r=u,o=l.pointToCoord(o),h?(n=r.startAngle,i=r.endAngle,-o[1]/180*Math.PI):(n=r.r0,i=r.r,o[0])):h?(n=u.x,i=u.x+u.width,t.x):(n=u.y+u.height,i=u.y,t.y)),r=i===n?0:(a-n)/(i-n),p&&(r=1-r),o=S(f)?f(e):d*r+g,a=(t=s.getSymbolPath()).getTextContent(),s.attr({scaleX:0,scaleY:0}),s.animateTo({scaleX:1,scaleY:1},{duration:200,setToFinal:!0,delay:o}),a&&a.animateFrom({style:{opacity:0}},{duration:300,delay:o}),t.disableLabelAnimation=!0)})},kx.prototype._initOrUpdateEndLabel=function(t,e,n){var u,i,r,o=t.getModel("endLabel");Mx(t)?(u=t.getData(),i=this._polyline,(r=u.getLayout("points"))?(this._endLabel||((this._endLabel=new bs({z2:200})).ignoreClip=!0,i.setTextContent(this._endLabel),i.disableLabelAnimation=!0),0<=(r=bx(r))&&($h(i,Qh(t,"endLabel"),{inheritColor:n,labelFetcher:t,labelDataIndex:r,defaultText:function(t,e,n){if(null==n)return G1(u,t);var i=u,r=n,o=i.mapDimensionsAll("defaultedLabel");if(!F(r))return r+"";for(var a=[],s=0;s<o.length;s++){var l=i.getDimensionIndex(o[s]);0<=l&&a.push(r[l])}return a.join(" ")},enableTextSetter:!0},(n=o,r=(t=(t=e).getBaseAxis()).isHorizontal(),t=t.inverse,o=r?t?"right":"left":"center",r=r?"middle":t?"top":"bottom",{normal:{align:n.get("align")||o,verticalAlign:n.get("verticalAlign")||r}})),i.textConfig.position=null)):(i.removeTextContent(),this._endLabel=null)):this._endLabel&&(this._polyline.removeTextContent(),this._endLabel=null)},kx.prototype._endLabelOnDuring=function(t,e,n,i,r,o,a){var s,l,u,h,c,p,d,f,g,y,m=this._endLabel,v=this._polyline;m&&(t<1&&null==i.originalX&&(i.originalX=m.x,i.originalY=m.y),s=n.getLayout("points"),g=(l=n.hostModel).get("connectNulls"),u=o.get("precision"),o=o.get("distance")||0,c=(a=a.getBaseAxis()).isHorizontal(),a=a.inverse,e=e.shape,h=(c?o:0)*(a?-1:1),o=(c?0:-o)*(a?-1:1),d=void 0,1<=(f=(p=(c=function(t,e,n){for(var i,r,o=t.length/2,a="x"===n?0:1,s=0,l=-1,u=0;u<o;u++)if(r=t[2*u+a],!isNaN(r)&&!isNaN(t[2*u+1-a])){if(0!==u){if(i<=e&&e<=r||e<=i&&r<=e){l=u;break}s=u}i=r}return{range:[s,l],t:(e-i)/(r-i)}}(s,a=a?c?e.x:e.y+e.height:c?e.x+e.width:e.y,e=c?"x":"y")).range)[1]-p[0])?(1<f&&!g?(y=Sx(s,p[0]),m.attr({x:y[0]+h,y:y[1]+o}),r&&(d=l.getRawValue(p[0]))):((y=v.getPointOn(a,e))&&m.attr({x:y[0]+h,y:y[1]+o}),f=l.getRawValue(p[0]),g=l.getRawValue(p[1]),r&&(d=Co(n,u,f,g,c.t))),i.lastFrameIndex=p[0]):(y=Sx(s,v=1===t||0<i.lastFrameIndex?p[0]:0),r&&(d=l.getRawValue(v)),m.attr({x:y[0]+h,y:y[1]+o})),r)&&oc(m).setLabelText(d)},kx.prototype._doUpdateAnimation=function(t,e,n,i,r,o){var a=this._polyline,s=this._polygon,l=t.hostModel,e=function(t,e,n,i,r,o){a=[],e.diff(t).add(function(t){a.push({cmd:"+",idx:t})}).update(function(t,e){a.push({cmd:"=",idx:e,idx1:t})}).remove(function(t){a.push({cmd:"-",idx:t})}).execute();for(var a,s=a,l=[],u=[],h=[],c=[],p=[],d=[],f=[],g=Q1(r,e,o),y=t.getLayout("points")||[],m=e.getLayout("points")||[],v=0;v<s.length;v++){var _=s[v],x=!0,w=void 0;switch(_.cmd){case"=":var b=2*_.idx,w=2*_.idx1,S=y[b],M=y[1+b],T=m[w],C=m[w+1];(isNaN(S)||isNaN(M))&&(S=T,M=C),l.push(S,M),u.push(T,C),h.push(n[b],n[1+b]),c.push(i[w],i[w+1]),f.push(e.getRawIndex(_.idx1));break;case"+":S=_.idx,M=g.dataDimsForPoint,T=r.dataToPoint([e.get(M[0],S),e.get(M[1],S)]),C=(w=2*S,l.push(T[0],T[1]),u.push(m[w],m[w+1]),J1(g,r,e,S));h.push(C[0],C[1]),c.push(i[w],i[w+1]),f.push(e.getRawIndex(S));break;case"-":x=!1}x&&(p.push(_),d.push(d.length))}d.sort(function(t,e){return f[t]-f[e]});for(var I=Nv(o=l.length),k=Nv(o),D=Nv(o),A=Nv(o),P=[],v=0;v<d.length;v++){var L=d[v],O=2*v,R=2*L;I[O]=l[R],I[1+O]=l[1+R],k[O]=u[R],k[1+O]=u[1+R],D[O]=h[R],D[1+O]=h[1+R],A[O]=c[R],A[1+O]=c[1+R],P[v]=p[L]}return{current:I,next:k,stackedOnCurrent:D,stackedOnNext:A,status:P}}(this._data,t,this._stackedOnPoints,e,this._coordSys,this._valueOrigin),u=e.current,h=e.stackedOnCurrent,c=e.next,p=e.stackedOnNext;if(r&&(u=vx(e.current,n,r),h=vx(e.stackedOnCurrent,n,r),c=vx(e.next,n,r),p=vx(e.stackedOnNext,n,r)),3e3<yx(u,c)||s&&3e3<yx(h,p))a.stopAnimation(),a.setShape({points:c}),s&&(s.stopAnimation(),s.setShape({points:c,stackedOnPoints:p}));else{a.shape.__points=e.current,a.shape.points=u;for(var d,n={shape:{points:c}},f=(e.current!==u&&(n.shape.__points=e.next),a.stopAnimation(),mh(a,n,l),s&&(s.setShape({points:u,stackedOnPoints:h}),s.stopAnimation(),mh(s,{shape:{stackedOnPoints:p}},l),a.shape.points!==s.shape.points)&&(s.shape.points=a.shape.points),[]),g=e.status,y=0;y<g.length;y++)"="===g[y].cmd&&(d=t.getItemGraphicEl(g[y].idx1))&&f.push({el:d,ptIdx:y});a.animators&&a.animators.length&&a.animators[0].during(function(){s&&s.dirtyShape();for(var t=a.shape.__points,e=0;e<f.length;e++){var n=f[e].el,i=2*f[e].ptIdx;n.x=t[i],n.y=t[1+i],n.markRedraw()}})}},kx.prototype.remove=function(t){var n=this.group,i=this._data;this._lineGroup.removeAll(),this._symbolDraw.remove(!0),i&&i.eachItemGraphicEl(function(t,e){t.__temp&&(n.remove(t),i.setItemGraphicEl(e,null))}),this._polyline=this._polygon=this._coordSys=this._points=this._stackedOnPoints=this._endLabel=this._data=null},kx.type="line";var Cx,Ix=kx;function kx(){return null!==Cx&&Cx.apply(this,arguments)||this}var Dx={average:function(t){for(var e=0,n=0,i=0;i<t.length;i++)isNaN(t[i])||(e+=t[i],n++);return 0===n?NaN:e/n},sum:function(t){for(var e=0,n=0;n<t.length;n++)e+=t[n]||0;return e},max:function(t){for(var e=-1/0,n=0;n<t.length;n++)t[n]>e&&(e=t[n]);return isFinite(e)?e:NaN},min:function(t){for(var e=1/0,n=0;n<t.length;n++)t[n]<e&&(e=t[n]);return isFinite(e)?e:NaN},nearest:function(t){return t[0]}},Ax=function(t){return Math.round(t.length/2)};__(function(t){var i;t.registerChartView(Ix),t.registerSeriesModel(V1),t.registerLayout((i=!0,{seriesType:"line",plan:Og(),reset:function(t){var h,e,c,p,d,n=t.getData(),f=t.coordinateSystem,t=t.pipelineContext,g=i||t.large;if(f)return t=B(f.dimensions,function(t){return n.mapDimension(t)}).slice(0,2),h=t.length,e=n.getCalculationInfo("stackResultDimension"),uv(n,t[0])&&(t[0]=e),uv(n,t[1])&&(t[1]=e),c=n.getStore(),p=n.getDimensionIndex(t[0]),d=n.getDimensionIndex(t[1]),h&&{progress:function(t,e){for(var n=t.end-t.start,i=g&&Nv(n*h),r=[],o=[],a=t.start,s=0;a<t.end;a++){var l,u=void 0;u=1===h?(l=c.get(p,a),f.dataToPoint(l,null,o)):(r[0]=c.get(p,a),r[1]=c.get(d,a),f.dataToPoint(r,null,o)),g?(i[s++]=u[0],i[s++]=u[1]):e.setItemLayout(a,u.slice())}g&&e.setLayout("points",i)}}}})),t.registerVisual({seriesType:"line",reset:function(t){var e=t.getData(),t=t.getModel("lineStyle").getLineStyle();t&&!t.stroke&&(t.stroke=e.getVisual("style").fill),e.setVisual("legendLineStyle",t)}}),t.registerProcessor(t.PRIORITY.PROCESSOR.STATISTIC,{seriesType:"line",reset:function(t,e,n){var i,r=t.getData(),o=t.get("sampling"),a=t.coordinateSystem,s=r.count();10<s&&"cartesian2d"===a.type&&o&&(i=a.getBaseAxis(),a=a.getOtherAxis(i),i=i.getExtent(),n=n.getDevicePixelRatio(),i=Math.abs(i[1]-i[0])*(n||1),1<(n=Math.round(s/i)))&&("lttb"===o&&t.setData(r.lttbDownSample(r.mapDimension(a.dim),1/n)),s=void 0,V(o)?s=Dx[o]:S(o)&&(s=o),s)&&t.setData(r.downSample(r.mapDimension(a.dim),1/n,s,Ax))}})});u(Ox,Px=g),Ox.type="grid",Ox.dependencies=["xAxis","yAxis"],Ox.layoutMode="box",Ox.defaultOption={show:!1,z:0,left:"10%",top:60,right:"10%",bottom:70,containLabel:!1,backgroundColor:"rgba(0,0,0,0)",borderWidth:1,borderColor:"#ccc"};var Px,Lx=Ox;function Ox(){return null!==Px&&Px.apply(this,arguments)||this}u(zx,Rx=g),zx.prototype.getCoordSysModel=function(){return this.getReferringComponents("grid",So).models[0]},zx.type="cartesian2dAxis";var Rx,Nx=zx;function zx(){return null!==Rx&&Rx.apply(this,arguments)||this}at(Nx,g_);var dc={show:!0,z:0,inverse:!1,name:"",nameLocation:"end",nameRotate:null,nameTruncate:{maxWidth:null,ellipsis:"...",placeholder:"."},nameTextStyle:{},nameGap:15,silent:!1,triggerEvent:!1,tooltip:{show:!1},axisPointer:{},axisLine:{show:!0,onZero:!0,onZeroAxisIndex:null,lineStyle:{color:"#6E7079",width:1,type:"solid"},symbol:["none","none"],symbolSize:[10,15]},axisTick:{show:!0,inside:!1,length:5,lineStyle:{width:1}},axisLabel:{show:!0,inside:!1,rotate:0,showMinLabel:null,showMaxLabel:null,margin:8,fontSize:12},splitLine:{show:!0,lineStyle:{color:["#E0E6F1"],width:1,type:"solid"}},splitArea:{show:!1,areaStyle:{color:["rgba(250,250,250,0.2)","rgba(210,219,238,0.2)"]}}},Oo=d({boundaryGap:!0,deduplication:null,splitLine:{show:!1},axisTick:{alignWithLabel:!1,interval:"auto"},axisLabel:{interval:"auto"}},dc),Iy=d({boundaryGap:[0,0],axisLine:{show:"auto"},axisTick:{show:"auto"},splitNumber:5,minorTick:{show:!1,splitNumber:5,length:3,lineStyle:{}},minorSplitLine:{show:!1,lineStyle:{color:"#F4F7FD",width:1}}},dc),Ex={category:Oo,value:Iy,time:d({splitNumber:6,axisLabel:{showMinLabel:!1,showMaxLabel:!1,rich:{primary:{fontWeight:"bold"}}},splitLine:{show:!1}},Iy),log:E({logBase:10},Iy)},Bx={value:1,category:1,time:1,log:1};function Fx(o,a,s,l){O(Bx,function(t,r){var e,n=d(d({},Ex[r],!0),l,!0),n=(u(i,e=s),i.prototype.mergeDefaultAndTheme=function(t,e){var n=bp(this),i=n?Mp(t):{};d(t,e.getTheme().get(r+"Axis")),d(t,this.getDefaultOption()),t.type=Vx(t),n&&Sp(t,i,n)},i.prototype.optionUpdated=function(){"category"===this.option.type&&(this.__ordinalMeta=gv.createByAxisModel(this))},i.prototype.getCategories=function(t){var e=this.option;if("category"===e.type)return t?e.data:this.__ordinalMeta.categories},i.prototype.getOrdinalMeta=function(){return this.__ordinalMeta},i.type=a+"Axis."+r,i.defaultOption=n,i);function i(){var t=null!==e&&e.apply(this,arguments)||this;return t.type=a+"Axis."+r,t}o.registerComponentModel(n)}),o.registerSubTypeDefaulter(a+"Axis",Vx)}function Vx(t){return t.type||(t.data?"category":"value")}function Hx(t){this.type="cartesian",this._dimList=[],this._axes={},this.name=t||""}Hx.prototype.getAxis=function(t){return this._axes[t]},Hx.prototype.getAxes=function(){return B(this._dimList,function(t){return this._axes[t]},this)},Hx.prototype.getAxesByScale=function(e){return e=e.toLowerCase(),ut(this.getAxes(),function(t){return t.scale.type===e})},Hx.prototype.addAxis=function(t){var e=t.dim;this._axes[e]=t,this._dimList.push(e)};var Gx=["x","y"];function Wx(t){return"interval"===t.type||"time"===t.type}u(Yx,Ux=Hx),Yx.prototype.calcAffineTransform=function(){this._transform=this._invTransform=null;var t,e,n,i,r=this.getAxis("x").scale,o=this.getAxis("y").scale;Wx(r)&&Wx(o)&&(r=r.getExtent(),o=o.getExtent(),i=this.dataToPoint([r[0],o[0]]),e=this.dataToPoint([r[1],o[1]]),t=r[1]-r[0],n=o[1]-o[0],t)&&n&&(t=(e[0]-i[0])/t,e=(e[1]-i[1])/n,n=i[0]-r[0]*t,r=i[1]-o[0]*e,i=this._transform=[t,0,0,e,n,r],this._invTransform=Xi([],i))},Yx.prototype.getBaseAxis=function(){return this.getAxesByScale("ordinal")[0]||this.getAxesByScale("time")[0]||this.getAxis("x")},Yx.prototype.containPoint=function(t){var e=this.getAxis("x"),n=this.getAxis("y");return e.contain(e.toLocalCoord(t[0]))&&n.contain(n.toLocalCoord(t[1]))},Yx.prototype.containData=function(t){return this.getAxis("x").containData(t[0])&&this.getAxis("y").containData(t[1])},Yx.prototype.dataToPoint=function(t,e,n){n=n||[];var i,r=t[0],o=t[1];return this._transform&&null!=r&&isFinite(r)&&null!=o&&isFinite(o)?Jt(n,t,this._transform):(t=this.getAxis("x"),i=this.getAxis("y"),n[0]=t.toGlobalCoord(t.dataToCoord(r,e)),n[1]=i.toGlobalCoord(i.dataToCoord(o,e)),n)},Yx.prototype.clampData=function(t,e){var n=this.getAxis("x").scale,i=this.getAxis("y").scale,r=n.getExtent(),o=i.getExtent(),n=n.parse(t[0]),i=i.parse(t[1]);return(e=e||[])[0]=Math.min(Math.max(Math.min(r[0],r[1]),n),Math.max(r[0],r[1])),e[1]=Math.min(Math.max(Math.min(o[0],o[1]),i),Math.max(o[0],o[1])),e},Yx.prototype.pointToData=function(t,e){var n,i,r=[];return this._invTransform?Jt(r,t,this._invTransform):(n=this.getAxis("x"),i=this.getAxis("y"),r[0]=n.coordToData(n.toLocalCoord(t[0]),e),r[1]=i.coordToData(i.toLocalCoord(t[1]),e),r)},Yx.prototype.getOtherAxis=function(t){return this.getAxis("x"===t.dim?"y":"x")},Yx.prototype.getArea=function(){var t=this.getAxis("x").getGlobalExtent(),e=this.getAxis("y").getGlobalExtent(),n=Math.min(t[0],t[1]),i=Math.min(e[0],e[1]),t=Math.max(t[0],t[1])-n,e=Math.max(e[0],e[1])-i;return new X(n,i,t,e)};var Ux,Xx=Yx;function Yx(){var t=null!==Ux&&Ux.apply(this,arguments)||this;return t.type="cartesian2d",t.dimensions=Gx,t}u(jx,qx=mc),jx.prototype.isHorizontal=function(){var t=this.position;return"top"===t||"bottom"===t},jx.prototype.getGlobalExtent=function(t){var e=this.getExtent();return e[0]=this.toGlobalCoord(e[0]),e[1]=this.toGlobalCoord(e[1]),t&&e[0]>e[1]&&e.reverse(),e},jx.prototype.pointToData=function(t,e){return this.coordToData(this.toLocalCoord(t["x"===this.dim?0:1]),e)},jx.prototype.setCategorySortInfo=function(t){if("category"!==this.type)return!1;this.model.option.categorySortInfo=t,this.scale.setSortInfo(t)};var qx,Zx=jx;function jx(t,e,n,i,r){t=qx.call(this,t,e,n)||this;return t.index=0,t.type=i||"value",t.position=r||"bottom",t}function Kx(t,e,n){n=n||{};var t=t.coordinateSystem,i=e.axis,r={},o=i.getAxesOnZeroOf()[0],a=i.position,s=o?"onZero":a,i=i.dim,t=t.getRect(),t=[t.x,t.x+t.width,t.y,t.y+t.height],l={left:0,right:1,top:0,bottom:1,onZero:2},u=e.get("offset")||0,u="x"===i?[t[2]-u,t[3]+u]:[t[0]-u,t[1]+u],h=(o&&(h=o.toGlobalCoord(o.dataToCoord(0)),u[l.onZero]=Math.max(Math.min(h,u[1]),u[0])),r.position=["y"===i?u[l[s]]:t[0],"x"===i?u[l[s]]:t[3]],r.rotation=Math.PI/2*("x"===i?0:1),r.labelDirection=r.tickDirection=r.nameDirection={top:-1,bottom:1,left:-1,right:1}[a],r.labelOffset=o?u[l[a]]-u[l.onZero]:0,e.get(["axisTick","inside"])&&(r.tickDirection=-r.tickDirection),bt(n.labelInside,e.get(["axisLabel","inside"]))&&(r.labelDirection=-r.labelDirection),e.get(["axisLabel","rotate"]));return r.labelRotate="top"===s?-h:h,r.z2=1,r}function $x(t){return"cartesian2d"===t.get("coordinateSystem")}function Qx(i){var r={xAxisModel:null,yAxisModel:null};return O(r,function(t,e){var n=e.replace(/Model$/,""),n=i.getReferringComponents(n,So).models[0];r[e]=n}),r}var Jx=Math.log;ew.prototype.getRect=function(){return this._rect},ew.prototype.update=function(t,e){var n=this._axesMap;function i(t){var d,e=ht(t),n=e.length;if(n){for(var i=[],r=n-1;0<=r;r--){var o=t[+e[r]],a=o.model,s=o.scale;vv(s)&&a.get("alignTicks")&&null==a.get("interval")?i.push(o):(l_(s,a),vv(s)&&(d=o))}i.length&&(d||l_((d=i.pop()).scale,d.model),O(i,function(t){var e=t.scale,t=t.model,n=d.scale,i=Pv.prototype,r=i.getTicks.call(n),o=i.getTicks.call(n,!0),a=r.length-1,n=i.getInterval.call(n),s=(t=s_(e,t)).extent,l=t.fixMin,t=t.fixMax,u=("log"===e.type&&(u=Jx(e.base),s=[Jx(s[0])/u,Jx(s[1])/u]),e.setExtent(s[0],s[1]),e.calcNiceExtent({splitNumber:a,fixMin:l,fixMax:t}),i.getExtent.call(e)),h=(l&&(s[0]=u[0]),t&&(s[1]=u[1]),i.getInterval.call(e)),c=s[0],p=s[1];if(l&&t)h=(p-c)/a;else if(l)for(p=s[0]+h*a;p<s[1]&&isFinite(p)&&isFinite(s[1]);)h=xv(h),p=s[0]+h*a;else if(t)for(c=s[1]-h*a;c>s[0]&&isFinite(c)&&isFinite(s[0]);)h=xv(h),c=s[1]-h*a;else{u=(h=a<e.getTicks().length-1?xv(h):h)*a;(c=Wr((p=Math.ceil(s[1]/h)*h)-u))<0&&0<=s[0]?(c=0,p=Wr(u)):0<p&&s[1]<=0&&(p=0,c=-Wr(u))}l=(r[0].value-o[0].value)/n,t=(r[a].value-o[a].value)/n,i.setExtent.call(e,c+h*l,p+h*t),i.setInterval.call(e,h),(l||t)&&i.setNiceExtent.call(e,c+h,p-h)}))}}this._updateScale(t,this.model),i(n.x),i(n.y);var r={};O(n.x,function(t){iw(n,"y",t,r)}),O(n.y,function(t){iw(n,"x",t,r)}),this.resize(this.model,e)},ew.prototype.resize=function(t,e,n){var i=t.getBoxLayoutParams(),n=!n&&t.get("containLabel"),a=wp(i,{width:e.getWidth(),height:e.getHeight()}),r=(this._rect=a,this._axesList);function o(){O(r,function(t){var e,n,i=t.isHorizontal(),r=i?[0,a.width]:[0,a.height],o=t.inverse?1:0;t.setExtent(r[o],r[1-o]),r=t,e=i?a.x:a.y,o=r.getExtent(),n=o[0]+o[1],r.toGlobalCoord="x"===r.dim?function(t){return t+e}:function(t){return n-t+e},r.toLocalCoord="x"===r.dim?function(t){return t-e}:function(t){return n-t+e}})}o(),n&&(O(r,function(t){var e,n,i;t.model.get(["axisLabel","inside"])||(e=p_(t))&&(n=t.isHorizontal()?"height":"width",i=t.model.get(["axisLabel","margin"]),a[n]-=e[n]+i,"top"===t.position?a.y+=e.height+i:"left"===t.position&&(a.x+=e.width+i))}),o()),O(this._coordsList,function(t){t.calcAffineTransform()})},ew.prototype.getAxis=function(t,e){t=this._axesMap[t];if(null!=t)return t[e||0]},ew.prototype.getAxes=function(){return this._axesList.slice()},ew.prototype.getCartesian=function(t,e){if(null!=t&&null!=e)return this._coordsMap["x"+t+"y"+e];R(t)&&(e=t.yAxisIndex,t=t.xAxisIndex);for(var n=0,i=this._coordsList;n<i.length;n++)if(i[n].getAxis("x").index===t||i[n].getAxis("y").index===e)return i[n]},ew.prototype.getCartesians=function(){return this._coordsList.slice()},ew.prototype.convertToPixel=function(t,e,n){e=this._findConvertTarget(e);return e.cartesian?e.cartesian.dataToPoint(n):e.axis?e.axis.toGlobalCoord(e.axis.dataToCoord(n)):null},ew.prototype.convertFromPixel=function(t,e,n){e=this._findConvertTarget(e);return e.cartesian?e.cartesian.pointToData(n):e.axis?e.axis.coordToData(e.axis.toLocalCoord(n)):null},ew.prototype._findConvertTarget=function(t){var e,n,i=t.seriesModel,r=t.xAxisModel||i&&i.getReferringComponents("xAxis",So).models[0],o=t.yAxisModel||i&&i.getReferringComponents("yAxis",So).models[0],t=t.gridModel,a=this._coordsList;return i?I(a,e=i.coordinateSystem)<0&&(e=null):r&&o?e=this.getCartesian(r.componentIndex,o.componentIndex):r?n=this.getAxis("x",r.componentIndex):o?n=this.getAxis("y",o.componentIndex):t&&t.coordinateSystem===this&&(e=this._coordsList[0]),{cartesian:e,axis:n}},ew.prototype.containPoint=function(t){var e=this._coordsList[0];if(e)return e.containPoint(t)},ew.prototype._initCartesian=function(o,t,e){var a=this,s=this,l={left:!1,right:!1,top:!1,bottom:!1},u={x:{},y:{}},h={x:0,y:0};function n(r){return function(t,e){var n,i;nw(t,o)&&(n=t.get("position"),"x"===r?"top"!==n&&"bottom"!==n&&(n=l.bottom?"top":"bottom"):"left"!==n&&"right"!==n&&(n=l.left?"right":"left"),l[n]=!0,i="category"===(n=new Zx(r,u_(t),[0,0],t.get("type"),n)).type,n.onBand=i&&t.get("boundaryGap"),n.inverse=t.get("inverse"),(t.axis=n).model=t,n.grid=s,n.index=e,s._axesList.push(n),u[r][e]=n,h[r]++)}}t.eachComponent("xAxis",n("x"),this),t.eachComponent("yAxis",n("y"),this),h.x&&h.y?O((this._axesMap=u).x,function(i,r){O(u.y,function(t,e){var e="x"+r+"y"+e,n=new Xx(e);n.master=a,n.model=o,a._coordsMap[e]=n,a._coordsList.push(n),n.addAxis(i),n.addAxis(t)})}):(this._axesMap={},this._axesList=[])},ew.prototype._updateScale=function(t,i){function r(e,n){var i,t,r;O((i=e,t=n.dim,r={},O(i.mapDimensionsAll(t),function(t){r[hv(i,t)]=!0}),ht(r)),function(t){n.scale.unionExtentFromData(e,t)})}O(this._axesList,function(t){var e;t.scale.setExtent(1/0,-1/0),"category"===t.type&&(e=t.model.get("categorySortInfo"),t.scale.setSortInfo(e))}),t.eachSeries(function(t){var e,n;$x(t)&&(n=(e=Qx(t)).xAxisModel,e=e.yAxisModel,nw(n,i))&&nw(e,i)&&(n=this.getCartesian(n.componentIndex,e.componentIndex),e=t.getData(),t=n.getAxis("x"),n=n.getAxis("y"),r(e,t),r(e,n))},this)},ew.prototype.getTooltipAxes=function(n){var i=[],r=[];return O(this.getCartesians(),function(t){var e=null!=n&&"auto"!==n?t.getAxis(n):t.getBaseAxis(),t=t.getOtherAxis(e);I(i,e)<0&&i.push(e),I(r,t)<0&&r.push(t)}),{baseAxes:i,otherAxes:r}},ew.create=function(i,r){var o=[];return i.eachComponent("grid",function(t,e){var n=new ew(t,i,r);n.name="grid_"+e,n.resize(t,r,!0),t.coordinateSystem=n,o.push(n)}),i.eachSeries(function(t){var e,n,i;$x(t)&&(e=(n=Qx(t)).xAxisModel,n=n.yAxisModel,i=e.getCoordSysModel().coordinateSystem,t.coordinateSystem=i.getCartesian(e.componentIndex,n.componentIndex))}),o},ew.dimensions=Gx;var tw=ew;function ew(t,e,n){this.type="grid",this._coordsMap={},this._coordsList=[],this._axesMap={},this._axesList=[],this.axisPointerEnabled=!0,this.dimensions=Gx,this._initCartesian(t,e,n),this.model=t}function nw(t,e){return t.getCoordSysModel()===e}function iw(t,e,n,i){n.getAxesOnZeroOf=function(){return r?[r]:[]};var r,o=t[e],t=n.model,e=t.get(["axisLine","onZero"]),n=t.get(["axisLine","onZeroAxisIndex"]);if(e){if(null!=n)rw(o[n])&&(r=o[n]);else for(var a in o)if(o.hasOwnProperty(a)&&rw(o[a])&&!i[s(o[a])]){r=o[a];break}r&&(i[s(r)]=!0)}function s(t){return t.dim+"_"+t.index}}function rw(t){return t&&"category"!==t.type&&"time"!==t.type&&(e=(t=(t=t).scale.getExtent())[0],t=t[1],!(0<e&&0<t||e<0&&t<0));var e}var ow=Math.PI,aw=(sw.prototype.hasBuilder=function(t){return!!lw[t]},sw.prototype.add=function(t){lw[t](this.opt,this.axisModel,this.group,this._transformGroup)},sw.prototype.getGroup=function(){return this.group},sw.innerTextLayout=function(t,e,n){var i,e=qr(e-t),t=Zr(e)?(i=0<n?"top":"bottom","center"):Zr(e-ow)?(i=0<n?"bottom":"top","center"):(i="middle",0<e&&e<ow?0<n?"right":"left":0<n?"left":"right");return{rotation:e,textAlign:t,textVerticalAlign:i}},sw.makeAxisEventDataBase=function(t){var e={componentType:t.mainType,componentIndex:t.componentIndex};return e[t.mainType+"Index"]=t.componentIndex,e},sw.isLabelSilent=function(t){var e=t.get("tooltip");return t.get("silent")||!(t.get("triggerEvent")||e&&e.show)},sw);function sw(t,e){this.group=new Pr,this.opt=e,this.axisModel=t,E(e,{labelOffset:0,nameDirection:1,tickDirection:1,labelDirection:1,silent:!0,handleAutoShown:function(){return!0}});t=new Pr({x:e.position[0],y:e.position[1],rotation:e.rotation});t.updateTransform(),this._transformGroup=t}var lw={axisLine:function(i,t,r,e){var o,a,s,l,u,n=t.get(["axisLine","show"]);(n="auto"===n&&i.handleAutoShown?i.handleAutoShown("axisLine"):n)&&(n=t.axis.getExtent(),e=e.transform,o=[n[0],0],n=[n[1],0],e&&(Jt(o,o,e),Jt(n,n,e)),a=L({lineCap:"round"},t.getModel(["axisLine","lineStyle"]).getLineStyle()),(e=new Bu({subPixelOptimize:!0,shape:{x1:o[0],y1:o[1],x2:n[0],y2:n[1]},style:a,strokeContainThreshold:i.strokeContainThreshold||5,silent:!0,z2:1})).anid="line",r.add(e),null!=(s=t.get(["axisLine","symbol"])))&&(e=t.get(["axisLine","symbolSize"]),V(s)&&(s=[s,s]),(V(e)||ft(e))&&(e=[e,e]),t=By(t.get(["axisLine","symbolOffset"])||0,e),l=e[0],u=e[1],O([{rotate:i.rotation+Math.PI/2,offset:t[0],r:0},{rotate:i.rotation-Math.PI/2,offset:t[1],r:Math.sqrt((o[0]-n[0])*(o[0]-n[0])+(o[1]-n[1])*(o[1]-n[1]))}],function(t,e){var n;"none"!==s[e]&&null!=s[e]&&(e=Ey(s[e],-l/2,-u/2,l,u,a.stroke,!0),n=t.r+t.offset,e.attr({rotation:t.rotate,x:o[0]+n*Math.cos(i.rotation),y:o[1]-n*Math.sin(i.rotation),silent:!0,z2:11}),r.add(e))}))},axisTickLabel:function(t,e,n,i){var r,o,a,s,l,u=function(t,e,n,i){var r=n.axis,o=n.getModel("axisTick"),a=o.get("show");"auto"===a&&i.handleAutoShown&&(a=i.handleAutoShown("axisTick"));if(a&&!r.scale.isBlank()){for(var a=o.getModel("lineStyle"),i=i.tickDirection*o.get("length"),s=pw(r.getTicksCoords(),e.transform,i,E(a.getLineStyle(),{stroke:n.get(["axisLine","lineStyle","color"])}),"ticks"),l=0;l<s.length;l++)t.add(s[l]);return s}}(n,i,e,t),h=function(l,u,h,c){var p,d,t,f,g,y,m,v,_=h.axis,e=bt(c.axisLabelShow,h.get(["axisLabel","show"]));if(e&&!_.scale.isBlank())return p=h.getModel("axisLabel"),d=p.get("margin"),e=_.getViewLabels(),t=(bt(c.labelRotate,p.get("rotate"))||0)*ow/180,f=aw.innerTextLayout(c.rotation,t,c.labelDirection),g=h.getCategories&&h.getCategories(!0),y=[],m=aw.isLabelSilent(h),v=h.get("triggerEvent"),O(e,function(t,e){var n="ordinal"===_.scale.type?_.scale.getRawOrdinalNumber(t.tickValue):t.tickValue,i=t.formattedLabel,r=t.rawLabel,o=p,a=(o=g&&g[n]&&R(a=g[n])&&a.textStyle?new xc(a.textStyle,p,h.ecModel):o).getTextColor()||h.get(["axisLine","lineStyle","color"]),s=_.dataToCoord(n),s=new bs({x:s,y:c.labelOffset+c.labelDirection*d,rotation:f.rotation,silent:m,z2:10+(t.level||0),style:Jh(o,{text:i,align:o.getShallow("align",!0)||f.textAlign,verticalAlign:o.getShallow("verticalAlign",!0)||o.getShallow("baseline",!0)||f.textVerticalAlign,fill:S(a)?a("category"===_.type?r:"value"===_.type?n+"":n,e):a})});s.anid="label_"+n,v&&((t=aw.makeAxisEventDataBase(h)).targetType="axisLabel",t.value=r,k(s).eventData=t),u.add(s),s.updateTransform(),y.push(s),l.add(s),s.decomposeTransform()}),y}(n,i,e,t),c=(o=h,u=u,f_((r=e).axis)||(d=r.get(["axisLabel","showMinLabel"]),r=r.get(["axisLabel","showMaxLabel"]),u=u||[],y=(o=o||[])[0],f=o[1],a=o[o.length-1],o=o[o.length-2],s=u[0],g=u[1],l=u[u.length-1],u=u[u.length-2],!1===d?(uw(y),uw(s)):hw(y,f)&&(d?(uw(f),uw(g)):(uw(y),uw(s))),!1===r?(uw(a),uw(l)):hw(o,a)&&(r?(uw(o),uw(u)):(uw(a),uw(l)))),n),p=i,d=e,f=t.tickDirection,g=d.axis,y=d.getModel("minorTick");if(y.get("show")&&!g.scale.isBlank()){var m=g.getMinorTicksCoords();if(m.length)for(var g=y.getModel("lineStyle"),v=f*y.get("length"),_=E(g.getLineStyle(),E(d.getModel("axisTick").getLineStyle(),{stroke:d.get(["axisLine","lineStyle","color"])})),x=0;x<m.length;x++)for(var w=pw(m[x],p.transform,v,_,"minorticks_"+x),b=0;b<w.length;b++)c.add(w[b])}e.get(["axisLabel","hideOverlap"])&&f1(p1(B(h,function(t){return{label:t,priority:t.z2,defaultAttr:{ignore:t.ignore}}})))},axisName:function(t,e,n,i){var r,o,a,s,l,u,h,c,p=bt(t.axisName,e.get("name"));p&&(c=e.get("nameLocation"),l=t.nameDirection,r=e.getModel("nameTextStyle"),u=e.get("nameGap")||0,o=(h=e.axis.getExtent())[0]>h[1]?-1:1,o=["start"===c?h[0]-o*u:"end"===c?h[1]+o*u:(h[0]+h[1])/2,cw(c)?t.labelOffset+l*u:0],null!=(u=e.get("nameRotate"))&&(u=u*ow/180),cw(c)?a=aw.innerTextLayout(t.rotation,null!=u?u:t.rotation,l):(a=function(t,e,n,i){var r,n=qr(n-t),t=i[0]>i[1],i="start"===e&&!t||"start"!==e&&t;e=Zr(n-ow/2)?(r=i?"bottom":"top","center"):Zr(n-1.5*ow)?(r=i?"top":"bottom","center"):(r="middle",n<1.5*ow&&ow/2<n?i?"left":"right":i?"right":"left");return{rotation:n,textAlign:e,textVerticalAlign:r}}(t.rotation,c,u||0,h),null!=(s=t.axisNameAvailableWidth)&&(s=Math.abs(s/Math.sin(a.rotation)),isFinite(s)||(s=null))),l=r.getFont(),u=(c=e.get("nameTruncate",!0)||{}).ellipsis,h=bt(t.nameTruncateMaxWidth,c.maxWidth,s),Uh({el:t=new bs({x:o[0],y:o[1],rotation:a.rotation,silent:aw.isLabelSilent(e),style:Jh(r,{text:p,font:l,overflow:"truncate",width:h,ellipsis:u,fill:r.getTextColor()||e.get(["axisLine","lineStyle","color"]),align:r.get("align")||a.textAlign,verticalAlign:r.get("verticalAlign")||a.textVerticalAlign}),z2:1}),componentModel:e,itemName:p}),t.__fullText=p,t.anid="name",e.get("triggerEvent")&&((c=aw.makeAxisEventDataBase(e)).targetType="axisName",c.name=p,k(t).eventData=c),i.add(t),t.updateTransform(),n.add(t),t.decomposeTransform())}};function uw(t){t&&(t.ignore=!0)}function hw(t,e){var n,i=t&&t.getBoundingRect().clone(),r=e&&e.getBoundingRect().clone();if(i&&r)return Wi(n=Fi([]),n,-t.rotation),i.applyTransform(Hi([],n,t.getLocalTransform())),r.applyTransform(Hi([],n,e.getLocalTransform())),i.intersect(r)}function cw(t){return"middle"===t||"center"===t}function pw(t,e,n,i,r){for(var o=[],a=[],s=[],l=0;l<t.length;l++){var u=t[l].coord,u=(a[0]=u,s[a[1]=0]=u,s[1]=n,e&&(Jt(a,a,e),Jt(s,s,e)),new Bu({subPixelOptimize:!0,shape:{x1:a[0],y1:a[1],x2:s[0],y2:s[1]},style:i,z2:2,autoBatch:!0,silent:!0}));u.anid=r+"_"+t[l].tickValue,o.push(u)}return o}function dw(t,e){var u,h,r,c,p,d,o,n={axesInfo:{},seriesInvolved:!1,coordSysAxesInfo:{},coordSysMap:{}};return u=n,e=e,r=(h=t).getComponent("tooltip"),c=h.getComponent("axisPointer"),p=c.get("link",!0)||[],d=[],O(e.getCoordinateSystems(),function(a){var s,l,t,e,n;function i(t,e,n){var i,r=n.model.getModel("axisPointer",c),o=r.get("show");o&&("auto"!==o||t||yw(r))&&(null==e&&(e=r.get("triggerTooltip")),o=(r=t?function(t,e,n,i,r,o){var a=e.getModel("axisPointer"),s={};O(["type","snap","lineStyle","shadowStyle","label","animation","animationDurationUpdate","animationEasingUpdate","z"],function(t){s[t]=y(a.get(t))}),s.snap="category"!==t.type&&!!o,"cross"===a.get("type")&&(s.type="line");e=s.label||(s.label={});null==e.show&&(e.show=!1),"cross"===r&&(r=a.get(["label","show"]),e.show=null==r||r,o||(r=s.lineStyle=a.get("crossStyle"))&&E(e,r.textStyle));return t.model.getModel("axisPointer",new xc(s,n,i))}(n,l,c,h,t,e):r).get("snap"),t=mw(n.model),i=e||o||"category"===n.type,e=u.axesInfo[t]={key:t,axis:n,coordSys:a,axisPointerModel:r,triggerTooltip:e,involveSeries:i,snap:o,useHandle:yw(r),seriesModels:[],linkGroup:null},s[t]=e,u.seriesInvolved=u.seriesInvolved||i,null!=(o=function(t,e){for(var n=e.model,i=e.dim,r=0;r<t.length;r++){var o=t[r]||{};if(fw(o[i+"AxisId"],n.id)||fw(o[i+"AxisIndex"],n.componentIndex)||fw(o[i+"AxisName"],n.name))return r}}(p,n)))&&((r=d[o]||(d[o]={axesInfo:{}})).axesInfo[t]=e,r.mapper=p[o].mapper,e.linkGroup=r)}a.axisPointerEnabled&&(t=mw(a.model),s=u.coordSysAxesInfo[t]={},l=(u.coordSysMap[t]=a).model.getModel("tooltip",r),O(a.getAxes(),pt(i,!1,null)),a.getTooltipAxes)&&r&&l.get("show")&&(t="axis"===l.get("trigger"),e="cross"===l.get(["axisPointer","type"]),n=a.getTooltipAxes(l.get(["axisPointer","axis"])),(t||e)&&O(n.baseAxes,pt(i,!e||"cross",t)),e)&&O(n.otherAxes,pt(i,"cross",!1))}),n.seriesInvolved&&(o=n,t.eachSeries(function(n){var i=n.coordinateSystem,t=n.get(["tooltip","trigger"],!0),e=n.get(["tooltip","show"],!0);i&&"none"!==t&&!1!==t&&"item"!==t&&!1!==e&&!1!==n.get(["axisPointer","show"],!0)&&O(o.coordSysAxesInfo[mw(i.model)],function(t){var e=t.axis;i.getAxis(e.dim)===e&&(t.seriesModels.push(n),null==t.seriesDataCount&&(t.seriesDataCount=0),t.seriesDataCount+=n.getData().count())})})),n}function fw(t,e){return"all"===t||F(t)&&0<=I(t,e)||t===e}function gw(t){var e=(t.ecModel.getComponent("axisPointer")||{}).coordSysAxesInfo;return e&&e.axesInfo[mw(t)]}function yw(t){return!!t.get(["handle","show"])}function mw(t){return t.type+"||"+t.id}var vw,_w={},xw=(u(ww,vw=Pg),ww.prototype.render=function(t,e,n,i){var r,o,a,s,l,u;this.axisPointerClass&&(r=gw(r=t))&&(l=r.axisPointerModel,o=r.axis.scale,a=l.option,u=l.get("status"),null!=(s=l.get("value"))&&(s=o.parse(s)),l=yw(l),null==u&&(a.status=l?"show":"hide"),(u=o.getExtent().slice())[0]>u[1]&&u.reverse(),(s=null==s||s>u[1]?u[1]:s)<u[0]&&(s=u[0]),a.value=s,l)&&(a.status=r.axis.scale.isBlank()?"hide":"show"),vw.prototype.render.apply(this,arguments),this._doUpdateAxisPointerClass(t,n,!0)},ww.prototype.updateAxisPointer=function(t,e,n,i){this._doUpdateAxisPointerClass(t,n,!1)},ww.prototype.remove=function(t,e){var n=this._axisPointer;n&&n.remove(e)},ww.prototype.dispose=function(t,e){this._disposeAxisPointer(e),vw.prototype.dispose.apply(this,arguments)},ww.prototype._doUpdateAxisPointerClass=function(t,e,n){var i,r=ww.getAxisPointerClass(this.axisPointerClass);r&&((i=(i=gw(i=t))&&i.axisPointerModel)?(this._axisPointer||(this._axisPointer=new r)).render(t,i,e,n):this._disposeAxisPointer(e))},ww.prototype._disposeAxisPointer=function(t){this._axisPointer&&this._axisPointer.dispose(t),this._axisPointer=null},ww.registerAxisPointerClass=function(t,e){_w[t]=e},ww.getAxisPointerClass=function(t){return t&&_w[t]},ww.type="axis",ww);function ww(){var t=null!==vw&&vw.apply(this,arguments)||this;return t.type=ww.type,t}var bw=_o();var Sw,Mw=["axisLine","axisTickLabel","axisName"],Tw=["splitArea","splitLine","minorSplitLine"],Ly=(u(Cw,Sw=xw),Cw.prototype.render=function(i,t,e,n){this.group.removeAll();var r,o,a=this._axisGroup;this._axisGroup=new Pr,this.group.add(this._axisGroup),i.get("show")&&(o=Kx(r=i.getCoordSysModel(),i),o=new aw(i,L({handleAutoShown:function(t){for(var e=r.coordinateSystem.getCartesians(),n=0;n<e.length;n++)if(vv(e[n].getOtherAxis(i.axis).scale))return!0;return!1}},o)),O(Mw,o.add,o),this._axisGroup.add(o.getGroup()),O(Tw,function(t){i.get([t,"show"])&&kw[t](this,this._axisGroup,i,r)},this),n&&"changeAxisOrder"===n.type&&n.isInitSort||Fh(a,this._axisGroup,i),Sw.prototype.render.call(this,i,t,e,n))},Cw.prototype.remove=function(){bw(this).splitAreaColors=null},Cw.type="cartesianAxis",Cw);function Cw(){var t=null!==Sw&&Sw.apply(this,arguments)||this;return t.type=Cw.type,t.axisPointerClass="CartesianAxisPointer",t}var Iw,kw={splitLine:function(t,e,n,i){var r=n.axis;if(!r.scale.isBlank())for(var n=n.getModel("splitLine"),o=n.getModel("lineStyle"),a=F(a=o.get("color"))?a:[a],s=i.coordinateSystem.getRect(),l=r.isHorizontal(),u=0,h=r.getTicksCoords({tickModel:n}),c=[],p=[],d=o.getLineStyle(),f=0;f<h.length;f++){var g=r.toGlobalCoord(h[f].coord),g=(l?(c[0]=g,c[1]=s.y,p[0]=g,p[1]=s.y+s.height):(c[0]=s.x,c[1]=g,p[0]=s.x+s.width,p[1]=g),u++%a.length),y=h[f].tickValue;e.add(new Bu({anid:null!=y?"line_"+h[f].tickValue:null,subPixelOptimize:!0,autoBatch:!0,shape:{x1:c[0],y1:c[1],x2:p[0],y2:p[1]},style:E({stroke:a[g]},d),silent:!0}))}},minorSplitLine:function(t,e,n,i){var r=n.axis,n=n.getModel("minorSplitLine").getModel("lineStyle"),o=i.coordinateSystem.getRect(),a=r.isHorizontal(),s=r.getMinorTicksCoords();if(s.length)for(var l=[],u=[],h=n.getLineStyle(),c=0;c<s.length;c++)for(var p=0;p<s[c].length;p++){var d=r.toGlobalCoord(s[c][p].coord);a?(l[0]=d,l[1]=o.y,u[0]=d,u[1]=o.y+o.height):(l[0]=o.x,l[1]=d,u[0]=o.x+o.width,u[1]=d),e.add(new Bu({anid:"minor_line_"+s[c][p].tickValue,subPixelOptimize:!0,autoBatch:!0,shape:{x1:l[0],y1:l[1],x2:u[0],y2:u[1]},style:h,silent:!0}))}},splitArea:function(t,e,n,i){var r=e,e=i,o=(i=n).axis;if(!o.scale.isBlank()){var i=i.getModel("splitArea"),n=i.getModel("areaStyle"),a=n.get("color"),s=e.coordinateSystem.getRect(),l=o.getTicksCoords({tickModel:i,clamp:!0});if(l.length){var u=a.length,h=bw(t).splitAreaColors,c=z(),p=0;if(h)for(var d=0;d<l.length;d++){var f=h.get(l[d].tickValue);if(null!=f){p=(f+(u-1)*d)%u;break}}for(var g=o.toGlobalCoord(l[0].coord),y=n.getAreaStyle(),a=F(a)?a:[a],d=1;d<l.length;d++){var m=o.toGlobalCoord(l[d].coord),v=void 0,_=void 0,x=void 0,w=void 0,g=o.isHorizontal()?(v=g,_=s.y,w=s.height,v+(x=m-v)):(v=s.x,_=g,x=s.width,_+(w=m-_)),m=l[d-1].tickValue;null!=m&&c.set(m,p),r.add(new ms({anid:null!=m?"area_"+m:null,shape:{x:v,y:_,width:x,height:w},style:E({fill:a[p]},y),autoBatch:!0,silent:!0})),p=(p+1)%u}bw(t).splitAreaColors=c}}}},Dw=(u(Aw,Iw=Ly),Aw.type="xAxis",Aw);function Aw(){var t=null!==Iw&&Iw.apply(this,arguments)||this;return t.type=Aw.type,t}u(Ow,Pw=Ly),Ow.type="yAxis";var Pw,Lw=Ow;function Ow(){var t=null!==Pw&&Pw.apply(this,arguments)||this;return t.type=Dw.type,t}u(zw,Rw=Pg),zw.prototype.render=function(t,e){this.group.removeAll(),t.get("show")&&this.group.add(new ms({shape:t.coordinateSystem.getRect(),style:E({fill:t.get("backgroundColor")},t.getItemStyle()),silent:!0,z2:-1}))},zw.type="grid";var Rw,Nw=zw;function zw(){var t=null!==Rw&&Rw.apply(this,arguments)||this;return t.type="grid",t}var Ew={offset:0};__(function(t){t.registerComponentView(Nw),t.registerComponentModel(Lx),t.registerCoordinateSystem("cartesian2d",tw),Fx(t,"x",Nx,Ew),Fx(t,"y",Nx,Ew),t.registerComponentView(Dw),t.registerComponentView(Lw),t.registerPreprocessor(function(t){t.xAxis&&t.yAxis&&!t.grid&&(t.grid={})})});u(Vw,Bw=g),Vw.prototype.init=function(t,e,n){this.mergeDefaultAndTheme(t,n),t.selected=t.selected||{},this._updateSelector(t)},Vw.prototype.mergeOption=function(t,e){Bw.prototype.mergeOption.call(this,t,e),this._updateSelector(t)},Vw.prototype._updateSelector=function(t){var n=t.selector,i=this.ecModel;F(n=!0===n?t.selector=["all","inverse"]:n)&&O(n,function(t,e){V(t)&&(t={type:t}),n[e]=d(t,(e=i,"all"===(t=t.type)?{type:"all",title:e.getLocaleModel().get(["legend","selector","all"])}:"inverse"===t?{type:"inverse",title:e.getLocaleModel().get(["legend","selector","inverse"])}:void 0))})},Vw.prototype.optionUpdated=function(){this._updateData(this.ecModel);var t=this._data;if(t[0]&&"single"===this.get("selectedMode")){for(var e=!1,n=0;n<t.length;n++){var i=t[n].get("name");if(this.isSelected(i)){this.select(i),e=!0;break}}e||this.select(t[0].get("name"))}},Vw.prototype._updateData=function(i){var r=[],o=[];i.eachRawSeries(function(t){var e,n=t.name;o.push(n),t.legendVisualProvider&&(n=t.legendVisualProvider.getAllNames(),i.isSeriesFiltered(t)||(o=o.concat(n)),n.length)?r=r.concat(n):e=!0,e&&go(t)&&r.push(t.name)}),this._availableNames=o;var t=B(this.get("data")||r,function(t){return(V(t)||ft(t))&&(t={name:t}),new xc(t,this,this.ecModel)},this);this._data=t},Vw.prototype.getData=function(){return this._data},Vw.prototype.select=function(t){var e=this.option.selected;"single"===this.get("selectedMode")&&O(this._data,function(t){e[t.get("name")]=!1}),e[t]=!0},Vw.prototype.unSelect=function(t){"single"!==this.get("selectedMode")&&(this.option.selected[t]=!1)},Vw.prototype.toggleSelected=function(t){var e=this.option.selected;e.hasOwnProperty(t)||(e[t]=!0),this[e[t]?"unSelect":"select"](t)},Vw.prototype.allSelect=function(){var t=this._data,e=this.option.selected;O(t,function(t){e[t.get("name",!0)]=!0})},Vw.prototype.inverseSelect=function(){var t=this._data,e=this.option.selected;O(t,function(t){t=t.get("name",!0);e.hasOwnProperty(t)||(e[t]=!0),e[t]=!e[t]})},Vw.prototype.isSelected=function(t){var e=this.option.selected;return!(e.hasOwnProperty(t)&&!e[t])&&0<=I(this._availableNames,t)},Vw.prototype.getOrient=function(){return"vertical"===this.get("orient")?{index:1,name:"vertical"}:{index:0,name:"horizontal"}},Vw.type="legend.plain",Vw.dependencies=["series"],Vw.defaultOption={z:4,show:!0,orient:"horizontal",left:"center",top:0,align:"auto",backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderRadius:0,borderWidth:0,padding:5,itemGap:10,itemWidth:25,itemHeight:14,symbolRotate:"inherit",symbolKeepAspect:!0,inactiveColor:"#ccc",inactiveBorderColor:"#ccc",inactiveBorderWidth:"auto",itemStyle:{color:"inherit",opacity:"inherit",borderColor:"inherit",borderWidth:"auto",borderCap:"inherit",borderJoin:"inherit",borderDashOffset:"inherit",borderMiterLimit:"inherit"},lineStyle:{width:"auto",color:"inherit",inactiveColor:"#ccc",inactiveWidth:2,opacity:"inherit",type:"inherit",cap:"inherit",join:"inherit",dashOffset:"inherit",miterLimit:"inherit"},textStyle:{color:"#333"},selectedMode:!0,selector:!1,selectorLabel:{show:!0,borderRadius:10,padding:[3,5,3,5],fontSize:12,fontFamily:"sans-serif",color:"#666",borderWidth:1,borderColor:"#666"},emphasis:{selectorLabel:{show:!0,color:"#eee",backgroundColor:"#666"}},selectorPosition:"auto",selectorItemGap:7,selectorButtonGap:10,tooltip:{show:!1}};var Bw,Fw=Vw;function Vw(){var t=null!==Bw&&Bw.apply(this,arguments)||this;return t.type=Vw.type,t.layoutMode={type:"box",ignoreSize:!0},t}var Hw,Gw=pt,Ww=O,Uw=Pr,Xw=(u(Yw,Hw=Pg),Yw.prototype.init=function(){this.group.add(this._contentGroup=new Uw),this.group.add(this._selectorGroup=new Uw),this._isFirstRender=!0},Yw.prototype.getContentGroup=function(){return this._contentGroup},Yw.prototype.getSelectorGroup=function(){return this._selectorGroup},Yw.prototype.render=function(t,e,n){var i,r,o,a,s,l=this._isFirstRender;this._isFirstRender=!1,this.resetInner(),t.get("show",!0)&&(r=t.get("align"),i=t.get("orient"),r&&"auto"!==r||(r="right"===t.get("left")&&"vertical"===i?"right":"left"),a=t.get("selector",!0),s=t.get("selectorPosition",!0),this.renderInner(r,t,e,n,a,i,s=!a||s&&"auto"!==s?s:"horizontal"===i?"end":"start"),o=wp(e=t.getBoxLayoutParams(),i={width:n.getWidth(),height:n.getHeight()},n=t.get("padding")),o=wp(E({width:(r=this.layoutInner(t,r,o,l,a,s)).width,height:r.height},e),i,n),this.group.x=o.x-r.x,this.group.y=o.y-r.y,this.group.markRedraw(),this.group.add(this._backgroundEl=(l=r,s=ap((a=t).get("padding")),(e=a.getItemStyle(["color","opacity"])).fill=a.get("backgroundColor"),l=new ms({shape:{x:l.x-s[3],y:l.y-s[0],width:l.width+s[1]+s[3],height:l.height+s[0]+s[2],r:a.get("borderRadius")},style:e,silent:!0,z2:-1}))))},Yw.prototype.resetInner=function(){this.getContentGroup().removeAll(),this._backgroundEl&&this.group.remove(this._backgroundEl),this.getSelectorGroup().removeAll()},Yw.prototype.renderInner=function(s,l,u,h,t,e,n){var c=this.getContentGroup(),p=z(),d=l.get("selectedMode"),f=[];u.eachRawSeries(function(t){t.get("legendHoverLink")||f.push(t.id)}),Ww(l.getData(),function(r,o){var t,e,n,i,a=r.get("name");this.newlineDisabled||""!==a&&"\n"!==a?(t=u.getSeriesByName(a)[0],p.get(a)||(t?(e=(i=t.getData()).getVisual("legendLineStyle")||{},n=i.getVisual("legendIcon"),i=i.getVisual("style"),this._createItem(t,a,o,r,l,s,e,i,n,d).on("click",Gw(qw,a,null,h,f)).on("mouseover",Gw(jw,t.name,null,h,f)).on("mouseout",Gw(Kw,t.name,null,h,f)),p.set(a,!0)):u.eachRawSeries(function(t){var e,n,i;p.get(a)||t.legendVisualProvider&&(n=t.legendVisualProvider).containName(a)&&(i=n.indexOfName(a),e=n.getItemVisual(i,"style"),n=n.getItemVisual(i,"legendIcon"),(i=Wn(e.fill))&&0===i[3]&&(i[3]=.2,e=L(L({},e),{fill:Zn(i,"rgba")})),this._createItem(t,a,o,r,l,s,{},e,n,d).on("click",Gw(qw,null,a,h,f)).on("mouseover",Gw(jw,null,a,h,f)).on("mouseout",Gw(Kw,null,a,h,f)),p.set(a,!0))},this))):((e=new Uw).newline=!0,c.add(e))},this),t&&this._createSelector(t,l,h,e,n)},Yw.prototype._createSelector=function(t,i,r,e,n){var o=this.getSelectorGroup();Ww(t,function(t){var e=t.type,n=new bs({style:{x:0,y:0,align:"center",verticalAlign:"middle"},onclick:function(){r.dispatchAction({type:"all"===e?"legendAllSelect":"legendInverseSelect"})}});o.add(n),$h(n,{normal:i.getModel("selectorLabel"),emphasis:i.getModel(["emphasis","selectorLabel"])},{defaultText:t.title}),Sl(n)})},Yw.prototype._createItem=function(t,e,n,i,r,o,a,s,l,u){var h=t.visualDrawType,c=r.get("itemWidth"),p=r.get("itemHeight"),d=r.isSelected(e),f=i.get("symbolRotate"),g=i.get("symbolKeepAspect"),y=i.get("icon"),a=function(t,e,n,i,r,o){function a(n,i){"auto"===n.lineWidth&&(n.lineWidth=0<i.lineWidth?2:0),Ww(n,function(t,e){"inherit"===n[e]&&(n[e]=i[e])})}var s=e.getModel("itemStyle").getItemStyle(),t=0===t.lastIndexOf("empty",0)?"fill":"stroke";s.decal=i.decal,"inherit"===s.fill&&(s.fill=i[r]);"inherit"===s.stroke&&(s.stroke=i[t]);"inherit"===s.opacity&&(s.opacity=("fill"===r?i:n).opacity);a(s,i);var r=e.getModel("lineStyle"),l=r.getLineStyle();a(l,n),"auto"===s.fill&&(s.fill=i.fill),"auto"===s.stroke&&(s.stroke=i.fill),"auto"===l.stroke&&(l.stroke=i.fill),o||(n=e.get("inactiveBorderWidth"),o=s[t],s.lineWidth="auto"===n?0<i.lineWidth&&o?2:0:s.lineWidth,s.fill=e.get("inactiveColor"),s.stroke=e.get("inactiveBorderColor"),l.stroke=r.get("inactiveColor"),l.lineWidth=r.get("inactiveWidth"));return{itemStyle:s,lineStyle:l}}(l=y||l||"roundRect",i,a,s,h,d),s=new Uw,h=i.getModel("textStyle"),y=(!S(t.getLegendIcon)||y&&"inherit"!==y?(y="inherit"===y&&t.getData().getVisual("symbol")?"inherit"===f?t.getData().getVisual("symbolRotate"):f:0,s.add(function(t){var e=t.icon||"roundRect",n=Ey(e,0,0,t.itemWidth,t.itemHeight,t.itemStyle.fill,t.symbolKeepAspect);n.setStyle(t.itemStyle),n.rotation=(t.iconRotate||0)*Math.PI/180,n.setOrigin([t.itemWidth/2,t.itemHeight/2]),-1<e.indexOf("empty")&&(n.style.stroke=n.style.fill,n.style.fill="#fff",n.style.lineWidth=2);return n}({itemWidth:c,itemHeight:p,icon:l,iconRotate:y,itemStyle:a.itemStyle,lineStyle:a.lineStyle,symbolKeepAspect:g}))):s.add(t.getLegendIcon({itemWidth:c,itemHeight:p,icon:l,iconRotate:f,itemStyle:a.itemStyle,lineStyle:a.lineStyle,symbolKeepAspect:g})),"left"===o?c+5:-5),t=o,l=r.get("formatter"),f=e,a=(V(l)&&l?f=l.replace("{name}",null!=e?e:""):S(l)&&(f=l(e)),i.get("inactiveColor")),g=(s.add(new bs({style:Jh(h,{text:f,x:y,y:p/2,fill:d?h.getTextColor():a,align:t,verticalAlign:"middle"})})),new ms({shape:s.getBoundingRect(),invisible:!0})),c=i.getModel("tooltip");return c.get("show")&&Uh({el:g,componentModel:r,itemName:e,itemTooltipOption:c.option}),s.add(g),s.eachChild(function(t){t.silent=!0}),g.silent=!u,this.getContentGroup().add(s),Sl(s),s.__legendDataIndex=n,s},Yw.prototype.layoutInner=function(t,e,n,i,r,o){var a,s,l,u,h,c=this.getContentGroup(),p=this.getSelectorGroup(),n=(xp(t.get("orient"),c,t.get("itemGap"),n.width,n.height),c.getBoundingRect()),d=[-n.x,-n.y];return p.markRedraw(),c.markRedraw(),r?(xp("horizontal",p,t.get("selectorItemGap",!0)),a=[-(r=p.getBoundingRect()).x,-r.y],s=t.get("selectorButtonGap",!0),l=0===(t=t.getOrient().index)?"width":"height",u=0===t?"height":"width",h=0===t?"y":"x","end"===o?a[t]+=n[l]+s:d[t]+=r[l]+s,a[1-t]+=n[u]/2-r[u]/2,p.x=a[0],p.y=a[1],c.x=d[0],c.y=d[1],(o={x:0,y:0})[l]=n[l]+s+r[l],o[u]=Math.max(n[u],r[u]),o[h]=Math.min(0,r[h]+a[1-t]),o):(c.x=d[0],c.y=d[1],this.group.getBoundingRect())},Yw.prototype.remove=function(){this.getContentGroup().removeAll(),this._isFirstRender=!0},Yw.type="legend.plain",Yw);function Yw(){var t=null!==Hw&&Hw.apply(this,arguments)||this;return t.type=Yw.type,t.newlineDisabled=!1,t}function qw(t,e,n,i){Kw(t,e,n,i),n.dispatchAction({type:"legendToggleSelect",name:null!=t?t:e}),jw(t,e,n,i)}function Zw(t){for(var e,n=t.getZr().storage.getDisplayList(),i=0,r=n.length;i<r&&!(e=n[i].states.emphasis);)i++;return e&&e.hoverLayer}function jw(t,e,n,i){Zw(n)||n.dispatchAction({type:"highlight",seriesName:t,name:e,excludeSeriesId:i})}function Kw(t,e,n,i){Zw(n)||n.dispatchAction({type:"downplay",seriesName:t,name:e,excludeSeriesId:i})}function $w(t){var n=t.findComponents({mainType:"legend"});n&&n.length&&t.filterSeries(function(t){for(var e=0;e<n.length;e++)if(!n[e].isSelected(t.name))return!1;return!0})}function Qw(t,e,n){var i,r={},o="toggleSelected"===t;return n.eachComponent("legend",function(n){o&&null!=i?n[i?"select":"unSelect"](e.name):"allSelect"===t||"inverseSelect"===t?n[t]():(n[t](e.name),i=n.isSelected(e.name)),O(n.getData(),function(t){var e,t=t.get("name");"\n"!==t&&""!==t&&(e=n.isSelected(t),r.hasOwnProperty(t)?r[t]=r[t]&&e:r[t]=e)})}),"allSelect"===t||"inverseSelect"===t?{selected:r}:{name:e.name,selected:r}}function Jw(t){t.registerComponentModel(Fw),t.registerComponentView(Xw),t.registerProcessor(t.PRIORITY.PROCESSOR.SERIES_FILTER,$w),t.registerSubTypeDefaulter("legend",function(){return"plain"}),(t=t).registerAction("legendToggleSelect","legendselectchanged",pt(Qw,"toggleSelected")),t.registerAction("legendAllSelect","legendselectall",pt(Qw,"allSelect")),t.registerAction("legendInverseSelect","legendinverseselect",pt(Qw,"inverseSelect")),t.registerAction("legendSelect","legendselected",pt(Qw,"select")),t.registerAction("legendUnSelect","legendunselected",pt(Qw,"unSelect"))}u(nb,tb=Fw),nb.prototype.setScrollDataIndex=function(t){this.option.scrollDataIndex=t},nb.prototype.init=function(t,e,n){var i=Mp(t);tb.prototype.init.call(this,t,e,n),ib(this,t,i)},nb.prototype.mergeOption=function(t,e){tb.prototype.mergeOption.call(this,t,e),ib(this,this.option,t)},nb.type="legend.scroll",nb.defaultOption=(Uu=Fw.defaultOption,Jl={scrollDataIndex:0,pageButtonItemGap:5,pageButtonGap:null,pageButtonPosition:"end",pageFormatter:"{current}/{total}",pageIcons:{horizontal:["M0,0L12,-10L12,10z","M0,0L-12,-10L-12,10z"],vertical:["M0,0L20,0L10,-20z","M0,0L20,0L10,20z"]},pageIconColor:"#2f4554",pageIconInactiveColor:"#aaa",pageIconSize:15,pageTextStyle:{color:"#333"},animationDurationUpdate:800},d(d({},Uu,!0),Jl,!0));var tb,eb=nb;function nb(){var t=null!==tb&&tb.apply(this,arguments)||this;return t.type=nb.type,t}function ib(t,e,n){var i=[1,1];i[t.getOrient().index]=0,Sp(e,n,{type:"box",ignoreSize:!!i})}var rb,ob=Pr,ab=["width","height"],sb=["x","y"],lb=(u(ub,rb=Xw),ub.prototype.init=function(){rb.prototype.init.call(this),this.group.add(this._containerGroup=new ob),this._containerGroup.add(this.getContentGroup()),this.group.add(this._controllerGroup=new ob)},ub.prototype.resetInner=function(){rb.prototype.resetInner.call(this),this._controllerGroup.removeAll(),this._containerGroup.removeClipPath(),this._containerGroup.__rectSize=null},ub.prototype.renderInner=function(t,i,e,r,n,o,a){var s=this,l=(rb.prototype.renderInner.call(this,t,i,e,r,n,o,a),this._controllerGroup),t=i.get("pageIconSize",!0),u=F(t)?t:[t,t],e=(h("pagePrev",0),i.getModel("pageTextStyle"));function h(t,e){var n=t+"DataIndex",e=Gh(i.get("pageIcons",!0)[i.getOrient().name][e],{onclick:ct(s._pageGo,s,n,i,r)},{x:-u[0]/2,y:-u[1]/2,width:u[0],height:u[1]});e.name=t,l.add(e)}l.add(new bs({name:"pageText",style:{text:"xx/xx",fill:e.getTextColor(),font:e.getFont(),verticalAlign:"middle",align:"center"},silent:!0})),h("pageNext",1)},ub.prototype.layoutInner=function(t,e,n,i,r,o){var a=this.getSelectorGroup(),s=t.getOrient().index,l=ab[s],u=sb[s],h=ab[1-s],c=sb[1-s],p=(r&&xp("horizontal",a,t.get("selectorItemGap",!0)),t.get("selectorButtonGap",!0)),d=a.getBoundingRect(),f=[-d.x,-d.y],g=y(n),n=(r&&(g[l]=n[l]-d[l]-p),this._layoutContentAndController(t,i,g,s,l,h,c,u));return r&&("end"===o?f[s]+=n[l]+p:(t=d[l]+p,f[s]-=t,n[u]-=t),n[l]+=d[l]+p,f[1-s]+=n[c]+n[h]/2-d[h]/2,n[h]=Math.max(n[h],d[h]),n[c]=Math.min(n[c],d[c]+f[1-s]),a.x=f[0],a.y=f[1],a.markRedraw()),n},ub.prototype._layoutContentAndController=function(t,e,n,i,r,o,a,s){var l=this.getContentGroup(),u=this._containerGroup,h=this._controllerGroup,c=(xp(t.get("orient"),l,t.get("itemGap"),i?n.width:null,i?null:n.height),xp("horizontal",h,t.get("pageButtonItemGap",!0)),l.getBoundingRect()),p=h.getBoundingRect(),d=this._showController=c[r]>n[r],f=[-c.x,-c.y],e=(e||(f[i]=l[s]),[0,0]),s=[-p.x,-p.y],g=N(t.get("pageButtonGap",!0),t.get("itemGap",!0)),f=(d&&("end"===t.get("pageButtonPosition",!0)?s[i]+=n[r]-p[r]:e[i]+=p[r]+g),s[1-i]+=c[o]/2-p[o]/2,l.setPosition(f),u.setPosition(e),h.setPosition(s),{x:0,y:0}),c=(f[r]=(d?n:c)[r],f[o]=Math.max(c[o],p[o]),f[a]=Math.min(0,p[a]+s[1-i]),u.__rectSize=n[r],d?((e={x:0,y:0})[r]=Math.max(n[r]-p[r]-g,0),e[o]=f[o],u.setClipPath(new ms({shape:e})),u.__rectSize=e[r]):h.eachChild(function(t){t.attr({invisible:!0,silent:!0})}),this._getPageInfo(t));return null!=c.pageIndex&&mh(l,{x:c.contentPosition[0],y:c.contentPosition[1]},d?t:null),this._updatePageInfoView(t,c),f},ub.prototype._pageGo=function(t,e,n){t=this._getPageInfo(e)[t];null!=t&&n.dispatchAction({type:"legendScroll",scrollDataIndex:t,legendId:e.id})},ub.prototype._updatePageInfoView=function(n,i){var r=this._controllerGroup,t=(O(["pagePrev","pageNext"],function(t){var e=null!=i[t+"DataIndex"],t=r.childOfName(t);t&&(t.setStyle("fill",e?n.get("pageIconColor",!0):n.get("pageIconInactiveColor",!0)),t.cursor=e?"pointer":"default")}),r.childOfName("pageText")),e=n.get("pageFormatter"),o=i.pageIndex,o=null!=o?o+1:0,a=i.pageCount;t&&e&&t.setStyle("text",V(e)?e.replace("{current}",null==o?"":o+"").replace("{total}",null==a?"":a+""):e({current:o,total:a}))},ub.prototype._getPageInfo=function(t){var e=t.get("scrollDataIndex",!0),n=this.getContentGroup(),i=this._containerGroup.__rectSize,t=t.getOrient().index,r=ab[t],o=sb[t],e=this._findTargetItemIndex(e),a=n.children(),s=a[e],l=a.length,u=l?1:0,h={contentPosition:[n.x,n.y],pageCount:u,pageIndex:u-1,pagePrevDataIndex:null,pageNextDataIndex:null};if(s){n=g(s);h.contentPosition[t]=-n.s;for(var c=e+1,p=n,d=n,f=null;c<=l;++c)(!(f=g(a[c]))&&d.e>p.s+i||f&&!y(f,p.s))&&(p=d.i>p.i?d:f)&&(null==h.pageNextDataIndex&&(h.pageNextDataIndex=p.i),++h.pageCount),d=f;for(c=e-1,p=n,d=n,f=null;-1<=c;--c)(f=g(a[c]))&&y(d,f.s)||!(p.i<d.i)||(d=p,null==h.pagePrevDataIndex&&(h.pagePrevDataIndex=p.i),++h.pageCount,++h.pageIndex),p=f}return h;function g(t){var e,n;if(t)return{s:n=(e=t.getBoundingRect())[o]+t[o],e:n+e[r],i:t.__legendDataIndex}}function y(t,e){return t.e>=e&&t.s<=e+i}},ub.prototype._findTargetItemIndex=function(n){var i,r;return this._showController?(this.getContentGroup().eachChild(function(t,e){t=t.__legendDataIndex;null==r&&null!=t&&(r=e),t===n&&(i=e)}),null!=i?i:r):0},ub.type="legend.scroll",ub);function ub(){var t=null!==rb&&rb.apply(this,arguments)||this;return t.type=ub.type,t.newlineDisabled=!0,t._currentIndex=0,t}__(function(t){__(Jw),t.registerComponentModel(eb),t.registerComponentView(lb),t.registerAction("legendScroll","legendscroll",function(t,e){var n=t.scrollDataIndex;null!=n&&e.eachComponent({mainType:"legend",subType:"scroll",query:t},function(t){t.setScrollDataIndex(n)})})});var hb=_o(),cb=y,pb=ct;function db(){this._dragging=!1,this.animationThreshold=15}function fb(t,e,n,i){!function n(i,t){{var r;return R(i)&&R(t)?(r=!0,O(t,function(t,e){r=r&&n(i[e],t)}),!!r):i===t}}(hb(n).lastProp,i)&&(hb(n).lastProp=i,e?mh(n,i,t):(n.stopAnimation(),n.attr(i)))}function gb(t,e){t[e.get(["label","show"])?"show":"hide"]()}function yb(t){return{x:t.x||0,y:t.y||0,rotation:t.rotation||0}}function mb(t,e,n){var i=e.get("z"),r=e.get("zlevel");t&&t.traverse(function(t){"group"!==t.type&&(null!=i&&(t.z=i),null!=r&&(t.zlevel=r),t.silent=n)})}function vb(t,e,n,i,r){var o=_b(n.get("value"),e.axis,e.ecModel,n.get("seriesDataIndices"),{precision:n.get(["label","precision"]),formatter:n.get(["label","formatter"])}),n=n.getModel("label"),a=ap(n.get("padding")||0),s=n.getFont(),l=dr(o,s),u=r.position,h=l.width+a[1]+a[3],l=l.height+a[0]+a[2],c=r.align,c=("right"===c&&(u[0]-=h),"center"===c&&(u[0]-=h/2),r.verticalAlign),i=("bottom"===c&&(u[1]-=l),"middle"===c&&(u[1]-=l/2),r=u,c=h,h=l,i=(l=i).getWidth(),l=l.getHeight(),r[0]=Math.min(r[0]+c,i)-c,r[1]=Math.min(r[1]+h,l)-h,r[0]=Math.max(r[0],0),r[1]=Math.max(r[1],0),n.get("backgroundColor"));i&&"auto"!==i||(i=e.get(["axisLine","lineStyle","color"])),t.label={x:u[0],y:u[1],style:Jh(n,{text:o,font:s,fill:n.getTextColor(),padding:a,backgroundColor:i}),z2:10}}function _b(t,e,n,i,r){t=e.scale.parse(t);var o,a=e.scale.getLabel({value:t},{precision:r.precision}),r=r.formatter;return r&&(o={value:c_(e,{value:t}),axisDimension:e.dim,axisIndex:e.index,seriesData:[]},O(i,function(t){var e=n.getSeriesByIndex(t.seriesIndex),t=t.dataIndexInside,e=e&&e.getDataParams(t);e&&o.seriesData.push(e)}),V(r)?a=r.replace("{value}",a):S(r)&&(a=r(o))),a}function xb(t,e,n){var i=Bi();return Wi(i,i,n.rotation),Gi(i,i,n.position),Eh([t.dataToCoord(e),(n.labelOffset||0)+(n.labelDirection||1)*(n.labelMargin||0)],i)}db.prototype.render=function(t,e,n,i){var r,o,a=e.get("value"),s=e.get("status");this._axisModel=t,this._axisPointerModel=e,this._api=n,!i&&this._lastValue===a&&this._lastStatus===s||(this._lastValue=a,this._lastStatus=s,i=this._group,r=this._handle,s&&"hide"!==s?(i&&i.show(),r&&r.show(),this.makeElOption(s={},a,t,e,n),(o=s.graphicKey)!==this._lastGraphicKey&&this.clear(n),this._lastGraphicKey=o,o=this._moveAnimation=this.determineAnimation(t,e),i?(o=pt(fb,e,o),this.updatePointerEl(i,s,o),this.updateLabelEl(i,s,o,e)):(i=this._group=new Pr,this.createPointerEl(i,s,t,e),this.createLabelEl(i,s,t,e),n.getZr().add(i)),mb(i,e,!0),this._renderHandle(a)):(i&&i.hide(),r&&r.hide()))},db.prototype.remove=function(t){this.clear(t)},db.prototype.dispose=function(t){this.clear(t)},db.prototype.determineAnimation=function(t,e){var n,i=e.get("animation"),r=t.axis,o="category"===r.type,e=e.get("snap");return!(!e&&!o)&&("auto"===i||null==i?(n=this.animationThreshold,o&&r.getBandWidth()>n||!!e&&(o=gw(t).seriesDataCount,e=r.getExtent(),Math.abs(e[0]-e[1])/o>n)):!0===i)},db.prototype.makeElOption=function(t,e,n,i,r){},db.prototype.createPointerEl=function(t,e,n,i){var r=e.pointer;r&&(r=hb(t).pointerEl=new qh[r.type](cb(e.pointer)),t.add(r))},db.prototype.createLabelEl=function(t,e,n,i){e.label&&(e=hb(t).labelEl=new bs(cb(e.label)),t.add(e),gb(e,i))},db.prototype.updatePointerEl=function(t,e,n){t=hb(t).pointerEl;t&&e.pointer&&(t.setStyle(e.pointer.style),n(t,{shape:e.pointer.shape}))},db.prototype.updateLabelEl=function(t,e,n,i){t=hb(t).labelEl;t&&(t.setStyle(e.label.style),n(t,{x:e.label.x,y:e.label.y}),gb(t,i))},db.prototype._renderHandle=function(t){var e,n,i,r,o,a;!this._dragging&&this.updateHandleTransform&&(e=this._axisPointerModel,n=this._api.getZr(),i=this._handle,r=e.getModel("handle"),a=e.get("status"),r.get("show")&&a&&"hide"!==a?(this._handle||(o=!0,i=this._handle=Gh(r.get("icon"),{cursor:"move",draggable:!0,onmousemove:function(t){be(t.event)},onmousedown:pb(this._onHandleDragMove,this,0,0),drift:pb(this._onHandleDragMove,this),ondragend:pb(this._onHandleDragEnd,this)}),n.add(i)),mb(i,e,!1),i.setStyle(r.getItemStyle(null,["color","borderColor","borderWidth","opacity","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"])),F(a=r.get("size"))||(a=[a,a]),i.scaleX=a[0]/2,i.scaleY=a[1]/2,qg(this,"_doDispatchAxisPointer",r.get("throttle")||0,"fixRate"),this._moveHandleToValue(t,o)):(i&&n.remove(i),this._handle=null))},db.prototype._moveHandleToValue=function(t,e){fb(this._axisPointerModel,!e&&this._moveAnimation,this._handle,yb(this.getHandleTransform(t,this._axisModel,this._axisPointerModel)))},db.prototype._onHandleDragMove=function(t,e){var n=this._handle;n&&(this._dragging=!0,t=this.updateHandleTransform(yb(n),[t,e],this._axisModel,this._axisPointerModel),this._payloadInfo=t,n.stopAnimation(),n.attr(yb(t)),hb(n).lastProp=null,this._doDispatchAxisPointer())},db.prototype._doDispatchAxisPointer=function(){var t,e;this._handle&&(t=this._payloadInfo,e=this._axisModel,this._api.dispatchAction({type:"updateAxisPointer",x:t.cursorPoint[0],y:t.cursorPoint[1],tooltipOption:t.tooltipOption,axesInfo:[{axisDim:e.axis.dim,axisIndex:e.componentIndex}]}))},db.prototype._onHandleDragEnd=function(){var t;this._dragging=!1,this._handle&&(t=this._axisPointerModel.get("value"),this._moveHandleToValue(t),this._api.dispatchAction({type:"hideTip"}))},db.prototype.clear=function(t){this._lastValue=null,this._lastStatus=null;var t=t.getZr(),e=this._group,n=this._handle;t&&e&&(this._lastGraphicKey=null,e&&t.remove(e),n&&t.remove(n),this._group=null,this._handle=null,this._payloadInfo=null),Zg(this,"_doDispatchAxisPointer")},db.prototype.doClear=function(){},db.prototype.buildLabel=function(t,e,n){return{x:t[n=n||0],y:t[1-n],width:e[n],height:e[1-n]}};u(Sb,wb=db),Sb.prototype.makeElOption=function(t,e,n,i,r){var o,a,s=n.axis,l=s.grid,u=i.get("type"),h=Mb(l,s).getOtherAxis(s).getGlobalExtent(),c=s.toGlobalCoord(s.dataToCoord(e,!0)),p=(u&&"none"!==u&&(o=(a=i).get("type"),a=a.getModel(o+"Style"),"line"===o?(p=a.getLineStyle()).fill=null:"shadow"===o&&((p=a.getAreaStyle()).stroke=null),o=p,(a=Tb[u](s,c,h)).style=o,t.graphicKey=a.type,t.pointer=a),Kx(l.model,n));u=e,s=t,c=p,h=n,o=i,a=r,l=aw.innerTextLayout(c.rotation,0,c.labelDirection),c.labelMargin=o.get(["label","margin"]),vb(s,h,o,a,{position:xb(h.axis,u,c),align:l.textAlign,verticalAlign:l.textVerticalAlign})},Sb.prototype.getHandleTransform=function(t,e,n){var i=Kx(e.axis.grid.model,e,{labelInside:!1}),n=(i.labelMargin=n.get(["handle","margin"]),xb(e.axis,t,i));return{x:n[0],y:n[1],rotation:i.rotation+(i.labelDirection<0?Math.PI:0)}},Sb.prototype.updateHandleTransform=function(t,e,n,i){var n=n.axis,r=n.grid,o=n.getGlobalExtent(!0),r=Mb(r,n).getOtherAxis(n).getGlobalExtent(),n="x"===n.dim?0:1,a=[t.x,t.y],e=(a[n]+=e[n],a[n]=Math.min(o[1],a[n]),a[n]=Math.max(o[0],a[n]),(r[1]+r[0])/2),o=[e,e];o[n]=a[n];return{x:a[0],y:a[1],rotation:t.rotation,cursorPoint:o,tooltipOption:[{verticalAlign:"middle"},{align:"center"}][n]}};var wb,bb=Sb;function Sb(){return null!==wb&&wb.apply(this,arguments)||this}function Mb(t,e){var n={};return n[e.dim+"AxisIndex"]=e.index,t.getCartesian(n)}var Tb={line:function(t,e,n){var i;return i=[e,n[0]],e=[e,n[1]],n=Cb(t),{type:"Line",subPixelOptimize:!0,shape:{x1:i[n=n||0],y1:i[1-n],x2:e[n],y2:e[1-n]}}},shadow:function(t,e,n){var i=Math.max(1,t.getBandWidth()),r=n[1]-n[0];return{type:"Rect",shape:(e=[e-i/2,n[0]],n=[i,r],i=Cb(t),{x:e[i=i||0],y:e[1-i],width:n[i],height:n[1-i]})}}};function Cb(t){return"x"===t.dim?0:1}u(Db,Ib=g),Db.type="axisPointer",Db.defaultOption={show:"auto",z:50,type:"line",snap:!1,triggerTooltip:!0,value:null,status:null,link:[],animation:null,animationDurationUpdate:200,lineStyle:{color:"#B9BEC9",width:1,type:"dashed"},shadowStyle:{color:"rgba(210,219,238,0.2)"},label:{show:!0,formatter:null,precision:"auto",margin:3,color:"#fff",padding:[5,7,5,7],backgroundColor:"auto",borderColor:null,borderWidth:0,borderRadius:3},handle:{show:!1,icon:"M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7v-1.2h6.6z M13.3,22H6.7v-1.2h6.6z M13.3,19.6H6.7v-1.2h6.6z",size:45,margin:50,color:"#333",shadowBlur:3,shadowColor:"#aaa",shadowOffsetX:0,shadowOffsetY:2,throttle:40}};var Ib,kb=Db;function Db(){var t=null!==Ib&&Ib.apply(this,arguments)||this;return t.type=Db.type,t}var Ab=_o(),Pb=O;function Lb(t,e,n){var i,c,p;function r(t,h){c.on(t,function(e){n=p;var n,i,r={dispatchAction:o,pendings:i={showTip:[],hideTip:[]}};function o(t){var e=i[t.type];e?e.push(t):(t.dispatchAction=o,n.dispatchAction(t))}Pb(Ab(c).records,function(t){t&&h(t,e,r.dispatchAction)});var t,a=r.pendings,s=p,l=a.showTip.length,u=a.hideTip.length;l?t=a.showTip[l-1]:u&&(t=a.hideTip[u-1]),t&&(t.dispatchAction=null,s.dispatchAction(t))})}b.node||(i=e.getZr(),Ab(i).records||(Ab(i).records={}),p=e,Ab(c=i).initialized||(Ab(c).initialized=!0,r("click",pt(Rb,"click")),r("mousemove",pt(Rb,"mousemove")),r("globalout",Ob)),(Ab(i).records[t]||(Ab(i).records[t]={})).handler=n)}function Ob(t,e,n){t.handler("leave",null,n)}function Rb(t,e,n,i){e.handler(t,n,i)}function Nb(t,e){b.node||(e=e.getZr(),(Ab(e).records||{})[t]&&(Ab(e).records[t]=null))}u(Bb,zb=Pg),Bb.prototype.render=function(t,e,n){var e=e.getComponent("tooltip"),i=t.get("triggerOn")||e&&e.get("triggerOn")||"mousemove|click";Lb("axisPointer",n,function(t,e,n){"none"!==i&&("leave"===t||0<=i.indexOf(t))&&n({type:"updateAxisPointer",currTrigger:t,x:e&&e.offsetX,y:e&&e.offsetY})})},Bb.prototype.remove=function(t,e){Nb("axisPointer",e)},Bb.prototype.dispose=function(t,e){Nb("axisPointer",e)},Bb.type="axisPointer";var zb,Eb=Bb;function Bb(){var t=null!==zb&&zb.apply(this,arguments)||this;return t.type=Bb.type,t}function Fb(t,e){var n,i,r,o,a=[],s=t.seriesIndex;return null==s||!(e=e.getSeriesByIndex(s))||null==(s=vo(n=e.getData(),t))||s<0||F(s)?{point:[]}:(i=n.getItemGraphicEl(s),r=e.coordinateSystem,e.getTooltipPosition?a=e.getTooltipPosition(s)||[]:r&&r.dataToPoint?a=t.isStacked?(e=r.getBaseAxis(),t=r.getOtherAxis(e).dim,e=e.dim,t="x"===t||"radius"===t?1:0,e=n.mapDimension(e),(o=[])[t]=n.get(e,s),o[1-t]=n.get(n.getCalculationInfo("stackResultDimension"),s),r.dataToPoint(o)||[]):r.dataToPoint(n.getValues(B(r.dimensions,function(t){return n.mapDimension(t)}),s))||[]:i&&((e=i.getBoundingRect().clone()).applyTransform(i.transform),a=[e.x+e.width/2,e.y+e.height/2]),{point:a,el:i})}var Vb=_o();function Hb(t,e,n){var o,a,i,s,l,r,u,h,c,p,d,f,g,y,m=t.currTrigger,v=[t.x,t.y],_=t,x=t.dispatchAction||ct(n.dispatchAction,n),w=e.getComponent("axisPointer").coordSysAxesInfo;if(w)return Yb(v)&&(v=Fb({seriesIndex:_.seriesIndex,dataIndex:_.dataIndex},e).point),o=Yb(v),a=_.axesInfo,i=w.axesInfo,s="leave"===m||Yb(v),l={},e={list:[],map:{}},u={showPointer:pt(Wb,r={}),showTooltip:pt(Ub,e)},O(w.coordSysMap,function(t,e){var r=o||t.containPoint(v);O(w.coordSysAxesInfo[e],function(t,e){var n=t.axis,i=function(t,e){for(var n=0;n<(t||[]).length;n++){var i=t[n];if(e.axis.dim===i.axisDim&&e.axis.model.componentIndex===i.axisIndex)return i}}(a,t);s||!r||a&&!i||null!=(i=null!=(i=i&&i.value)||o?i:n.pointToData(v))&&Gb(t,i,u,!1,l)})}),h={},O(i,function(n,t){var i=n.linkGroup;i&&!r[t]&&O(i.axesInfo,function(t,e){var e=r[e];t!==n&&e&&(e=e.value,i.mapper&&(e=n.axis.scale.parse(i.mapper(e,Xb(t),Xb(n)))),h[n.key]=e)})}),O(h,function(t,e){Gb(i[e],t,u,!0,l)}),c=r,_=i,p=l.axesInfo=[],O(_,function(t,e){var n=t.axisPointerModel.option,e=c[e];e?(t.useHandle||(n.status="show"),n.value=e.value,n.seriesDataIndices=(e.payloadBatch||[]).slice()):t.useHandle||(n.status="hide"),"show"===n.status&&p.push({axisDim:t.axis.dim,axisIndex:t.axis.model.componentIndex,value:n.value})}),m=e,_=t,e=x,Yb(t=v)||!m.list.length?e({type:"hideTip"}):(x=((m.list[0].dataByAxis[0]||{}).seriesDataIndices||[])[0]||{},e({type:"showTip",escapeConnect:!0,x:t[0],y:t[1],tooltipOption:_.tooltipOption,position:_.position,dataIndexInside:x.dataIndexInside,dataIndex:x.dataIndex,seriesIndex:x.seriesIndex,dataByCoordSys:m.list})),e=i,_=(t=n).getZr(),x="axisPointerLastHighlights",d=Vb(_)[x]||{},f=Vb(_)[x]={},O(e,function(t,e){t=t.axisPointerModel.option;"show"===t.status&&O(t.seriesDataIndices,function(t){var e=t.seriesIndex+" | "+t.dataIndex;f[e]=t})}),g=[],y=[],O(d,function(t,e){f[e]||y.push(t)}),O(f,function(t,e){d[e]||g.push(t)}),y.length&&t.dispatchAction({type:"downplay",escapeConnect:!0,notBlur:!0,batch:y}),g.length&&t.dispatchAction({type:"highlight",escapeConnect:!0,notBlur:!0,batch:g}),l}function Gb(t,e,n,i,r){var o,a,s,l,u,h,c,p,d,f,g=t.axis;!g.scale.isBlank()&&g.containData(e)&&(t.involveSeries?(a=e,s=t.axis,l=s.dim,u=a,h=[],c=Number.MAX_VALUE,p=-1,O(t.seriesModels,function(e,t){var n,i=e.getData().mapDimensionsAll(l);if(e.getAxisTooltipData)var r=e.getAxisTooltipData(i,a,s),o=r.dataIndices,r=r.nestestValue;else{if(!(o=e.getData().indicesOfNearest(i[0],a,"category"===s.type?.5:null)).length)return;r=e.getData().get(i[0],o[0])}null!=r&&isFinite(r)&&(i=a-r,(n=Math.abs(i))<=c)&&((n<c||0<=i&&p<0)&&(c=n,p=i,u=r,h.length=0),O(o,function(t){h.push({seriesIndex:e.seriesIndex,dataIndexInside:t,dataIndex:e.getData().getRawIndex(t)})}))}),f=(o={payloadBatch:h,snapToValue:u}).snapToValue,(d=o.payloadBatch)[0]&&null==r.seriesIndex&&L(r,d[0]),!i&&t.snap&&g.containData(f)&&null!=f&&(e=f),n.showPointer(t,e,d),n.showTooltip(t,o,f)):n.showPointer(t,e))}function Wb(t,e,n,i){t[e.key]={value:n,payloadBatch:i}}function Ub(t,e,n,i){var r,o,n=n.payloadBatch,a=e.axis,s=a.model,l=e.axisPointerModel;e.triggerTooltip&&n.length&&(r=mw(e=e.coordSys.model),(o=t.map[r])||(o=t.map[r]={coordSysId:e.id,coordSysIndex:e.componentIndex,coordSysType:e.type,coordSysMainType:e.mainType,dataByAxis:[]},t.list.push(o)),o.dataByAxis.push({axisDim:a.dim,axisIndex:s.componentIndex,axisType:s.type,axisId:s.id,value:i,valueLabelOpt:{precision:l.get(["label","precision"]),formatter:l.get(["label","formatter"])},seriesDataIndices:n.slice()}))}function Xb(t){var e=t.axis.model,n={},t=n.axisDim=t.axis.dim;return n.axisIndex=n[t+"AxisIndex"]=e.componentIndex,n.axisName=n[t+"AxisName"]=e.name,n.axisId=n[t+"AxisId"]=e.id,n}function Yb(t){return!t||null==t[0]||isNaN(t[0])||null==t[1]||isNaN(t[1])}function qb(t){xw.registerAxisPointerClass("CartesianAxisPointer",bb),t.registerComponentModel(kb),t.registerComponentView(Eb),t.registerPreprocessor(function(t){var e;t&&(t.axisPointer&&0!==t.axisPointer.length||(t.axisPointer={}),e=t.axisPointer.link)&&!F(e)&&(t.axisPointer.link=[e])}),t.registerProcessor(t.PRIORITY.PROCESSOR.STATISTIC,function(t,e){t.getComponent("axisPointer").coordSysAxesInfo=dw(t,e)}),t.registerAction({type:"updateAxisPointer",event:"updateAxisPointer",update:":updateAxisPointer"},Hb)}u(Kb,Zb=g),Kb.type="tooltip",Kb.dependencies=["axisPointer"],Kb.defaultOption={z:60,show:!0,showContent:!0,trigger:"item",triggerOn:"mousemove|click",alwaysShowContent:!1,displayMode:"single",renderMode:"auto",confine:null,showDelay:0,hideDelay:100,transitionDuration:.4,enterable:!1,backgroundColor:"#fff",shadowBlur:10,shadowColor:"rgba(0, 0, 0, .2)",shadowOffsetX:1,shadowOffsetY:2,borderRadius:4,borderWidth:1,padding:null,extraCssText:"",axisPointer:{type:"line",axis:"auto",animation:"auto",animationDurationUpdate:200,animationEasingUpdate:"exponentialOut",crossStyle:{color:"#999",width:1,type:"dashed",textStyle:{}}},textStyle:{color:"#666",fontSize:14}};var Zb,jb=Kb;function Kb(){var t=null!==Zb&&Zb.apply(this,arguments)||this;return t.type=Kb.type,t}function $b(t){var e=t.get("confine");return null!=e?e:"richText"===t.get("renderMode")}function Qb(t){if(b.domSupported)for(var e=document.documentElement.style,n=0,i=t.length;n<i;n++)if(t[n]in e)return t[n]}var Jb=Qb(["transform","webkitTransform","OTransform","MozTransform","msTransform"]);function tS(t,e){if(!t)return e;e=op(e,!0);var n=t.indexOf(e);return(t=-1===n?e:"-"+t.slice(0,n)+"-"+e).toLowerCase()}var eS=tS(Qb(["webkitTransition","transition","OTransition","MozTransition","msTransition"]),"transition"),nS=tS(Jb,"transform"),iS="position:absolute;display:block;border-style:solid;white-space:nowrap;z-index:9999999;"+(b.transform3dSupported?"will-change:transform;":"");function rS(t,e,n){var i,t=t.toFixed(0)+"px",e=e.toFixed(0)+"px";return b.transformSupported?(i="translate"+((i=b.transform3dSupported)?"3d":"")+"("+t+","+e+(i?",0":"")+")",n?"top:0;left:0;"+nS+":"+i+";":[["top",0],["left",0],[Jb,i]]):n?"top:"+e+";left:"+t+";":[["top",e],["left",t]]}function oS(i,t,e){var n,r,o=[],a=i.get("transitionDuration"),s=i.get("backgroundColor"),l=i.get("shadowBlur"),u=i.get("shadowColor"),h=i.get("shadowOffsetX"),c=i.get("shadowOffsetY"),p=i.getModel("textStyle"),d=yg(i,"html");return o.push("box-shadow:"+(h+"px "+c+"px "+l+"px "+u)),t&&a&&o.push((u="opacity"+(l=" "+(h=a)/2+"s "+(c="cubic-bezier(0.23,1,0.32,1)"))+",visibility"+l,e||(l=" "+h+"s "+c,u+=b.transformSupported?","+nS+l:",left"+l+",top"+l),eS+":"+u)),s&&o.push("background-color:"+s),O(["width","color","radius"],function(t){var e="border-"+t,n=op(e),n=i.get(n);null!=n&&o.push(e+":"+n+("color"===t?"":"px"))}),o.push((r=[],t=(n=p).get("fontSize"),(a=n.getTextColor())&&r.push("color:"+a),r.push("font:"+n.getFont()),t&&r.push("line-height:"+Math.round(3*t/2)+"px"),a=n.get("textShadowColor"),t=n.get("textShadowBlur")||0,e=n.get("textShadowOffsetX")||0,h=n.get("textShadowOffsetY")||0,a&&t&&r.push("text-shadow:"+e+"px "+h+"px "+t+"px "+a),O(["decoration","align"],function(t){var e=n.get(t);e&&r.push("text-"+t+":"+e)}),r.join(";"))),null!=d&&o.push("padding:"+ap(d).join("px ")+"px"),o.join(";")+";"}function aS(t,e,n,i,r){var o,a,s=e&&e.painter;n?(n=s&&s.getViewportRoot())&&(o=t,a=document.body,de(pe,n,i,r,!0))&&de(o,a,pe[0],pe[1]):(t[0]=i,t[1]=r,(n=s&&s.getViewportRootOffset())&&(t[0]+=n.offsetLeft,t[1]+=n.offsetTop)),t[2]=t[0]/e.getWidth(),t[3]=t[1]/e.getHeight()}lS.prototype.update=function(t){var e=this._container,n=(n="position",(i=(i=e).currentStyle||document.defaultView&&document.defaultView.getComputedStyle(i))?n?i[n]:i:null),i=e.style;"absolute"!==i.position&&"absolute"!==n&&(i.position="relative"),t.get("alwaysShowContent")&&this._moveIfResized(),this.el.className=t.get("className")||""},lS.prototype.show=function(t,e){clearTimeout(this._hideTimeout),clearTimeout(this._longHideTimeout);var n=this.el,i=n.style,r=this._styleCoord;n.innerHTML?i.cssText=iS+oS(t,!this._firstShow,this._longHide)+rS(r[0],r[1],!0)+"border-color:"+gp(e)+";"+(t.get("extraCssText")||"")+";pointer-events:"+(this._enterable?"auto":"none"):i.display="none",this._show=!0,this._firstShow=!1,this._longHide=!1},lS.prototype.setContent=function(t,e,n,i,r){var o=this.el;if(null==t)o.innerHTML="";else{var a,s,l,u,h,c="";if(V(r)&&"item"===n.get("trigger")&&!$b(n)&&(n=n,i=i,c=V(r=r)&&"inside"!==r?(a=n.get("backgroundColor"),n=n.get("borderWidth"),i=gp(i),r=r="left"===(r=r)?"right":"right"===r?"left":"top"===r?"bottom":"top",p=Math.max(1.5*Math.round(n),6),s="",l=nS+":",-1<I(["left","right"],r)?(s+="top:50%",l+="translateY(-50%) rotate("+(h="left"==r?-225:-45)+"deg)"):(s+="left:50%",l+="translateX(-50%) rotate("+(h="top"==r?225:45)+"deg)"),h=h*Math.PI/180,h=(u=p+n)*Math.abs(Math.cos(h))+u*Math.abs(Math.sin(h)),i=i+" solid "+n+"px;",'<div style="'+["position:absolute;width:"+p+"px;height:"+p+"px;",(s+=";"+r+":-"+Math.round(100*((h-Math.SQRT2*n)/2+Math.SQRT2*n-(h-u)/2))/100+"px")+";"+l+";","border-bottom:"+i,"border-right:"+i,"background-color:"+a+";"].join("")+'"></div>'):""),V(t))o.innerHTML=t+c;else if(t){o.innerHTML="",F(t)||(t=[t]);for(var p,d=0;d<t.length;d++)mt(t[d])&&t[d].parentNode!==o&&o.appendChild(t[d]);c&&o.childNodes.length&&((p=document.createElement("div")).innerHTML=c,o.appendChild(p))}}},lS.prototype.setEnterable=function(t){this._enterable=t},lS.prototype.getSize=function(){var t=this.el;return[t.offsetWidth,t.offsetHeight]},lS.prototype.moveTo=function(t,e){var n,i=this._styleCoord;aS(i,this._zr,this._appendToBody,t,e),null!=i[0]&&null!=i[1]&&(n=this.el.style,O(rS(i[0],i[1]),function(t){n[t[0]]=t[1]}))},lS.prototype._moveIfResized=function(){var t=this._styleCoord[2],e=this._styleCoord[3];this.moveTo(t*this._zr.getWidth(),e*this._zr.getHeight())},lS.prototype.hide=function(){var t=this,e=this.el.style;e.visibility="hidden",e.opacity="0",b.transform3dSupported&&(e.willChange=""),this._show=!1,this._longHideTimeout=setTimeout(function(){return t._longHide=!0},500)},lS.prototype.hideLater=function(t){!this._show||this._inContent&&this._enterable||(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(ct(this.hide,this),t)):this.hide())},lS.prototype.isShow=function(){return this._show},lS.prototype.dispose=function(){this.el.parentNode.removeChild(this.el)};var sS=lS;function lS(t,e,n){if(this._show=!1,this._styleCoord=[0,0,0,0],this._enterable=!0,this._firstShow=!0,this._longHide=!0,b.wxa)return null;var i=document.createElement("div"),r=(i.domBelongToZr=!0,this.el=i,this._zr=e.getZr()),n=this._appendToBody=n&&n.appendToBody,o=(aS(this._styleCoord,r,n,e.getWidth()/2,e.getHeight()/2),(n?document.body:t).appendChild(i),this._container=t,this);i.onmouseenter=function(){o._enterable&&(clearTimeout(o._hideTimeout),o._show=!0),o._inContent=!0},i.onmousemove=function(t){var e;t=t||window.event,o._enterable||(e=r.handler,we(r.painter.getViewportRoot(),t,!0),e.dispatch("mousemove",t))},i.onmouseleave=function(){o._inContent=!1,o._enterable&&o._show&&o.hideLater(o._hideDelay)}}hS.prototype.update=function(t){t.get("alwaysShowContent")&&this._moveIfResized()},hS.prototype.show=function(){this._hideTimeout&&clearTimeout(this._hideTimeout),this.el.show(),this._show=!0},hS.prototype.setContent=function(t,e,n,i,r){var o=this,a=(R(t)&&f(""),this.el&&this._zr.remove(this.el),n.getModel("textStyle")),s=(this.el=new bs({style:{rich:e.richTextStyles,text:t,lineHeight:22,borderWidth:1,borderColor:i,textShadowColor:a.get("textShadowColor"),fill:n.get(["textStyle","color"]),padding:yg(n,"richText"),verticalAlign:"top",align:"left"},z:n.get("z")}),O(["backgroundColor","borderRadius","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"],function(t){o.el.style[t]=n.get(t)}),O(["textShadowBlur","textShadowOffsetX","textShadowOffsetY"],function(t){o.el.style[t]=a.get(t)||0}),this._zr.add(this.el),this);this.el.on("mouseover",function(){s._enterable&&(clearTimeout(s._hideTimeout),s._show=!0),s._inContent=!0}),this.el.on("mouseout",function(){s._enterable&&s._show&&s.hideLater(s._hideDelay),s._inContent=!1})},hS.prototype.setEnterable=function(t){this._enterable=t},hS.prototype.getSize=function(){var t=this.el,e=this.el.getBoundingRect(),t=pS(t.style);return[e.width+t.left+t.right,e.height+t.top+t.bottom]},hS.prototype.moveTo=function(t,e){var n,i,r=this.el;r&&(dS(i=this._styleCoord,this._zr,t,e),t=i[0],e=i[1],n=cS((i=r.style).borderWidth||0),i=pS(i),r.x=t+n+i.left,r.y=e+n+i.top,r.markRedraw())},hS.prototype._moveIfResized=function(){var t=this._styleCoord[2],e=this._styleCoord[3];this.moveTo(t*this._zr.getWidth(),e*this._zr.getHeight())},hS.prototype.hide=function(){this.el&&this.el.hide(),this._show=!1},hS.prototype.hideLater=function(t){!this._show||this._inContent&&this._enterable||(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(ct(this.hide,this),t)):this.hide())},hS.prototype.isShow=function(){return this._show},hS.prototype.dispose=function(){this._zr.remove(this.el)};var uS=hS;function hS(t){this._show=!1,this._styleCoord=[0,0,0,0],this._enterable=!0,this._zr=t.getZr(),dS(this._styleCoord,this._zr,t.getWidth()/2,t.getHeight()/2)}function cS(t){return Math.max(0,t)}function pS(t){var e=cS(t.shadowBlur||0),n=cS(t.shadowOffsetX||0),t=cS(t.shadowOffsetY||0);return{left:cS(e-n),right:cS(e+n),top:cS(e-t),bottom:cS(e+t)}}function dS(t,e,n,i){t[0]=n,t[1]=i,t[2]=t[0]/e.getWidth(),t[3]=t[1]/e.getHeight()}var fS,gS=new ms({shape:{x:-1,y:-1,width:2,height:2}}),yS=(u(mS,fS=Pg),mS.prototype.init=function(t,e){var n;!b.node&&e.getDom()&&(t=t.getComponent("tooltip"),n=this._renderMode="auto"===(n=t.get("renderMode"))?b.domSupported?"html":"richText":n||"html",this._tooltipContent="richText"===n?new uS(e):new sS(e.getDom(),e,{appendToBody:t.get("appendToBody",!0)}))},mS.prototype.render=function(t,e,n){!b.node&&n.getDom()&&(this.group.removeAll(),this._tooltipModel=t,this._ecModel=e,this._api=n,this._alwaysShowContent=t.get("alwaysShowContent"),(e=this._tooltipContent).update(t),e.setEnterable(t.get("enterable")),this._initGlobalListener(),this._keepShow(),"richText"!==this._renderMode&&t.get("transitionDuration")?qg(this,"_updatePosition",50,"fixRate"):Zg(this,"_updatePosition"))},mS.prototype._initGlobalListener=function(){var i=this._tooltipModel.get("triggerOn");Lb("itemTooltip",this._api,ct(function(t,e,n){"none"!==i&&(0<=i.indexOf(t)?this._tryShow(e,n):"leave"===t&&this._hide(n))},this))},mS.prototype._keepShow=function(){var t,e=this._tooltipModel,n=this._ecModel,i=this._api;null!=this._lastX&&null!=this._lastY&&"none"!==e.get("triggerOn")&&(t=this,clearTimeout(this._refreshUpdateTimeout),this._refreshUpdateTimeout=setTimeout(function(){i.isDisposed()||t.manuallyShowTip(e,n,i,{x:t._lastX,y:t._lastY,dataByCoordSys:t._lastDataByCoordSys})}))},mS.prototype.manuallyShowTip=function(t,e,n,i){var r,o,a,s;i.from!==this.uid&&!b.node&&n.getDom()&&(r=_S(i,n),this._ticket="",s=i.dataByCoordSys,(o=function(n,t,e){var i=bo(n).queryOptionMap,r=i.keys()[0];if(r&&"series"!==r){var o,t=Mo(t,r,i.get(r),{useDefault:!1,enableAll:!1,enableNone:!1}).models[0];if(t)if(e.getViewOfComponentModel(t).group.traverse(function(t){var e=k(t).tooltipConfig;if(e&&e.name===n.name)return o=t,!0}),o)return{componentMainType:r,componentIndex:t.componentIndex,el:o}}}(i,e,n))?((a=o.el.getBoundingRect().clone()).applyTransform(o.el.transform),this._tryShow({offsetX:a.x+a.width/2,offsetY:a.y+a.height/2,target:o.el,position:i.position,positionDefault:"bottom"},r)):i.tooltip&&null!=i.x&&null!=i.y?((a=gS).x=i.x,a.y=i.y,a.update(),k(a).tooltipConfig={name:null,option:i.tooltip},this._tryShow({offsetX:i.x,offsetY:i.y,target:a},r)):s?this._tryShow({offsetX:i.x,offsetY:i.y,position:i.position,dataByCoordSys:s,tooltipOption:i.tooltipOption},r):null!=i.seriesIndex?this._manuallyAxisShowTip(t,e,n,i)||(a=(o=Fb(i,e)).point[0],s=o.point[1],null!=a&&null!=s&&this._tryShow({offsetX:a,offsetY:s,target:o.el,position:i.position,positionDefault:"bottom"},r)):null!=i.x&&null!=i.y&&(n.dispatchAction({type:"updateAxisPointer",x:i.x,y:i.y}),this._tryShow({offsetX:i.x,offsetY:i.y,position:i.position,target:n.getZr().findHover(i.x,i.y).target},r)))},mS.prototype.manuallyHideTip=function(t,e,n,i){var r=this._tooltipContent;!this._alwaysShowContent&&this._tooltipModel&&r.hideLater(this._tooltipModel.get("hideDelay")),this._lastX=this._lastY=this._lastDataByCoordSys=null,i.from!==this.uid&&this._hide(_S(i,n))},mS.prototype._manuallyAxisShowTip=function(t,e,n,i){var r=i.seriesIndex,o=i.dataIndex,a=e.getComponent("axisPointer").coordSysAxesInfo;if(null!=r&&null!=o&&null!=a){a=e.getSeriesByIndex(r);if(a){e=vS([a.getData().getItemModel(o),a,(a.coordinateSystem||{}).model],this._tooltipModel);if("axis"===e.get("trigger"))return n.dispatchAction({type:"updateAxisPointer",seriesIndex:r,dataIndex:o,position:i.position}),!0}}},mS.prototype._tryShow=function(t,e){var n,i,r,o=t.target;this._tooltipModel&&(this._lastX=t.offsetX,this._lastY=t.offsetY,(n=t.dataByCoordSys)&&n.length?this._showAxisTooltip(n,t):o?(My(o,function(t){return null!=k(t).dataIndex?(i=t,1):null!=k(t).tooltipConfig&&(r=t,1)},!(this._lastDataByCoordSys=null)),i?this._showSeriesItemTooltip(t,i,e):r?this._showComponentItemTooltip(t,r,e):this._hide(e)):(this._lastDataByCoordSys=null,this._hide(e)))},mS.prototype._showOrMove=function(t,e){t=t.get("showDelay");e=ct(e,this),clearTimeout(this._showTimout),0<t?this._showTimout=setTimeout(e,t):e()},mS.prototype._showAxisTooltip=function(t,e){var u=this._ecModel,h=this._tooltipModel,n=[e.offsetX,e.offsetY],i=vS([e.tooltipOption],h),c=this._renderMode,p=[],d=lg("section",{blocks:[],noHeader:!0}),f=[],g=new mg,r=(O(t,function(t){O(t.dataByAxis,function(r){var o,a,s=u.getComponent(r.axisDim+"Axis",r.axisIndex),l=r.value;s&&null!=l&&(o=_b(l,s.axis,u,r.seriesDataIndices,r.valueLabelOpt),a=lg("section",{header:o,noHeader:!It(o),sortBlocks:!0,blocks:[]}),d.blocks.push(a),O(r.seriesDataIndices,function(t){var e,n=u.getSeriesByIndex(t.seriesIndex),t=t.dataIndexInside,i=n.getDataParams(t);i.dataIndex<0||(i.axisDim=r.axisDim,i.axisIndex=r.axisIndex,i.axisType=r.axisType,i.axisId=r.axisId,i.axisValue=c_(s.axis,{value:l}),i.axisValueLabel=o,i.marker=g.makeTooltipMarker("item",gp(i.color),c),(e=(t=cf(n.formatTooltip(t,!0,null))).frag)&&(n=vS([n],h).get("valueFormatter"),a.blocks.push(n?L({valueFormatter:n},e):e)),t.text&&f.push(t.text),p.push(i))}))})}),d.blocks.reverse(),f.reverse(),e.position),e=i.get("order"),e=dg(d,g,c,e,u.get("useUTC"),i.get("textStyle")),e=(e&&f.unshift(e),"richText"===c?"\n\n":"<br/>"),o=f.join(e);this._showOrMove(i,function(){this._updateContentNotChangedOnAxis(t,p)?this._updatePosition(i,r,n[0],n[1],this._tooltipContent,p):this._showTooltipContent(i,o,p,Math.random()+"",n[0],n[1],r,null,g)})},mS.prototype._showSeriesItemTooltip=function(t,e,n){var i,r,o,a,s,l=this._ecModel,e=k(e),u=e.seriesIndex,h=l.getSeriesByIndex(u),c=e.dataModel||h,p=e.dataIndex,e=e.dataType,d=c.getData(e),f=this._renderMode,g=t.positionDefault,y=vS([d.getItemModel(p),c,h&&(h.coordinateSystem||{}).model],this._tooltipModel,g?{position:g}:null),h=y.get("trigger");null!=h&&"item"!==h||(i=c.getDataParams(p,e),r=new mg,i.marker=r.makeTooltipMarker("item",gp(i.color),f),g=cf(c.formatTooltip(p,!1,e)),h=y.get("order"),e=y.get("valueFormatter"),o=g.frag,a=o?dg(e?L({valueFormatter:e},o):o,r,f,h,l.get("useUTC"),y.get("textStyle")):g.text,s="item_"+c.name+"_"+p,this._showOrMove(y,function(){this._showTooltipContent(y,a,i,s,t.offsetX,t.offsetY,t.position,t.target,r)}),n({type:"showTip",dataIndexInside:p,dataIndex:d.getRawIndex(p),seriesIndex:u,from:this.uid}))},mS.prototype._showComponentItemTooltip=function(e,n,t){var i=k(n),r=i.tooltipConfig.option||{},o=[r=V(r)?{content:r,formatter:r}:r],i=this._ecModel.getComponent(i.componentMainType,i.componentIndex),i=(i&&o.push(i),o.push({formatter:r.content}),e.positionDefault),a=vS(o,this._tooltipModel,i?{position:i}:null),s=a.get("content"),l=Math.random()+"",u=new mg;this._showOrMove(a,function(){var t=y(a.get("formatterParams")||{});this._showTooltipContent(a,s,t,l,e.offsetX,e.offsetY,e.position,n,u)}),t({type:"showTip",from:this.uid})},mS.prototype._showTooltipContent=function(n,t,i,e,r,o,a,s,l){var u,h,c,p,d;this._ticket="",n.get("showContent")&&n.get("show")&&(u=this._tooltipContent,h=n.get("formatter"),a=a||n.get("position"),t=t,c=this._getNearestPoint([r,o],i,n.get("trigger"),n.get("borderColor")).color,h&&(t=V(h)?(p=n.ecModel.get("useUTC"),t=h,dp(t=(d=F(i)?i[0]:i)&&d.axisType&&0<=d.axisType.indexOf("time")?Gc(d.axisValue,t,p):t,i,!0)):S(h)?(d=ct(function(t,e){t===this._ticket&&(u.setContent(e,l,n,c,a),this._updatePosition(n,a,r,o,u,i,s))},this),this._ticket=e,h(i,e,d)):h),u.setContent(t,l,n,c,a),u.show(n,c),this._updatePosition(n,a,r,o,u,i,s))},mS.prototype._getNearestPoint=function(t,e,n,i){return"axis"===n||F(e)?{color:i||("html"===this._renderMode?"#fff":"none")}:F(e)?void 0:{color:i||e.color||e.borderColor}},mS.prototype._updatePosition=function(t,e,n,i,r,o,a){var s,l=this._api.getWidth(),u=this._api.getHeight(),h=(e=e||t.get("position"),r.getSize()),c=t.get("align"),p=t.get("verticalAlign"),d=a&&a.getBoundingRect().clone();a&&d.applyTransform(a.transform),F(e=S(e)?e([n,i],o,r.el,d,{viewSize:[l,u],contentSize:h.slice()}):e)?(n=Gr(e[0],l),i=Gr(e[1],u)):R(e)?((o=e).width=h[0],o.height=h[1],n=(o=wp(o,{width:l,height:u})).x,i=o.y,p=c=null):i=(n=(s=V(e)&&a?function(t,e,n,i){var r=n[0],o=n[1],a=Math.ceil(Math.SQRT2*i)+8,s=0,l=0,u=e.width,h=e.height;switch(t){case"inside":s=e.x+u/2-r/2,l=e.y+h/2-o/2;break;case"top":s=e.x+u/2-r/2,l=e.y-o-a;break;case"bottom":s=e.x+u/2-r/2,l=e.y+h+a;break;case"left":s=e.x-r-a,l=e.y+h/2-o/2;break;case"right":s=e.x+u+a,l=e.y+h/2-o/2}return[s,l]}(e,d,h,t.get("borderWidth")):function(t,e,n,i,r,o,a){var n=n.getSize(),s=n[0],n=n[1];null!=o&&(i<t+s+o+2?t-=s+o:t+=o);null!=a&&(r<e+n+a?e-=n+a:e+=a);return[t,e]}(n,i,r,l,u,c?null:20,p?null:20))[0],s[1]),c&&(n-=xS(c)?h[0]/2:"right"===c?h[0]:0),p&&(i-=xS(p)?h[1]/2:"bottom"===p?h[1]:0),$b(t)&&(o=n,a=i,e=l,d=u,c=(c=r).getSize(),p=c[0],c=c[1],o=Math.min(o+p,e)-p,a=Math.min(a+c,d)-c,o=Math.max(o,0),a=Math.max(a,0),n=(s=[o,a])[0],i=s[1]),r.moveTo(n,i)},mS.prototype._updateContentNotChangedOnAxis=function(n,o){var t=this._lastDataByCoordSys,a=this._cbParamsList,s=!!t&&t.length===n.length;return s&&O(t,function(t,e){var t=t.dataByAxis||[],r=(n[e]||{}).dataByAxis||[];(s=s&&t.length===r.length)&&O(t,function(t,e){var e=r[e]||{},n=t.seriesDataIndices||[],i=e.seriesDataIndices||[];(s=s&&t.value===e.value&&t.axisType===e.axisType&&t.axisId===e.axisId&&n.length===i.length)&&O(n,function(t,e){e=i[e];s=s&&t.seriesIndex===e.seriesIndex&&t.dataIndex===e.dataIndex}),a&&O(t.seriesDataIndices,function(t){var t=t.seriesIndex,e=o[t],t=a[t];e&&t&&t.data!==e.data&&(s=!1)})})}),this._lastDataByCoordSys=n,this._cbParamsList=o,!!s},mS.prototype._hide=function(t){this._lastDataByCoordSys=null,t({type:"hideTip",from:this.uid})},mS.prototype.dispose=function(t,e){!b.node&&e.getDom()&&(Zg(this,"_updatePosition"),this._tooltipContent.dispose(),Nb("itemTooltip",e))},mS.type="tooltip",mS);function mS(){var t=null!==fS&&fS.apply(this,arguments)||this;return t.type=mS.type,t}function vS(t,e,n){for(var i=e.ecModel,r=n?(r=new xc(n,i,i),new xc(e.option,r,i)):e,o=t.length-1;0<=o;o--){var a=t[o];(a=a&&(V(a=a instanceof xc?a.get("tooltip",!0):a)?{formatter:a}:a))&&(r=new xc(a,r,i))}return r}function _S(t,e){return t.dispatchAction||ct(e.dispatchAction,e)}function xS(t){return"center"===t||"middle"===t}__(function(t){__(qb),t.registerComponentModel(jb),t.registerComponentView(yS),t.registerAction({type:"showTip",event:"showTip",update:"tooltip:manuallyShowTip"},Et),t.registerAction({type:"hideTip",event:"hideTip",update:"tooltip:manuallyHideTip"},Et)}),__(B1);var wS={value:"eq","<":"lt","<=":"lte",">":"gt",">=":"gte","=":"eq","!=":"ne","<>":"ne"},bS=(SS.prototype.evaluate=function(t){var e=typeof t;return V(e)?this._condVal.test(t):!!ft(e)&&this._condVal.test(t+"")},SS);function SS(t){null==(this._condVal=V(t)?new RegExp(t):xt(t)?t:null)&&f("")}TS.prototype.evaluate=function(){return this.value};var MS=TS;function TS(){}IS.prototype.evaluate=function(){for(var t=this.children,e=0;e<t.length;e++)if(!t[e].evaluate())return!1;return!0};var CS=IS;function IS(){}DS.prototype.evaluate=function(){for(var t=this.children,e=0;e<t.length;e++)if(t[e].evaluate())return!0;return!1};var kS=DS;function DS(){}PS.prototype.evaluate=function(){return!this.child.evaluate()};var AS=PS;function PS(){}OS.prototype.evaluate=function(){for(var t=!!this.valueParser,e=(0,this.getValue)(this.valueGetterParam),n=t?this.valueParser(e):null,i=0;i<this.subCondList.length;i++)if(!this.subCondList[i].evaluate(t?n:e))return!1;return!0};var LS=OS;function OS(){}function RS(t,e){if(!0===t||!1===t)return(n=new MS).value=t,n;var n;if(zS(t)||f(""),t.and)return NS("and",t,e);if(t.or)return NS("or",t,e);if(t.not)return n=e,zS(o=(o=t).not)||f(""),(l=new AS).child=RS(o,n),l.child||f(""),l;for(var i=t,r=e,o=r.prepareGetValue(i),a=[],s=ht(i),l=i.parser,u=l?Cf(l):null,h=0;h<s.length;h++){var c,p=s[h];"parser"===p||r.valueGetterAttrMap.get(p)||(c=zt(wS,p)?wS[p]:p,p=i[p],p=u?u(p):p,(c=function(t,e){return"eq"===t||"ne"===t?new Lf("eq"===t,e):zt(If,t)?new kf(t,e):null}(c,p)||"reg"===c&&new bS(p))||f(""),a.push(c))}return a.length||f(""),(l=new LS).valueGetterParam=o,l.valueParser=u,l.getValue=r.getValue,l.subCondList=a,l}function NS(t,e,n){e=e[t],F(e)||f(""),e.length||f(""),t=new("and"===t?CS:kS);return t.children=B(e,function(t){return RS(t,n)}),t.children.length||f(""),t}function zS(t){return R(t)&&!st(t)}BS.prototype.evaluate=function(){return this._cond.evaluate()};var ES=BS;function BS(t,e){this._cond=RS(t,e)}var FS={type:"echarts:filter",transform:function(t){for(var e,n,i=t.upstream,r=(t=t.config,n={valueGetterAttrMap:z({dimension:!0}),prepareGetValue:function(t){var e=t.dimension,t=(zt(t,"dimension")||f(""),i.getDimensionInfo(e));return t||f(""),{dimIdx:t.index}},getValue:function(t){return i.retrieveValueFromItem(e,t.dimIdx)}},new ES(t,n)),o=[],a=0,s=i.count();a<s;a++)e=i.getRawDataItem(a),r.evaluate()&&o.push(e);return{data:o}}},VS={type:"echarts:sort",transform:function(t){for(var a=t.upstream,t=t.config,t=ao(t),s=(t.length||f(""),[]),t=(O(t,function(t){var e=t.dimension,n=t.order,i=t.parser,t=t.incomparable,e=(null==e&&f(""),"asc"!==n&&"desc"!==n&&f(""),t&&"min"!==t&&"max"!==t&&f(""),"asc"!==n&&"desc"!==n&&f(""),a.getDimensionInfo(e)),r=(e||f(""),i?Cf(i):null);i&&!r&&f(""),s.push({dimIdx:e.index,parser:r,comparator:new Af(n,t)})}),a.sourceFormat),e=(t!==Np&&t!==zp&&f(""),[]),n=0,i=a.count();n<i;n++)e.push(a.getRawDataItem(n));return e.sort(function(t,e){for(var n=0;n<s.length;n++){var i=s[n],r=a.retrieveValueFromItem(t,i.dimIdx),o=a.retrieveValueFromItem(e,i.dimIdx),i=(i.parser&&(r=i.parser(r),o=i.parser(o)),i.comparator.evaluate(r,o));if(0!==i)return i}return 0}),{data:e}}};__(function(t){t.registerTransform(FS),t.registerTransform(VS)}),t.Axis=mc,t.ChartView=zg,t.ComponentModel=g,t.ComponentView=Pg,t.List=iv,t.Model=xc,t.PRIORITY=Ay,t.SeriesModel=Sg,t.color=Kn,t.connect=function(e){var t;return F(e)&&(t=e,e=null,O(t,function(t){null!=t.group&&(e=t.group)}),e=e||"g_"+h0++,O(t,function(t){t.group=e})),l0[e]=!0,e},t.dataTool={},t.dependencies={zrender:"5.3.0"},t.disConnect=p0,t.disconnect=Dy,t.dispose=function(t){V(t)?t=s0[t]:t instanceof qm||(t=d0(t)),t instanceof qm&&!t.isDisposed()&&t.dispose()},t.env=b,t.extendChartView=function(t){return t=zg.extend(t),zg.registerClass(t),t},t.extendComponentModel=function(t){return t=g.extend(t),g.registerClass(t),t},t.extendComponentView=function(t){return t=Pg.extend(t),Pg.registerClass(t),t},t.extendSeriesModel=function(t){return t=Sg.extend(t),Sg.registerClass(t),t},t.format=gc,t.getCoordinateSystemDimensions=function(t){if(t=hd.get(t))return t.getDimensionsInfo?t.getDimensionsInfo():t.dimensions.slice()},t.getInstanceByDom=d0,t.getInstanceById=function(t){return s0[t]},t.getMap=function(t){var e=gm.getMap;return e&&e(t)},t.graphic=zc,t.helper=Py,t.init=function(t,e,n){var i=!(n&&n.ssr);if(i){var r=d0(t);if(r)return r}return(r=new qm(t,e,n)).id="ec_"+u0++,s0[r.id]=r,i&&To(t,c0,r.id),Wm(r),fm.trigger("afterinit",r),r},t.innerDrawElementOnCanvas=sm,t.matrix=Ri,t.number=Fo,t.parseGeoJSON=z_,t.parseGeoJson=z_,t.registerAction=x0,t.registerCoordinateSystem=w0,t.registerLayout=b0,t.registerLoading=C0,t.registerLocale=Ac,t.registerMap=I0,t.registerPostInit=m0,t.registerPostUpdate=v0,t.registerPreprocessor=g0,t.registerProcessor=y0,t.registerTheme=f0,t.registerTransform=k0,t.registerUpdateLifecycle=_0,t.registerVisual=S0,t.setCanvasCreator=function(t){D({createCanvas:t})},t.setPlatformAPI=D,t.throttle=Yg,t.time=Nh,t.use=__,t.util=cc,t.vector=ne,t.version="5.3.0",t.zrUtil=Ft,t.zrender=Fr});