<template>
    <view class="page_root">
        <view class="no_data" v-if="total === 0">未查询到相关订单</view>
        <view wx:else>
            <view class="collect_item_box" v-for="(item, index) in orders" :key="index">
                <view class="distance">
                    <text>距离你：{{item.distance.label}}</text>
                    <button class="collect_btn" @click="toCollectInput(index)">录入信息</button>
                </view>
                <order-item :order="item" :no-border="true"></order-item>
                <view class="action_btn">
                    <view class="btn left" @click="makePhone(index)">联系用户</view>
                    <view class="btn right" @click="showGuide(index)">查看导航</view>
                </view>
            </view>
            <view class="loading" v-if="loading"></view>
            <view class="loaded_finish" v-else-if="total > 0 && total <= (pageIndex + 1) * pageSize">共{{total}}条订单，已加载完毕</view>
        </view>
    </view>
</template>

<script>
import orderManager from '../../api/orderManager'
import messageBox from '../../utils/messageBox'
import gps from '../../utils/gps'
import router from '../../router'
import OrderItem from '../../components/OrderItem.vue'
export default {
    components: {
        OrderItem
    },
    data () {
        return {
            loading: false,
            total: -1,
            pageIndex: 0,
            pageSize: 10,
            orders: [],
            latitude: null,
            longitude: null,
        }
    },
    onLoad () {
        messageBox.toast.loading('请选择位置').then(() => {
            this.pickAddress();
        })
    },
    onShow: function () {
        orderManager.queryCollectOrders(this.pageIndex, this.pageSize).then(res => {
            const { total, list } = res;
            const orders = list.map(order => {
                order.distance = gps.getDistance(order.latitude, order.longitude, this.latitude, this.longitude);
                return order;
            });
            this.orders = orders;
            this.total = total;
        }).catch(err => {
            messageBox.alert(err);
        })
    },
    methods: {
        pickAddress () {
            uni.chooseLocation({
                success: (res) => {
                    const { latitude, longitude } = res;
                    const orders = this.orders.map(order => {
                        order.distance = gps.getDistance(order.latitude, order.longitude, latitude, longitude);
                        return order;
                    });
                    this.latitude = latitude;
                    this.longitude = longitude;
                    this.orders = orders;
                },
                fail: (err) => {
                    messageBox.alert('位置信息获取失败')
                }
            })
        },
        loadMoreOrders () {
            this.moreLoading = true;
            orderManager.queryCollectOrders(this.pageIndex + 1, this.pageSize).then(res => {
                const { total, list } = res;
                const orders = list.map(order => {
                    order.distance = gps.getDistance(order.latitude, order.longitude, this.latitude, this.longitude);
                    return order;
                });
                this.moreLoading = false
                this.pageIndex = this.pageIndex + 1
                this.orders = this.orders.concat(orders)
                this.total = total
            }).catch(err => {
                messageBox.alert(err);
                this.moreLoading = false;
            })
        },
        toCollectInput (index) {
            router.startEditOrder(this.orders[index])
        },
        makePhone (index) {
            const { mobile } = this.orders[index];
            uni.makePhoneCall({
                phoneNumber: mobile
            })
        },
        showGuide (index) {
            const { latitude, longitude } = this.orders[index];
            uni.openLocation({
                latitude,
                longitude,
                scale: 16
            });
        },
        onReachBottom: function () {
            const { total, pageIndex, pageSize } = this;
            if (total > (pageIndex + 1) * pageSize && !this.moreLoading) {
                this.loadMoreOrders()
            }
        }
    }
}
</script>


<style lang="scss">
page {
    background-color: #f5f5f5;
}
.page_root {
    padding: 15px 0;
}
.collect_item_box {
    margin-bottom: 20px;
}
.distance {
    background-color: #fff;
    border-bottom: 1px solid #f1f1f1;
    padding: 8px 15px;
    font-size: 14px;
    line-height: 32px;
    color: yellowgreen;
    position: relative;
    .collect_btn {
        width: 80px;
        height: 32px;
        font-size: 14px;
        line-height: 32px;
        padding: 0;
        float: right;
        font-weight: normal;
        background-color: #ff6a01;
        color: #fff;
    }
}
.action_btn {
    height: 44px;
    line-height: 44px;
    border-top: 1px solid #f1f1f1;
    font-size: 0;
    background-color: #fff;
    text-align: center;
    .btn {
        display: inline-block;
        font-size: 14px;
        width: 50%;
        &.left {
            box-sizing: border-box;
            border-right: 1px solid #f1f1f1;
        }
        &.right {
            color: #5cb87a;
        }
    }
}
.loaded_finish {
    font-size: 14px;
    color: #8a96a3;
    text-align: center;
    margin-top: 15px;
}

.loading {
    margin-top: 10px;
    height: 40px;
}

.btn_box {
    border-top: 1px solid #f3f3f3;
    margin-top: 15px;
    .btn {
        display: inline-block;
        font-size: 15px;
        line-height: 40px;
        width: 50%;
        text-align: center;
        border-radius: 3px;
        &.btn1 {
            position: relative;
            color: #5cb87a;
            &::after {
                content: '';
                width: 1px;
                height: 100%;
                background: #f3f3f3;
                position: absolute;
                top: 0;
                right: 0;
            }
        }
        &.btn2 {
            color: #409eff;
        }
    }
}
</style>