<template>
  <div style="position: relative">
    <div class="watermark"></div>
    <div class="title_box">
      <span>{{ cropName }}工厂</span>
      <span class="label_price">价格</span>
      <span class="fr day_switch" @click="() => (showYesterday = !showYesterday)"
        >{{ showYesterday ? '查看今日' : '查看昨日' }}⇅</span
      >
    </div>
    <price-item
      v-for="item in renderPriceArr"
      :key="item.companyId"
      :priceItem="item"
      :companyUpdated="item.updateTime > priceItemZeroTime"
      @item-click="onCompanyClick(item)"
    ></price-item>
    <ad
      unit-id="adunit-7e4351e1e1b91210"
      ad-type="video"
      ad-theme="white"
      bindload="adLoad"
      binderror="adError"
      bindclose="adClose"
    ></ad>
  </div>
</template>

<script>
import PriceItem from '../../../components/PriceItem.vue'

const todayZeroTime = new Date().setHours(0, 0, 0, 0)

export default {
  components: {
    PriceItem
  },
  props: {
    cropId: {
      type: Number,
      default: null
    },
    cropName: {
      type: String,
      default: ''
    },
    companyMaps: {
      type: Object,
      default: () => {}
    },
    areaCompanyData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      showYesterday: Date.now() - todayZeroTime < 6 * 3600 * 1000 // 早上六点前优先展示昨日价格
    }
  },
  computed: {
    priceItemZeroTime() {
      // 对于判断价格所属企业是否有更新数据的时间节点
      return this.showYesterday ? todayZeroTime - 3600 * 1000 * 24 : todayZeroTime
    },
    renderPriceArr() {
      const arr = this.showYesterday
        ? this.areaCompanyData?.yesterdayPriceList
        : this.areaCompanyData?.priceList
      return arr?.map(item => ({ ...item, companyName: this.companyMaps[item.companyId].name }))
    }
  },
  created() {},
  methods: {
    onCompanyClick(item) {
      uni.navigateTo({
        url: `/pages/index/company?cropId=${this.cropId}&cropName=${this.cropName}&id=${item.companyId}&name=${item.companyName}`
      })
    }
  }
}
</script>

<style scoped lang="scss">
.title_box {
  border-bottom: 1px solid #f1f1f1;
  font-size: 14px;
  color: #666;
  padding: 10px 16px;
  line-height: 20px;
  position: relative;
  .label_price {
    position: absolute;
    left: 50%;
    top: 10px;
  }
  .day_switch {
    color: #ff6a01;
  }
}

.watermark {
  position: absolute;
}
</style>
