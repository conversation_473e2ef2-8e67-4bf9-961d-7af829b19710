// import { i } from '@dcloudio/vue-cli-plugin-uni/packages/postcss/tags'
import http from '../utils/http'

function orderPhotoFormat(item) {
  Object.keys(item).forEach(key => {
    if (key.match(/photo|video|Photo|Video/)) {
      item[key] = item[key].split(',')
    }
  })
  return item
}

function mapOrder(orders) {
  return orders.map(item => orderPhotoFormat(item))
}

function queryCompanyPrice(countDays, companyId) {
  return http.get(`/price/latest/${countDays}`, { companyId })
}
function queryAllCompanies() {
  return http.get('/price/company/all').then(data => {
    data.forEach(item => {
      if (item.detail) {
        item.detail = JSON.parse(item.detail.replace(/\n/g, '\\n').replace(/\r/g, '\\r'))
      }
    })
    return data
  })
}
function queryLatest(countDays, cropId) {
  return http.get(`/price/latest/${countDays}`, { cropId })
}
function queryCompanyLatestDetail(companyId) {
  return http.get(`/price/company/${companyId}/details`)
}
function queryCompanyUpdatedFromYesterday() {
  return http.get(`/price/company/updated/fromYesterday`)
}

function queryPortDetail() {
  return http.get('/price/port/latest')
}

// 查询首页的订单
function queryHomeTradeOrders(pageIndex, pageSize) {
  return http.post('/order/tradeList', { pageIndex, pageSize }).then(res => {
    res.list.forEach(trade => {
      trade.order = orderPhotoFormat(trade.order)
    })
    res.list = mapOrder(res.list)
    return res
  })
}

function getChartOption(xAxis, series) {
  // 构建 echart 的数据
  let min = 1000
  let max = 0
  const arr = []
  series.forEach(item => {
    arr.push(item.name)
    item.data = item.data.map(price => {
      price = Math.round(price * 1000) / 1000
      if (price < min) {
        min = price
      }
      if (price > max) {
        max = price
      }
      return price
    })
  })
  min = Math.floor(min * 100) / 100
  max = Math.ceil(max * 100) / 100
  if (min === max) {
    min = (min * 1000 - 10) / 1000
    max = (max * 1000 + 10) / 1000
  }
  return {
    tooltip: {
      trigger: 'axis',
      textStyle: {
        textShadowBlur: 0
      },
      renderMode: 'richText'
    },
    legend: {
      show: true
    },
    digit: 3,
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xAxis
    },
    yAxis: {
      name: '元/斤',
      type: 'value',
      min,
      max,
      axisLabel: {
        formatter: function (value) {
          return value.toFixed(2)
        }
      }
    },
    series
  }
}

// 构建 echart 的数据，
// 假如最后一个系列的价格数据存在较大的差距时，需要追加偏移量，让图标中的数据展示幅度更大
function getFixChartOption(xAxis, series) {
  let min = 1000
  let max = 0
  let normalMin = 1000 // 非村里价格的最小值
  let lastMax = 0
  let lastOffValue = 0 // 最后一个系列的价格的值是否需要偏移量
  const arr = []
  series.forEach((item, idx) => {
    let isLast = idx === series.length - 1 // 村里价格的系列价
    arr.push(item.name)
    item.smooth = true // 将折线图转换成平滑曲线绘制
    item.symbol = 'circle'
    item.data = item.data.map(price => {
      price = Math.round(price * 1000) / 1000
      if (price < min) {
        min = price
      }
      if (price > max) {
        max = price
      }
      if (isLast) {
        price > lastMax && (lastMax = price)
      } else {
        price < normalMin && (normalMin = price)
      }
      return price
    })
  })
  if (series.length > 1 && normalMin - lastMax > 0.02) {
    lastOffValue = Math.floor((normalMin - lastMax - 0.01) * 100) / 100
  }
  if (lastOffValue > 0) {
    // 计算出最后一个系列需要对数据进行追加偏移量处理
    series[series.length - 1].data = series[series.length - 1].data.map(price => {
      return Math.round((price + lastOffValue) * 1000) / 1000
    })
  }
  min = min + lastOffValue
  min = Math.floor(min * 100) / 100
  max = Math.ceil(max * 100) / 100
  if (min === max) {
    min = (min * 1000 - 10) / 1000
    max = (max * 1000 + 10) / 1000
  }

  const selected = arr.reduce((acc, item, index) => {
    acc[item] = index < 3 || item.includes('理论') // 前三个图例项以及包含“理论”的图例项选中，其余不选中
    return acc
  }, {})
  return {
    tooltip: {
      trigger: 'axis',
      textStyle: {
        textShadowBlur: 0
      },
      renderMode: 'richText',
      valueFormatter: function (value) {
        if (lastOffValue > 0) {
          if (value <= lastMax + lastOffValue + 0.001) {
            return Math.round((value - lastOffValue) * 1000) / 1000
          } else {
            return value
          }
        } else {
          return value
        }
      }
    },
    legend: {
      data: arr,
      selectedMode: true,
      selected,
      itemWidth: 10,
      itemHeight: 10,
      itemGap: 10,
      icon: 'circle',
      formatter: function (name) {
        if (series.find(item => item.picker) && series.find(item => item.picker).name === name) {
          return name.replace('出粒', '').replace('测量', '') + '▼'
        }
        return name.replace('出粒', '').replace('测量', '')
      }
    },
    digit: 3,
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xAxis
    },
    yAxis: {
      name: '元/斤',
      nameTextStyle: {
        align: 'right'
      },
      type: 'value',
      min,
      max,
      axisLabel: {
        formatter: function (value) {
          if (lastOffValue > 0 && value < lastMax + lastOffValue + 0.01) {
            value = value - lastOffValue
          }
          return value.toFixed(2)
        }
      }
    },
    series
  }
}

function queryLocalPrices(params) {
  return http.get(`/price/report`, params)
}

function postLocalPrices(params) {
  return http.post(`/price/report`, params)
}

function delLocalPrices(id) {
  return http.delete(`/price/report/${id}`)
}

function queryPriceConfigs(params) {
  return http.get(`/price/config`, params)
}

function queryPriceChartData(id, params) {
  return http.get(`/price/chartData/home/<USER>
}

// 查询单独企业的数据
function queryCompanyPriceChartData(id, cropId) {
  return http.get(`/price/chartData/company/${id}`, { cropId }, { needSign: true })
}

export default {
  queryCompanyPrice,
  queryAllCompanies,
  queryLatest,
  queryPortDetail,
  queryCompanyLatestDetail,
  queryCompanyUpdatedFromYesterday,
  queryHomeTradeOrders,
  getChartOption,
  getFixChartOption,
  queryLocalPrices,
  postLocalPrices,
  delLocalPrices,
  queryPriceConfigs,
  queryPriceChartData,
  queryCompanyPriceChartData
}
