<template>
    <view>
        <view class="chat_root">
            <view class="msg_item" v-for="(item, index) in renderMessageArr" :key="index">
                <view :class="[item.isMine ? 'my_message' : 'his_message', item.content ? '' : ' order_message']">
                    <image class="photo" mode="aspectFill" :src="item.avatar" />
                    <view class="content" v-if="item.content">{{item.content}}</view>
                    <view class="order_box" v-else-if="item.orderId" bind:tap="onOrderClick">
                        <order-item :order="item.order" no-margin="true"></order-item>
                    </view>
                </view>
                <view v-if="item.isMine" class="read_status">
                    <view v-if="item.status" class="status yes">已读</view>
                    <view wx:else class="status not">未读</view>
                </view>
            </view>
            <view class="order_box recommend_order_box" v-if="showOrderRecommend">
                <order-item :order="askOrder" no-margin="true"></order-item>
                <view class="recommend_send_btn_box">
                    <view class="recommend_send_btn cancel" bind:tap="onCancelRecommend">隐藏</view>
                    <view class="recommend_send_btn" bind:tap="onSendRecommendOrder">发送订单</view>
                </view>
            </view>
        </view>
        <view class="add_message_box">
            <view class="input_box">
                <textarea class="input" :value="msgValue" bindinput="bindMsgInput" :show-confirm-bar="false" auto-height placeholder="请输入内容" />
            </view>
            <view class="send_btn" bind:tap="onSendClick">发送</view>
        </view>
    </view>
</template>

<script>
import msgManager from '../../api/msgManager'
import userManager from '../../api/userManager'
import messageBox from '../../utils/messageBox'
import storeManager from '../../utils/storeManager'
import { ASK_ORDER_NOW } from '../../constant/storeKeys'
import router from '../../router'
export default {
    data () {
        return {
            otherUserId: null,
            otherName: null,
            pageIndex: 0,
            pageSize: 50,
            total: -1,
            loading: false,
            messageArr: [],
            renderMessageArr: [],
            userMap: {},
            orderMap: {},
            msgValue: '',
            myId: '',
            myAvatar: '',
            showOrderRecommend: false,
            askOrder: null
        }
    },
    onLoad: function (options) {
        const otherName = options.otherName ? decodeURIComponent(options.otherName) : ''
        const user = userManager.getUser();
        const { id, avatar } = user;
        this.otherUserId = options.otherUserId;
        this.otherName = otherName;
        this.myId = id;
        this.myAvatar = avatar || '/static/images/user_default.png'
        this.refreshData()
        if (otherName) {
            uni.setNavigationBarTitle({ title: otherName })
        }
    },
    methods: {
        refreshData () {
            uni.showLoading({ mask: true })
            this.setData({
                pageIndex: 0
            })
            msgManager.queryMessageBetweenUser(this.otherUserId, this.pageIndex, this.pageSize).then(data => {
                this.total = data.total;
                this.messageArr = data.list.sort((a, b) => a.id - b.id);
                this.buildData(data.users, data.orders);
                uni.hideLoading()
            }).catch(err => {
                uni.hideLoading()
            })
        },
        buildData (users, orders) { // 根据返回的消息刷新页面数据，根据人进行划分
            const userMap = {};
            const orderMap = {};
            users.forEach(user => {
                if (!userMap[user.id]) {
                    userMap[user.id] = {
                        name: user.name || `千万仓用户_${user.id}`,
                        avatar: user.avatar || '../../images/user_default.png'
                    }
                }
            })
            if (!this.otherName) {
                const otherName = userMap[this.otherUserId].name;
                if (otherName) {
                    uni.setNavigationBarTitle({ title: otherName })
                    this.setData({
                        otherName
                    })
                }
            }
            orders.forEach(order => {
                Object.keys(order).forEach(key => {
                    if (key.match(/photo|video|Photo|Video/)) {
                        order[key] = order[key].split(',')
                    }
                })
                orderMap[order.id] = order;
            })
            const arr = [];
            this.messageArr.forEach(item => {
                const isMine = item.userId === this.myId;
                if (isMine) {
                    arr.push(Object.assign({}, item, {
                        avatar: this.myAvatar,
                        isMine: true,
                        order: orderMap[item.orderId] || null
                    }))
                } else {
                    arr.push(Object.assign({}, item, {
                        avatar: userMap[item.userId].avatar,
                        isMine: false,
                        order: orderMap[item.orderId] || null,
                        status: 1
                    }))
                }
            })
            const askOrder = storeManager.get(ASK_ORDER_NOW); // 从订单过来想要询价的时候缓存下来的订单
            const showOrderRecommend = !!askOrder; // 如果过来到聊天界面后存在询价的订单，显示是否发送订单的弹窗提示
            this.setData({
                renderMessageArr: arr,
                askOrder,
                showOrderRecommend,
                orderMap
            })
            setTimeout(() => {
                uni.pageScrollTo({
                    scrollTop: 100000
                })
            }, 100)
        },
        bindMsgInput (e) {
            this.setData({
                msgValue: e.detail.value
            })
        },
        onSendClick (e) {
            if (this.msgValue) {
                msgManager.addMsg(this.otherUserId, this.msgValue).then(res => {
                    this.setData({
                        msgValue: '',
                        renderMessageArr: this.renderMessageArr.concat({
                            avatar: this.myAvatar,
                            isMine: true,
                            status: 0,
                            content: this.msgValue,
                        })
                    })
                    uni.pageScrollTo({
                        scrollTop: 100000
                    })
                }).catch(err => {
                    messageBox.alert(err)
                });
            } else {
                messageBox.alert('请输入内容')
            }
        },
        onCancelRecommend () {
            storeManager.clear(ASK_ORDER_NOW);
            this.setData({
                showOrderRecommend: false
            })
        },
        onSendRecommendOrder () {
            msgManager.addMsg(this.otherUserId, null, this.askOrder.id).then(res => {
                this.setData({
                    showOrderRecommend: false,
                    renderMessageArr: this.renderMessageArr.concat({
                        avatar: this.myAvatar,
                        isMine: true,
                        status: 0,
                        content: null,
                        orderId: this.askOrder.id,
                        order: this.askOrder
                    })
                })
                storeManager.clear(ASK_ORDER_NOW)
                uni.pageScrollTo({
                    scrollTop: 100000
                })
            }).catch(err => {
                messageBox.alert(err)
            });
        },
        onOrderClick (e) {
            const { id } = e.currentTarget.dataset;
            router.startOrderDetail(this.orderMap[id])
        },
    }
}
</script>