<template>
  <div class="my_nav_bar">
    <div :style="{ height: `${deviceInfo.statusBarHeight || 44}px` }"></div>
    <div class="logo_box" :style="{ height: `${deviceInfo.navBarHeight || 40}px` }">
      <image
        class="logo"
        src="/static/images/logo_index.png"
        mode="widthFix"
        @click="onLogoClick"
      ></image>
    </div>
    <slot></slot>
  </div>
</template>

<script>
export default {
  props: {
    deviceInfo: {
      type: Object,
      default: () => ({})
    }
  },
  methods: {
    onLogoClick() {
      uni.switchTab({
        url: '/pages/index/index'
      })
    }
  }
}
</script>

<style lang="scss">
.my_nav_bar {
  width: 100%;
  background: linear-gradient(120deg, #fff0eb, #ffffff);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1999;
  .logo_box {
    padding-left: 16px;
    display: flex;
    align-items: center;
    // justify-content: center;
  }
  .logo {
    width: 87px;
    height: 32px;
  }
}
</style>
