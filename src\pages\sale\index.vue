<template>
  <view class="sale_page_root" :style="{ paddingTop: `${paddingTopHeight}px` }">
    <navigate-bar :deviceInfo="deviceInfo">
      <tab-menu
        :textArr="['实时订单', '周边订单', '收购商', '设备商']"
        :index.sync="page"
        @change="onMenuChange"
      ></tab-menu>
    </navigate-bar>
    <view class="list_container" :style="{ display: page === 0 ? 'block' : 'none' }">
      <no-data v-if="total1 === 0" />
      <template v-else>
        <div v-for="(item, index) in orders" :key="index">
          <div v-if="item.isVideoAd" class="video_ad_box">
            <ad unit-id="adunit-7e4351e1e1b91210" ad-type="video" ad-theme="white"></ad>
          </div>
          <order-item v-else :order="item" @click="onOrderClick(index)"></order-item>
        </div>
        <view class="loading" v-if="loading1"></view>
        <view class="loaded_finish" v-else-if="total1 > 0 && total1 <= (pageIndex1 + 1) * pageSize">
          共{{ total1 }}条订单，已加载完毕
        </view>
      </template>
    </view>
    <view class="list_container" :style="{ display: page === 1 ? 'block' : 'none' }">
      <view class="address_info_box" @click="pickAddress">
        <image class="icon" src="/static/images/location_icon.png" />
        <view v-if="address" class="label">位置信息：{{ address }}</view>
        <view v-else class="label">
          <text class="no_address">点击获取当前位置，查询周边订单</text>
        </view>
        <view class="switch_icon"></view>
      </view>
      <no-data v-if="total2 === 0" />
      <template v-else>
        <div v-for="(item, index) in nearbyOrders" :key="index">
          <order-item :order="item" @click="onOrderClick(index)"></order-item>
          <!-- <ad v-else unit-id="adunit-7e4351e1e1b91210" ad-type="video" ad-theme="white"></ad> -->
        </div>
        <view class="loading" v-if="loading2"></view>
        <view class="loaded_finish" v-else-if="total2 > 0 && total2 <= (pageIndex2 + 1) * pageSize">
          共{{ total2 }}条订单，已加载完毕
        </view>
      </template>
    </view>
    <view class="list_container" v-if="page === 2 || page === 3">
      <Traders :paddingTopHeight="paddingTopHeight" :activePage="page" />
    </view>
    <view v-if="page === 0 || page === 1" class="add_order_btn" @click="addOrder">
      <image class="icon" mode="aspectFit" src="/static/images/sell.png"></image>
    </view>
  </view>
</template>

<script>
import logManager from '../../api/logManager'
import orderManager from '../../api/orderManager'
import userManager from '../../api/userManager'
import configManager from '../../api/configManager'
import router from '../../router'
import messageBox from '../../utils/messageBox'
import OrderItem from '../../components/OrderItem.vue'
import Traders from '../traders/index.vue'

const app = getApp()
export default {
  components: {
    OrderItem,
    Traders
  },
  data() {
    return {
      deviceInfo: {
        statusBarHeight: 44,
        navBarHeight: 40,
        totalBarHeight: 84
      },
      orders: [],
      priceOrders: [],
      nearbyOrders: [],
      buyersMap: {},
      page: 0,
      pageIndex1: 0, // 最新订单第一页
      pageIndex2: 0, // 周边订单第一页
      pageSize: 10,
      total1: -1,
      total2: -1,
      loading1: false,
      loading2: false,
      pageScrolling: false,
      showOrderBtn: false,
      orderNeedRefresh: false,
      longitude: null,
      latitude: null,
      address: ''
    }
  },
  computed: {
    paddingTopHeight() {
      return (this.deviceInfo.statusBarHeight || 44) + this.deviceInfo.navBarHeight + 50
    }
  },
  onLoad: function () {
    configManager.getDeviceInfo().then(info => {
      this.deviceInfo = info
    })
    this.init()
  },
  onShow() {
    this.showSheetAd()
    if (app.globalData.needRefreshList) {
      this.reloadData()
      app.globalData.needRefreshList = false
    } else if (this.orderNeedRefresh) {
      if (this.ignoreRefresh) {
        this.ignoreRefresh = false
      } else {
        this.reloadData()
      }
    }
  },
  onHide() {
    this.orderNeedRefresh = true
  },
  onShareAppMessage() {
    return {
      title: '千万仓-玉米、小麦价格',
      path: userManager.getSharePath('/pages/sale/index')
    }
  },
  methods: {
    showSheetAd() {
      if (uni.createInterstitialAd) {
        if (this.adInstance) {
          this.adInstance
            .show()
            .then(() => {
              this.adInstance = null
            })
            .catch(err => {
              console.log(err)
            })
          return
        }
        const adInstance = uni.createInterstitialAd({
          adUnitId: 'adunit-3416bcbd923ee6c8'
        })
        const loadListener = () => {
          adInstance
            .show()
            .then(() => {
              this.adInstance = null
            })
            .catch(err => {
              // 广告展示失败，暂存实例
              this.adInstance = adInstance
            })
        }
        const closeListener = () => {
          adInstance.destroy()
        }
        adInstance.onLoad(loadListener)
        adInstance.onClose(closeListener)
        adInstance.load()
      }
    },
    onMenuChange(index) {
      // 菜单切换到订单列表重新拉取插屏广告
      if (index === 0) {
        this.showSheetAd()
      }
      uni.pageScrollTo({
        scrollTop: 0
      })
    },
    reloadData() {
      if (this.page === 0) {
        this.loadOrders()
      } else if (this.page === 1) {
        this.loadNearByOrders()
      }
      this.goTop()
    },
    init() {
      if (this.page === 0) {
        logManager.addLog('order_page_view', '查看实时订单')
        this.loadOrders()
      } else if (this.page === 1) {
        logManager.addLog('order_page_view', '查看周边订单')
        this.loadNearByOrders()
      }
    },
    switchPage(event) {
      let page = parseInt(event.currentTarget.dataset.page)
      this.page = page
      if (page === 0) {
        logManager.addLog('order_page_view', '查看实时订单')
        this.loadOrders()
      } else if (page === 1) {
        logManager.addLog('order_page_view', '查看周边订单')
        this.loadNearByOrders()
      }
      this.goTop()
    },
    loadOrders() {
      this.pageIndex1 = 0
      this.loading1 = true
      orderManager.queryHomeOrders(this.pageIndex1, this.pageSize).then(res => {
        this.total1 = res.total
        this.loading1 = false
        const orders = res.list
        // 给数据中的第三条加入视频广告
        // orders.splice(3, 1, { isVideoAd: true })
        this.orders = orders
      })
    },

    loadMoreOrders() {
      this.pageIndex1 = this.pageIndex1 + 1
      this.loading1 = true
      orderManager.queryHomeOrders(this.pageIndex1, this.pageSize).then(res => {
        this.total1 = res.total
        this.orders = this.orders.concat(res.list)
        this.loading1 = false
      })
    },

    loadNearByOrders() {
      if (this.latitude) {
        this.pageIndex2 = 0
        this.loading2 = true
        orderManager
          .queryNearbyOrders(this.longitude, this.latitude, this.pageIndex2, this.pageSize)
          .then(res => {
            this.total2 = res.total
            this.nearbyOrders = res.list
            this.loading2 = false
          })
      } else {
        messageBox.toast.loading('请选择位置').then(() => {
          this.pickAddress()
        })
      }
    },

    loadMoreNearByOrders() {
      this.pageIndex2 = this.pageIndex2 + 1
      this.loading2 = true
      orderManager
        .queryNearbyOrders(this.longitude, this.latitude, this.pageSize, this.pageIndex2)
        .then(res => {
          this.total2 = res.total
          this.nearbyOrders = this.nearbyOrders.concat(res.list)
          this.loading2 = false
        })
    },

    pickAddress() {
      this.ignoreRefresh = true //进入地理位置选择页面
      uni.chooseLocation({
        success: res => {
          this.longitude = res.longitude
          this.latitude = res.latitude
          this.address = res.address
          this.loadNearByOrders()
        },
        fail: err => {
          console.log(err)
          messageBox.alert('位置信息获取失败')
        }
      })
    },

    addOrder() {
      uni.switchTab({
        url: '/pages/order/index'
      })
    },

    onOrderClick(index) {
      const order = this.page === 0 ? this.orders[index] : this.nearbyOrders[index]
      this.ignoreRefresh = true //进入订单详情页忽略返回刷新
      router.startOrderDetail(order, {
        orderUpdate: item => {
          this.onMyOrderUpdate(item, index)
        }
      })
    },
    onMyOrderUpdate(order, index) {
      if (this.page === 0) {
        this.orders.splice(index, 1)
        // this.orders = this.orders;
      } else {
        this.nearbyOrders.splice(index, 1)
        // this.setData({
        //     nearbyOrders: this.nearbyOrders
        // })
      }
    },
    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom: function () {
      const { page, loading1, loading2, total1, total2, pageIndex1, pageIndex2, pageSize } = this
      if (!loading1 && !loading2) {
        if (page === 0) {
          if (total1 > (pageIndex1 + 1) * pageSize) {
            this.loadMoreOrders()
          }
        } else if (page === 1) {
          if (total2 > (pageIndex2 + 1) * pageSize) {
            this.loadMoreNearByOrders()
          }
        }
      }
    },

    goTop: function (e) {
      // 一键回到顶部
      if (uni.pageScrollTo) {
        uni.pageScrollTo({
          scrollTop: 0
        })
      }
    }
  }
}
</script>

<style lang="scss">
.sale_page_root {
  padding-bottom: 80px;
}

.address_info_box {
  background-color: #f6f6f6;
  margin-bottom: 20px;
  padding: 15px 41px 15px 49px;
  font-size: 14px;
  line-height: 20px;
  position: relative;
  .icon {
    width: 24px;
    height: 24px;
    margin-top: -12px;
    position: absolute;
    top: 50%;
    left: 15px;
  }
  .switch_icon {
    background-image: url("data:image/svg+xml,%3Csvg width='100' height='99' viewBox='0 0 100 99' fill='none' xmlns='http://www.w3.org/2000/svg'%3E %3Cpath d='M20.3805 24.1657H93.5675C95.2359 24.1657 96.8359 24.8285 98.0156 26.0082C99.1952 27.1878 99.858 28.7878 99.858 30.4562C99.858 32.1245 99.1952 33.7245 98.0156 34.9042C96.8359 36.0839 95.2359 36.7466 93.5675 36.7466H7.13699C5.72331 36.7482 4.34109 36.3294 3.16598 35.5435C1.99087 34.7576 1.07594 33.6401 0.537456 32.333C-0.00102827 31.0259 -0.13875 29.5882 0.141796 28.2027C0.422342 26.8171 1.10849 25.5462 2.11303 24.5516L25.0438 1.82212C25.6308 1.24056 26.3267 0.780333 27.0916 0.467706C27.8565 0.155078 28.6755 -0.00382426 29.5019 6.98771e-05C30.3282 0.00396402 31.1457 0.170579 31.9076 0.490402C32.6696 0.810225 33.3611 1.27699 33.9426 1.86405C34.5242 2.45112 34.9844 3.14697 35.297 3.9119C35.6097 4.67683 35.7686 5.49584 35.7647 6.32217C35.7608 7.14851 35.5942 7.96599 35.2743 8.72793C34.9545 9.48988 34.4877 10.1814 33.9007 10.7629L20.3888 24.1657H20.3805ZM79.6279 74.2124H6.43246C4.76414 74.2124 3.16414 73.5497 1.98445 72.37C0.804769 71.1903 0.14203 69.5903 0.14203 67.922C0.14203 66.2537 0.804769 64.6537 1.98445 63.474C3.16414 62.2943 4.76414 61.6316 6.43246 61.6316H92.8714C94.2844 61.6317 95.6654 62.0516 96.8392 62.8382C98.0131 63.6247 98.9267 64.7423 99.4641 66.049C100.002 67.3558 100.139 68.7928 99.8578 70.1775C99.577 71.5623 98.891 72.8324 97.887 73.8266L74.9562 96.5477C74.3727 97.1447 73.6761 97.6196 72.9072 97.9446C72.1382 98.2697 71.3122 98.4384 70.4774 98.441C69.6425 98.4436 68.8155 98.28 68.0445 97.9597C67.2736 97.6395 66.5741 97.1689 65.9868 96.5755C65.3995 95.9821 64.9363 95.2778 64.624 94.5035C64.3117 93.7293 64.1567 92.9006 64.168 92.0659C64.1792 91.2311 64.3565 90.4069 64.6895 89.6413C65.0226 88.8758 65.5047 88.1842 66.1077 87.6069L79.6279 74.2124Z' fill='%23333333'/%3E %3C/svg%3E ");
    background-size: 100% auto;
    background-position: 50%;
    background-repeat: no-repeat;
    position: absolute;
    width: 16px;
    height: 16px;
    margin-top: -8px;
    top: 50%;
    right: 15px;
  }
}

.no_address {
  color: #ff6a01;
}

.loaded_finish {
  font-size: 14px;
  color: #8a96a3;
  text-align: center;
  margin-top: 15px;
}

.loading {
  margin-top: 10px;
  height: 40px;
}

.add_order_btn {
  width: 60px;
  height: 60px;
  position: fixed;
  right: 10px;
  bottom: 10px;
  .icon {
    width: 100%;
    height: 100%;
  }
  transition: all ease-out 0.3s;
}
.video_ad_box {
  border-bottom: 12px solid #f6f6f6;
}
</style>
