<template>
  <div class="data_sheet">
    <div class="price_box">
      <div style="flex-shrink: 0">
        <span class="num big">{{
          (data.priceChange >= 0 ? '+' : '') + formatNum(data.priceChange)
        }}</span>
        <span class="num">{{ formatNum(data.priceAverage) }}</span>
        <span class="num">{{ formatNum(calculatePriceChangePercentage) }}%</span>
        <img
          v-if="data.priceChange !== 0"
          class="icon"
          :src="`/static/images/icon_${data.priceChange > 0 ? 'up' : 'down'}.png`"
          alt="千万仓"
        />
      </div>
      <div class="label">均价(元)</div>
    </div>
    <div class="factory_box">
      <div>
        <span class="label">厂价：</span>
        <span class="txt">{{ formatNum(data.upTotal) }}<text>涨</text></span>
        <span class="txt last">{{ formatNum(data.downTotal) }}<text>跌</text></span>
        <span class="txt last">{{ formatNum(data.sameTotal) }}<text>平</text></span>
      </div>
      <div v-if="cropId === 1" class="tap_right" @click="onNavigateToTransportDetail(0)">
        <span class="label">厂车：</span>
        <span class="txt">{{ formatNum(statsData[0].totalToday) }}<text>车</text></span>
        <span class="txt last"
          >{{ (statsData[0].totalChange >= 0 ? '+' : '') + formatNum(statsData[0].totalChange)
          }}<text>车</text></span
        >
      </div>
      <div v-if="cropId === 1" class="tap_right" @click="onNavigateToTransportDetail(1)">
        <span class="label">锦州港：</span>
        <span class="txt">{{ formatNum(statsData[1].totalToday) }}<text>车</text></span>
        <span class="txt last"
          >{{ (statsData[1].totalChange >= 0 ? '+' : '') + formatNum(statsData[1].totalChange)
          }}<text>车</text></span
        >
      </div>
    </div>
  </div>
</template>

<script>
import priceManager from '../../../api/priceManager'
import transportManager from '../../../api/transportManager'

export default {
  components: {},
  props: {
    cropId: {
      type: Number,
      default: 0
    },
    data: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      portDetail: {
        carCount: 0, // 厂车数量
        carMinusYesterday: 0 // 厂车数量变化对比昨天
      },
      statsData: {
        0: { totalToday: 0, totalChange: 0 },
        1: { totalToday: 0, totalChange: 0 }
      }
    }
  },
  created() {
    priceManager.queryPortDetail().then(res => {
      this.portDetail = res
    })
    // this.loadStatsData()
  },
  computed: {
    calculatePriceChangePercentage() {
      const result = (this.data.priceChange / this.data.priceAverage) * 100
      return parseFloat((result || 0).toFixed(2))
    }
  },
  methods: {
    formatNum(num) {
      if (num === undefined || num === null) {
        return '--'
      }
      return num
    },
    async loadStatsData() {
      try {
        const res = await transportManager.getTransportStats()
        this.statsData = res
      } catch (error) {
        console.error('加载统计数据失败:', error)
      }
    },
    onNavigateToTransportDetail(type) {
      uni.navigateTo({
        url: `/pages/transport/index?cropId=${this.cropId}&type=${type}`
      })
    }
  }
}
</script>
<style lang="scss">
.data_sheet {
  margin: 0 16px;
  padding: 16px 12px;
  background: linear-gradient(70deg, #ff6a01, #ff8418);
  border-radius: 10px;
  color: #fff;
  .label {
    font-size: 12px;
    line-height: 16px;
    opacity: 0.8;
  }
  .price_box {
    display: flex;
    align-items: baseline;
    justify-content: space-between;
    font-size: 16px;
    padding-bottom: 3px;
    border-bottom: 1rpx solid #fff;
    .num {
      + .num {
        margin-left: 13px;
      }
      &.big {
        font-size: 2em;
      }
    }
    .icon {
      width: 16px;
      height: 16px;
      position: relative;
      top: 2px;
    }
  }
  .factory_box {
    font-size: 14px;
    line-height: 20px;
    margin-top: 8px;
    div {
      margin-top: 4px;
      &.tap_right::after {
        width: 8px;
        height: 8px;
        border-color: #fff;
        opacity: 0.8;
        border-width: 1.5px 1.5px 0 0;
        right: -16px;
      }
    }
    .label {
      display: inline-block;
      opacity: 0.8;
      min-width: 50px;
      text-align: right;
    }
    .txt {
      display: inline-block;
      min-width: 43px;
      text-align: right;
      text {
        margin-left: 3px;
        font-size: 12px;
        color: #fff;
        opacity: 0.8;
      }
    }
    .txt.last {
      margin-left: 12px;
    }
  }
}
</style>
