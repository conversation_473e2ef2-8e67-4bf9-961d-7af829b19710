import http from '../utils/http'

function addRecord(params) {
  return http.post('/points/record', params)
}

function getRecord(params) {
  return http.get('/points/record', params)
}

function getTodayUsed() {
  return http.get('/points/used/today')
}

function generateShareId() {
  return http.get(`/points/share/generate`)
}

function checkShareAndAssistance(params) {
  return http.get(`/points/share/check`, params)
}

export default {
  addRecord,
  getRecord,
  getTodayUsed,
  generateShareId,
  checkShareAndAssistance
}
