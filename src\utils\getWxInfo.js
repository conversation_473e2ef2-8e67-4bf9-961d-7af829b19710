import wxAuth from './wxAuth'
import router from '../router'

function check(){
    return new Promise(function(resolve, reject){
        wxAuth.getUserInfo(res => {
            resolve(res.userInfo)
        }).catch(() => {
            router.startAuth({//前往授权页
                getUserInfoSuccess(userInfo){
                    resolve(userInfo)
                },
                getUserInfoFail(err){
                    reject(err)
                }
            })
        })
    })
}

export default check