<template>
  <view class="has_bottom_btn_root root">
    <view class="title">{{ cropConfig.overallVideoMsg }}</view>
    <view class="video_box">
      <view class="video_item" v-for="(item, index) in fullVideos" :key="index">
        <video class="video" :src="item.path" controls></video>
        <view class="close_icon clear" @tap.native.stop="onClearVideo(index, true)"></view>
      </view>
      <view
        v-if="fullVideos.length < maxFullVideoCount"
        class="video_item add_item"
        @click="onAddVideo(true)"
      >
        <view class="add_btn">
          <view class="add_btn_x"></view>
          <view class="add_btn_y"></view>
        </view>
      </view>
    </view>
    <view v-if="cropConfig.videoMsg">
      <view class="title detail_title">{{ cropConfig.videoMsg }}</view>
      <view class="video_box">
        <view class="video_item" v-for="(item, index) in detailVideos" :key="index">
          <video class="video" :src="item.path" controls></video>
          <view class="close_icon clear" @tap.native.stop="onClearVideo(index, false)"></view>
        </view>
        <view
          v-if="detailVideos.length < maxDetailVideoCount"
          class="video_item add_item"
          @click="onAddVideo(false)"
        >
          <view class="add_btn">
            <view class="add_btn_x"></view>
            <view class="add_btn_y"></view>
          </view>
        </view>
      </view>
    </view>
    <view class="title detail_title">{{ cropConfig.photoMsg }}</view>
    <view class="photo_box">
      <view class="photo_item" v-for="(item, index) in detailPhotos" :key="index">
        <image
          class="photo"
          :src="item.path"
          mode="aspectFit"
          @click="onPhotoPreview(index)"
        ></image>
        <view class="close_icon clear" @tap.native.stop="onClearPhoto(index)"></view>
      </view>
      <view
        v-if="detailPhotos.length < maxDetailPhotoCount"
        class="photo_item add_item"
        @click="onAddPhoto"
      >
        <view class="add_btn">
          <view class="add_btn_x"></view>
          <view class="add_btn_y"></view>
        </view>
      </view>
    </view>
    <view class="title">备注</view>
    <textarea class="remark" v-model="remark" placeholder="填写备注信息"></textarea>
    <view class="fix_bottom_btn" @click="save">{{
      hasUploadFailedFile ? '重新上传' : '确 定'
    }}</view>
  </view>
</template>

<script>
import messageBox from '../../utils/messageBox'
import orderManager from '../../api/orderManager'
import router from '../../router'
import storeManager from '../../utils/storeManager'
import { requestHost } from '../../constant/common'
const app = getApp()
export default {
  data() {
    return {
      isEdit: false,
      fullVideos: [],
      detailVideos: [],
      detailPhotos: [],
      maxFullVideoCount: 4,
      maxDetailVideoCount: 10,
      maxDetailPhotoCount: 12,
      cropConfig: {},
      hasUploadFailedFile: false,
      saving: false,
      remark: ''
    }
  },
  onLoad(options) {
    const { cropsMap } = app.globalData
    const isEdit = !!options.isEdit // 是否编辑现有订单
    if (isEdit) {
      let fullVideos = []
      let detailVideos = []
      let detailPhotos = []
      const { overallVideos, videos, photos, cropChildId, cropId } = app.globalData.editOrder
      const cropConfig = cropsMap[cropChildId || cropId]
      this.overallVideoMsg = cropConfig.overallVideoMsg
      this.videoMsg = cropConfig.videoMsg
      this.photoMsg = cropConfig.photoMsg
      const mapItem = function (url) {
        return { path: url, uploading: false, url }
      }
      if (overallVideos) {
        fullVideos = overallVideos.map(url => mapItem(url))
      }
      if (videos) {
        detailVideos = videos.map(url => mapItem(url))
      }
      if (photos) {
        detailPhotos = photos.map(url => mapItem(url))
      }
      this.isEdit = isEdit
      this.cropConfig = cropConfig
      this.fullVideos = fullVideos
      this.detailVideos = detailVideos
      this.detailPhotos = detailPhotos
    } else {
      console.log(app.globalData.newOrder)
      const localVideosMap = storeManager.get('localVideosMap')
      let fullVideos = []
      let detailVideos = []
      let detailPhotos = []
      if (localVideosMap && localVideosMap.fullVideos) {
        fullVideos = localVideosMap.fullVideos
        detailVideos = localVideosMap.detailVideos
        detailPhotos = localVideosMap.detailPhotos
      }
      const { cropChildId, cropId } = app.globalData.newOrder
      const cropConfig = cropsMap[cropChildId || cropId]
      this.cropConfig = cropConfig
      this.fullVideos = fullVideos
      this.detailVideos = detailVideos
      this.detailPhotos = detailPhotos
    }
  },
  methods: {
    onPhotoPreview(index) {
      const urls = this.detailPhotos.map(item => item.path)
      const current = urls[index]
      uni.previewImage({ urls, current })
    },
    onClearVideo(index, isFull) {
      const arr = isFull ? this.fullVideos : this.detailVideos
      arr.splice(index, 1)
    },
    onClearPhoto(index) {
      this.detailPhotos.splice(index, 1)
    },
    onAddVideo(isFull) {
      uni.chooseVideo({
        sourceType: ['album', 'camera'],
        maxDuration: 60,
        camera: 'back',
        success: res => {
          // tempFilePath	string	选定视频的临时文件路径 (本地路径)
          // duration	number	选定视频的时间长度
          // size	number	选定视频的数据量大小
          // height	number	返回选定视频的高度
          // width	number	返回选定视频的宽度
          const videos = isFull ? this.fullVideos : this.detailVideos
          videos.push({
            path: res.tempFilePath
          })
          this.uploadSource(videos)
        }
      })
    },
    onAddPhoto() {
      const count = this.maxDetailPhotoCount - this.detailPhotos.length
      if (count > 0) {
        uni.chooseImage({
          sizeType: 'compressed',
          count,
          success: res => {
            this.onChooseImageSuccess(res.tempFilePaths)
          }
        })
      } else {
        messageBox.alert(`最多只能上传${this.maxDetailPhotoCount}张照片`)
      }
    },
    uploadSource(data, index) {
      const count = 0
      const timer = setInterval(() => {
        count++
        if (count === 15) {
          clearInterval(timer)
          uni.hideLoading()
          messageBox.alert('上传失败，建议到wifi信号强的地方或者使用手机流量上传')
        }
      }, 1000)
      index = index >= 0 ? index : data.length - 1 // 上传指定的资源或者对应数组的最后一个资源
      if (!data[index].uploading) {
        data[index].uploading = true
        const path = data[index].path
        const task =
          data === this.detailPhotos
            ? orderManager.uploadOrderPhoto(path)
            : orderManager.uploadOrderVideo(path)
        // 记录资源正在上传
        task
          .then(url => {
            clearInterval(timer)
            url = requestHost + url
            data.splice(index, 1, {
              path,
              url,
              uploading: false
            })
            this.onUploadSuccess()
          })
          .catch(err => {
            clearInterval(timer)
            this.hasUploadFailedFile = true
            url = requestHost + url
            data.splice(index, 1, {
              path,
              fail: true,
              uploading: false
            })
          })
      }
    },
    onChooseImageSuccess(paths) {
      const photos = paths.map(path => ({ path, loading: false }))
      this.detailPhotos.push(...photos)
      this.uploadPhotos()
    },
    uploadPhotos() {
      this.detailPhotos.forEach((item, index) => {
        if (item.fail || (!item.uploading && !item.url)) {
          this.uploadSource(this.detailPhotos, index)
        }
      })
    },
    checkUploadingStatus() {
      if (this.fullVideos.some(item => item.uploading)) {
        return '正在上传整体视频'
      }
      if (this.detailVideos.some(item => item.uploading)) {
        return '正在上传测量视频'
      }
      if (this.detailPhotos.some(item => item.uploading)) {
        return '正在上传详细图'
      }
    },
    onUploadSuccess() {
      storeManager.set('localVideosMap', {
        fullVideos: this.fullVideos,
        detailVideos: this.detailVideos,
        detailPhotos: this.detailPhotos
      })
      if (this.saving) {
        this.doSaveData()
      }
    },
    save() {
      // if (this.fullVideos.length === 0) {
      //   messageBox.alert('请添加至少一个' + this.cropConfig.overallVideoMsg).then(() => {
      //     this.onAddVideo(true)
      //   })
      // } else if (this.detailPhotos.length === 0) {
      //   messageBox.alert('请添加至少一个' + this.cropConfig.photoMsg).then(() => {
      //     this.onAddPhoto()
      //   })
      // } else
      if (this.hasUploadFailedFile) {
        // 有上传失败记录
        this.saving = true
        this.fullVideos.forEach((item, index) => {
          if (item.fail) {
            this.uploadSource(this.fullVideos, index)
          }
        })
        this.detailVideos.forEach((item, index) => {
          if (item.fail) {
            this.uploadSource(this.detailVideos, index)
          }
        })
        this.uploadPhotos()
      } else {
        this.doSaveData()
      }
    },
    doSaveData() {
      this.saving = true
      const uploadStatusMsg = this.checkUploadingStatus()
      if (uploadStatusMsg) {
        uni.showLoading({ title: uploadStatusMsg, mask: true })
      } else {
        uni.hideLoading()
        let params = {
          overallVideos: this.fullVideos.map(item => item.url),
          videos: this.detailVideos.map(item => item.url),
          photos: this.detailPhotos.map(item => item.url),
          remark: this.remark
        }
        if (this.isEdit) {
          Object.assign(app.globalData.editOrder, params)
        } else {
          Object.assign(app.globalData.newOrder, params)
        }
        router.startOrderCreateCheck(this.isEdit)
        this.saving = false
      }
    }
  }
}
</script>

<style lang="scss">
.title {
  font-size: 15px;
  line-height: 20px;
  padding: 6px 0;
  &.detail_title {
    margin-top: 10px;
  }
}
.video_item {
  box-sizing: border-box;
  width: 48%;
  height: 200rpx;
  display: inline-block;
  vertical-align: top;
  margin-bottom: 12px;
  position: relative;
  &:nth-child(2n + 1) {
    margin-right: 4%;
  }
}
.photo_item {
  box-sizing: border-box;
  width: 31%;
  height: 200rpx;
  display: inline-block;
  vertical-align: top;
  margin-bottom: 12px;
  position: relative;
  margin-right: 3.49%;
  &:nth-child(3n) {
    margin-right: 0;
  }
}
.add_item {
  border: 2rpx dashed #aaa;
}
.video,
.photo {
  display: block;
  width: 100%;
  height: 100%;
}
.photo {
  background-color: #e3e3e3;
}
.clear {
  width: 24px;
  height: 24px;
  position: absolute;
  top: 2px;
  right: 2px;
}
.add_btn {
  width: 40px;
  height: 40px;
  position: absolute;
  margin: -20px 0 0 -20px;
  left: 50%;
  top: 50%;
}
.add_btn_x,
.add_btn_y {
  background: #aaa;
  position: absolute;
}
.add_btn_x {
  width: 100%;
  height: 2px;
  left: 0;
  top: 50%;
  margin-top: -1px;
}
.add_btn_y {
  width: 2px;
  height: 100%;
  top: 0;
  left: 50%;
  margin-left: -1px;
}
.remark {
  display: block;
  width: 100%;
  min-height: 50px;
  box-sizing: border-box;
  padding: 6px 10px;
  border: 1px solid #aaa;
  border-radius: 3px;
  font-size: 14px;
}
</style>
