<template>
  <view class="factory-item" @click="handleClick">
    <view class="col factory-name">{{ item.factoryName }}</view>
    <view class="col today-data">{{ item.todayData }}{{ item.isSystemData ? '' : '车' }}</view>
    <view class="col change" :style="{ color: changeColor }">
      {{ formattedChange }}
    </view>
    <div class="col date">记录日期：{{ formatDateStr }}</div>
  </view>
</template>

<script>
const { formatDate } = require('../../../utils/date')
export default {
  props: {
    item: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    formattedChange() {
      const val = this.item.minusYesterday
      if (val > 0) return `+${val}${this.item.isSystemData ? '' : '车'}`
      if (val < 0) return `${val}${this.item.isSystemData ? '' : '车'}`
      return '持平'
    },
    changeColor() {
      const val = this.item.minusYesterday
      if (val > 0) return '#67C23A'
      if (val < 0) return '#F56C6C'
      return '#999'
    },
    formatDateStr() {
      return formatDate(new Date(this.item.date), 'yyyy-MM-dd')
    }
  },
  methods: {
    handleClick() {
      this.$emit('click', this.item)
    }
  }
}
</script>

<style lang="scss">
.factory-item {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  padding: 8px 16px;
  border-bottom: 1px solid #eee;

  .col {
    font-size: 14px;

    &.factory-name {
      flex: 2;
      color: #333;
    }
    &.today-data {
      flex: 1;
      text-align: right;
      color: #666;
    }
    &.change {
      flex: 1;
      text-align: right;
      font-weight: 500;
    }
    &.date {
      flex-basis: 100%;
      font-size: 12px;
      text-align: right;
      color: #999;
      padding-top: 3px;
    }
  }
}
</style>
