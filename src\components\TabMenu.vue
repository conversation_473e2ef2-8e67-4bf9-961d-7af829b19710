<template>
    <div class="tab_menu_box" :class="{ show_border: showBorder, fix: fixTop, full_width: fixWidth, show_bg: showBg }">
        <div
            class="top_tab"
            :class="[index === idx ? 'on' : 'off', fixWidth ? `fix_width_${textArr.length}` : '']"
            :style="itemStyle"
            v-for="(item, idx) in textArr"
            :key="idx"
            @click="onTabClick(idx)"
        >{{ item }}</div>
    </div>
</template>

<script>
export default {
    props: {
        textArr: {
            type: Array,
            default: () => []
        },
        index: {
            type: Number,
            default: 0
        },
        fixTop: {
            type: Boolean,
            default: false
        },
        fixWidth: {
            type: Boolean,
            default: false
        },
        showBorder: {
            type: Boolean,
            default: false
        },
        showBg: {
            type: Boolean,
            default: false
        }
    },
    methods: {
        onTabClick (index) {
            this.$emit('update:index', index)
            this.$emit('change', index)
        }
    }
}
</script>

<style lang="scss">
.tab_menu_box {
    height: 50px;
    line-height: 50px;
    padding: 0 16px;
    font-size: 0;
    box-sizing: border-box;
    &.show_bg {
        background: #ffffff;
    }
    &.show_border {
        border-bottom: 1px solid #f1f1f1;
    }
    &.full_width {
        // 屏幕同宽，内容居中
        padding: 0;
        text-align: center;
        .top_tab {
            margin-left: 0 !important;
            &.on::after {
                width: 1em !important;
                margin-left: -0.5em;
            }
        }
    }
    &.fix {
        width: 100%;
        position: fixed;
        left: 0;
        top: 0;
        z-index: 100;
    }
}
.top_tab {
    font-size: 16px;
    display: inline-block;
    color: #787878;
    position: relative;
    & + .top_tab {
        margin-left: 24px;
    }
    &.fix_width_2 {
        width: 50%;
    }
    &.fix_width_3 {
        width: 33.3%;
    }
    &.fix_width_4 {
        width: 25%;
    }
    &.fix_width_5 {
        width: 20%;
    }
    &.fix_width_6 {
        width: 16.6%;
    }
    &.on {
        font-size: 20px;
        font-weight: 600;
        color: #333;
        &::after {
            content: '';
            width: 1.8em;
            height: 3px;
            background-color: #ff6a01;
            margin-left: -0.9em;
            position: absolute;
            left: 50%;
            bottom: 6px;
        }
    }
}
</style>