import http from '../utils/http'

function addMsg (toUserId, content, orderId) {
    return http.post('/message/add', { toUserId, content, orderId });
}

function queryMyMessage (pageIndex, pageSize) {
    return http.post('/message/queryMy', { pageIndex, pageSize });
}

function queryMessageBetweenUser (otherUserId, pageIndex, pageSize) {
    return http.post('/message/queryBetweenUser', { otherUserId, pageIndex, pageSize });
}

export default {
    addMsg,
    queryMyMessage,
    queryMessageBetweenUser
}