<template>
  <view class="news-item" @click="onNavigateToNewsDetail(item)">
    <view class="news-content">
      <view class="news-header">
        <view class="news-title">{{ item.title }}</view>
        <view v-show="item.category" class="news-category">{{ item.category }}</view>
      </view>
      <view class="news-sub-header">
        <view class="news-date">{{ publishTime }}</view>
        <view class="news-split">-</view>
        <view class="news-source">{{ sourceLabels[item.source] }}</view>
      </view>
      <view v-show="item.summary" class="news-summary">{{ item.summary }}</view>
    </view>
    <view v-if="item.image" class="news-image">
      <image :src="item.image" mode="aspectFill"></image>
    </view>
  </view>
</template>

<script>
const { formatDate } = require('../../../utils/date')

export default {
  props: {
    item: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      sourceLabels: {
        1: '中国农业农村信息网',
        2: '国家粮食交易中心',
        3: '中国大豆产业协会',
        4: '中国棉花协会',
        5: '山东省粮食和物资储备局',
        6: '河北省粮食和物资储备局',
        7: '吉林省粮食和物资储备局',
        8: '新疆维吾尔自治区粮食和物资储备局',
        9: '内蒙古自治区粮食和物资储备局'
      }
    }
  },
  computed: {
    publishTime() {
      return formatDate(new Date(this.item.publishTime), 'yyyy-MM-dd')
    }
  },
  created() {},
  methods: {
    onNavigateToNewsDetail(item) {
      uni.navigateTo({
        url: `/pages/news/detail?newsId=${item.id}`
      })
    }
  }
}
</script>

<style lang="scss">
.news-item {
  display: flex;
  margin: 10px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0px 0px 6px 2px rgba(0, 0, 0, 0.05);
  &:first-child {
    margin-top: 5px;
  }
}

.news-content {
  flex: 1;
  padding: 10px;
  box-sizing: border-box;
  width: 100%;
}

.news-header {
  display: flex;
  margin-bottom: 5px;
}

.news-title {
  font-size: 14px;
  font-weight: bold;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  flex: 1;
}

.news-category {
  display: inline-flex;
  font-size: 12px;
  color: #ff8418;
  border: 1px solid #ff8418;
  padding: 0 6px;
  height: 20px;
  line-height: 20px;
  border-radius: 20px;
  margin-left: 6px;
}

.news-summary {
  font-size: 13px;
  color: #666;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  margin-top: 5px;
}

.news-sub-header {
  display: flex;
  align-items: center;
}

.news-date,
.news-source,
.news-split {
  font-size: 12px;
  color: #999;
}

.news-split {
  margin: 0 5px;
}

.news-image {
  width: 100px;
  height: 100px;
  margin-left: 10px;
  border-radius: 8px;
  overflow: hidden;
  image {
    width: 100%;
    height: 100%;
  }
}
</style>
