<template>
    <div class="company_root">
        <view class="line_chart">
            <l-echart ref="chartRef"></l-echart>
        </view>

        <div class="data_sheet">
            <div class="label">{{ cropName }}工价（元/斤）</div>
            <div class="price_box">
                <span v-if="todayPrice.minusYesterday === 0" class="num big">不变</span>
                <span v-else class="num big">
                    {{
                    (todayPrice.minusYesterday > 0 ? '+' : '') + todayPrice.minusYesterday
                    }}
                </span>
                <span class="num">{{ todayPrice.price }}</span>
                <template v-if="todayPrice.minusYesterday != 0">
                    <span class="num">{{ todayPrice.raisePercent }}%</span>
                    <img v-if="todayPrice.raisePercent > 0" class="icon" src="/static/images/icon_up.png" alt="千万仓" />
                    <img v-else-if="todayPrice.raisePercent < 0" class="icon" src="/static/images/icon_down.png" alt="千万仓" />
                </template>
            </div>
            <div class="factory_box">所在地：{{ cityText }}</div>
        </div>

        <div v-for="item in detailArr" :key="item.id" class="detail_box">
            <h3 class="title">最新{{item.cropName}}报价详情</h3>
            <div class="detail_content">{{ item.desc }}</div>
            <div v-if="item.photos" class="detail_photos">
                <img class="photo" v-for="item in item.photos" :key="item" :src="item" mode="widthFix" alt="qianwancang" />
            </div>
        </div>

        <div class="ad_box">
            <ad unit-id="adunit-4fb662e4bd218b9c" ad-type="video" ad-theme="white"></ad>
        </div>
    </div>
</template>

<script>
import * as echarts from '@/uni_modules/lime-echart/static/echarts.min'
import priceManager from '../../api/priceManager'
import cityManager from '../../api/cityManager'
import orderManager from '../../api/orderManager'
import { generateDates } from './handler'
import messageBox from '../../utils/messageBox'
import { requestHost } from '../../constant/common'
export default {
    name: 'CompanyDetail',
    data () {
        return {
            companyName: '',
            cityText: '',
            cornDetail: null,
            wheatDetail: null,
            todayPrice: {},
            detailArr: [], // 报价更新详情
            cropId: 1,
            cropName: '玉米'
        }
    },
    onLoad (params) {
        const { id, cropId, name } = params
        this.cropId = cropId
        if (name) {
            this.companyName = decodeURIComponent(name)
            uni.setNavigationBarTitle({
                title: name
            })
        }
        uni.showLoading({
            title: '加载中'
        })
        Promise.all([
            orderManager.fetchCrops(),
            priceManager.queryCompanyLatestDetail(id),
            priceManager.queryCompanyPriceChartData(id, cropId)
        ]).then(resArr => {
            const cropsMap = resArr[0].cropsMap;
            this.detailArr = resArr[1].map(item => {
                item.cropName = cropsMap[item.cropId] ? cropsMap[item.cropId].name : `农作物-${item.cropId}`
                if (item.photos) {
                    item.photos = item.photos.split(',').map(path => requestHost + path)
                }
                return item
            });
            const { companyName, cropName, datePrices, cityCode, dateRange } = resArr[2]
            this.companyName = companyName
            this.cropName = cropName
            this.datePrices = datePrices
            const todayPrice = datePrices[datePrices.length - 1]
            const yesterdayPrice = datePrices[datePrices.length - 2]
            const minusYesterday = Math.round((todayPrice - yesterdayPrice) * 1000) / 1000
            const raisePercent = Math.round((minusYesterday / yesterdayPrice) * 100) / 100
            this.todayPrice = {
                price: todayPrice,
                yesterdayPrice,
                minusYesterday,
                raisePercent
            }
            const xAxis = generateDates(...dateRange).map(date => {
                return `${date.getMonth() + 1}/${date.getDate()}`
            })
            this.renderChart(datePrices, xAxis)
            cityManager.fetchAllCities().then(res => {
                this.cityText = cityManager
                    .codeToValue(cityCode, res)
                    .map(item => item.name)
                    .join('')
            })
        }).catch(err => {
            console.log(err)
            messageBox.toast.text('数据加载失败', 1000);
        }).then(() => {
            uni.hideLoading()
        })
    },
    methods: {
        renderChart (priceArr, xAxis) {
            const chartRef = this.$refs['chartRef']
            chartRef.init(echarts).then(chart => {
                const option = priceManager.getFixChartOption(xAxis, [
                    {
                        name: `${this.companyName} - ${this.cropName}厂价`,
                        type: 'line',
                        data: priceArr
                    }
                ])
                chart.setOption(option)
                this.showChartTip(chart, priceArr.length - 1)
            })
        },
        showChartTip (chart, dataIndex) {
            setTimeout(() => {
                chart.dispatchAction({
                    type: 'showTip',
                    seriesIndex: 0,
                    dataIndex
                })
            }, 1000)
        }
    }
}
</script>

<style lang="scss">
.line_chart {
    width: 100%;
    height: 270px;
}
.company_root {
    padding: 20px 0;
}
.data_sheet {
    margin: 16px 16px;
    padding: 16px;
    background: linear-gradient(70deg, #ff6a01, #ff8418);
    border-radius: 10px;
    color: #fff;
    .label {
        font-size: 12px;
        line-height: 18px;
        opacity: 0.7;
    }
    .price_box {
        font-size: 16px;
        padding: 10px 0;
        border-bottom: 1rpx solid #fff;
        .num {
            + .num {
                margin-left: 16px;
            }
            &.big {
                font-size: 2em;
            }
        }
        .icon {
            width: 18px;
            height: 18px;
            position: relative;
            top: 3px;
            margin-left: 6px;
        }
    }
    .factory_box {
        font-size: 14px;
        line-height: 20px;
        margin-top: 12px;
    }
}
.detail_box {
    margin: 0 16px;
    padding-bottom: 20px;
    .title {
        font-size: 16px;
        line-height: 2.4;
        font-weight: 600;
    }
}
.detail_content {
    font-size: 14px;
    white-space: pre-wrap;
    text-align: justify;
}
.detail_photos {
    .photo {
        display: block;
        width: 100%;
        margin-top: 12px;
    }
}
.ad_box {
    border-top: 15px solid #f6f6f6;
}
</style>
