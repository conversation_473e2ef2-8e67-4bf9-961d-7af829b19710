<template>
    <view class="page_root">
        <no-data v-if="total === 0" msg="未查询到相关订单" />
        <order-item v-for="(item, index) in orders" :key="index" :order="item" @click="onOrderClick(index)"></order-item>
    </view>
</template>

<script>
import orderManager from '../../api/orderManager'
import router from '../../router'
import OrderItem from '../../components/OrderItem.vue'
export default {
    components: {
        OrderItem
    },
    data () {
        return {
            orders: [],
            total: -1
        }
    },
    onShow () {
        orderManager.queryMyLikeOrders().then(res => {
            this.orders = res;
            this.total = res.length;
        })
    },
    methods: {
        onOrderClick (index) {
            const order = this.orders[index];
            router.startOrderDetail(order);
        }
    }
}
</script>

<style lang="scss">
page {
    background-color: #f6f6f6;
}
.page_root {
    padding: 15px 0;
}
</style>