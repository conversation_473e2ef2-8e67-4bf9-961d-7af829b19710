<template>
  <view class="points-management">
    <div class="points-header">
      <zui-svg-icon icon="points" width="38px" style="height: 38px" />
      <div class="points-value">
        <text>{{ userInfo.points }}</text
        >积分
      </div>
    </div>
    <view class="points-list" style="height: calc(100vh - 65px)">
      <no-data v-if="total === 0" />
      <template v-else>
        <scroll-view
          :scroll-top="scrollTop"
          scroll-y
          refresher-enabled
          scroll-with-animation
          :refresher-triggered="triggered"
          style="height: 100%"
          @refresherrefresh="onRefresh"
          @scrolltolower="onLower"
        >
          <view class="points-item" v-for="(item, index) in processedPointsData" :key="index">
            <ad-custom
              v-if="item.type === 'insert'"
              unit-id="adunit-66ba8a8749812fd3"
              style="margin: -10px -16px"
              @load="adLoad"
              @error="adError"
              @close="adClose"
            ></ad-custom>
            <template v-else>
              <div class="item-content">
                <view class="item-left">
                  <text class="desc">{{
                    item.description ||
                    (item.transactionType === 'gain' && '获得') ||
                    (item.transactionType === 'use' && '使用')
                  }}</text>
                  <text class="date">{{ timeToDateString(item.createTime) }}</text>
                </view>
                <view class="item-right">
                  <text :class="['point', item.transactionType]"
                    >{{ transactionTypeLabels[item.transactionType] }}{{ item.point }}</text
                  >
                  <text class="point-unit"> 积分</text>
                </view>
              </div>
            </template>
          </view>
          <view v-if="pointsData.length === total" class="points-item">
            <ad-custom
              unit-id="adunit-66ba8a8749812fd3"
              style="margin: -10px -16px"
              @load="adLoad"
              @error="adError"
              @close="adClose"
            ></ad-custom>
          </view>
          <view class="loading" v-if="loadingData"></view>
          <view class="loaded_finish" v-else-if="total > 0 && total <= (pageIndex + 1) * pageSize">
            共{{ total }}条积分记录，已加载完毕
          </view>
        </scroll-view>
      </template>
    </view>
  </view>
</template>
<script>
import pointsManager from '../../api/pointsManager'
import userManager from '../../api/userManager'
const { timeToDateString, formatDate } = require('../../utils/date')

export default {
  name: 'PointsManagement',
  components: {},
  data() {
    return {
      userInfo: {},
      transactionTypeLabels: {
        gain: '+',
        use: '-'
      },
      timeToDateString,
      loading: false,
      description: '',
      pointsData: [],
      loadingData: false,
      pageIndex: 0,
      pageSize: 20,
      total: -1,
      triggered: false
    }
  },
  watch: {},
  computed: {
    processedPointsData() {
      return this.insertElementsBetweenDays(this.pointsData)
    }
  },
  created() {
    this.userInfo = userManager.getUser()
  },
  async mounted() {
    try {
      this._freshing = true
      uni.showLoading({ title: '加载中...' })
      await this.initPointsList(true)
    } catch (error) {
      uni.showToast({
        title: '加载积分记录失败',
        icon: 'none'
      })
      throw error
    } finally {
      uni.hideLoading()
      this._freshing = false
    }
  },

  methods: {
    adLoad() {
      console.log('原生模板广告加载成功')
    },
    adError(err) {
      console.error('原生模板广告加载失败', err)
    },
    adClose() {
      console.log('原生模板广告关闭')
    },
    insertElementsBetweenDays(data, dateField = 'createTime') {
      let newData = []
      let currentDayCount = 0
      let currentDate = ''
      for (let i = 0; i < data.length; i++) {
        const itemDate = formatDate(new Date(data[i][dateField]), 'yyyy-MM-dd')
        if (itemDate !== currentDate) {
          currentDayCount = 0
          currentDate = itemDate
        }
        newData.push(data[i])
        currentDayCount++
        if (i === data.length - 1) {
          continue
        }
        if (formatDate(new Date(data[i + 1][dateField]), 'yyyy-MM-dd') !== itemDate) {
          if (currentDayCount >= 5) {
            newData.push({
              id: Date.now(),
              type: 'insert'
            })
          }
        }
      }

      return newData
    },
    initPointsList(refresh) {
      return new Promise((resolve, reject) => {
        if (this.loadingData) return
        this.loadingData = true
        if (refresh) {
          this.pointsData = []
          this.pageIndex = 0
        } else {
          this.pageIndex++
        }
        pointsManager
          .getRecord({
            pageIndex: this.pageIndex,
            pageSize: this.pageSize
          })
          .then(res => {
            this.pointsData.push(...res.list)
            this.total = res.total
            resolve(res)
          })
          .catch(err => {
            uni.showToast({
              title: '获取积分记录失败',
              icon: 'none'
            })
            reject(err)
          })
          .finally(() => {
            this.loadingData = false
          })
      })
    },
    onRefresh() {
      if (this._freshing) return
      this._freshing = true
      this.triggered = true
      this.initPointsList(true).finally(() => {
        this.triggered = false
        this._freshing = false
      })
    },
    onLower() {
      const { loadingData, total, pageIndex, pageSize } = this
      if (!loadingData) {
        if (total > (pageIndex + 1) * pageSize) {
          this.initPointsList()
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.points-management {
  /* padding: 20px; */
}

.points-header {
  display: flex;
  align-items: center;
  padding: 10px 20px;
}
.points-value {
  margin-left: 6px;
  color: #666;
  text {
    font-size: 30px;
    font-weight: 700;
    margin: 0 3px;
    color: #ff5c5c;
  }
}

.points-list {
  position: relative;
  /* margin-top: 20px; */
}
.points-item {
  margin: 0 16px;
  border-bottom: 1px solid #f1f1f1;
  padding: 8px 0;
  font-size: 14px;
}
.item-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.item-left {
  display: flex;
  flex-direction: column;
}
.item-right {
  display: flex;
  justify-content: space-between;

  line-height: 20px;
}
.point {
  font-size: 18px;
  font-weight: 700;
  &.gain {
    color: #ff5c5c;
  }
  &.use {
    color: #999;
  }
}
.point-unit {
  font-size: 14px;
  margin-left: 3px;
  color: #666;
}
.desc {
  flex: 1;
}
.date {
  flex-shrink: 0;
  margin-top: 4px;
  font-size: 12px;
  color: #999;
}

.loading {
  margin-top: 10px;
  height: 40px;
}

.loaded_finish {
  font-size: 14px;
  color: #8a96a3;
  text-align: center;
  margin-top: 15px;
}
</style>
