<template>
  <view class="detail-container">
    <view class="info-card header-card">
      <view class="title-row">
        <image
          class="avatar"
          :src="detail.avatar || '/static/images/user_default.png'"
          mode="aspectFill"
        />
        <view class="user-info">
          <view class="type-row">
            <text class="name-text">{{ detail.name }}</text>
          </view>
          <view class="type-row">
            <text class="type-tag">{{ typeLabels[detail.type] }}</text>
            <text class="gender-tag">
              {{ detail.gender === 1 ? '♂' : '♀' }}
            </text>
          </view>
        </view>
      </view>
    </view>

    <view class="info-card image-container" v-if="detail.images && detail.images.length">
      <image
        class="image-detail-item"
        v-for="(img, index) in detail.images"
        :key="index"
        :src="img"
        mode="widthFix"
      />
    </view>

    <template v-if="detail.type === 1">
      <view class="info-card">
        <view class="section-header">
          <text class="section-title">收购品类</text>
        </view>
        <view class="tag-container">
          <text v-for="(crop, idx) in detail.purchaseCrops" :key="idx" class="crop-tag">
            {{ cropMap[crop].name || crop || '未知品类' }}
          </text>
        </view>
      </view>

      <view class="info-card">
        <view class="section-header">
          <text class="section-title">运输车辆</text>
        </view>
        <view class="tag-container">
          <text v-for="(car, idx) in detail.transportCars" :key="idx" class="car-tag">
            {{ car }}
          </text>
        </view>
      </view>
    </template>

    <template v-if="detail.type === 3">
      <view class="info-card">
        <view class="section-header">
          <text class="section-title">在售产品</text>
        </view>
        <view class="tag-container">
          <text v-for="(product, idx) in detail.saleProducts" :key="idx" class="car-tag">
            {{ product }}
          </text>
        </view>
      </view>
    </template>

    <view class="info-card">
      <view class="section-header">
        <text class="section-title">联系地址</text>
      </view>
      <text class="description-text">{{ detail.address }}</text>
    </view>

    <view class="info-card" v-if="detail.description">
      <view class="section-header">
        <text class="section-title">详情描述</text>
      </view>
      <text class="description-text">{{ detail.description }}</text>
    </view>

    <view class="action-bar fixed">
      <view class="distance-info">
        <text class="distance-text">距离您 {{ detail.distance || 0 }} 公里</text>
      </view>
      <view class="contact-info">
        <text class="contact-count">{{ detail.contactedCount || 0 }}人已联系</text>
        <view class="contact-btn" @click="onPhoneClick(detail)">电话联系</view>
      </view>
    </view>
  </view>
</template>

<script>
import traderManager from '../../api/traderManager'
import orderManager from '../../api/orderManager'

export default {
  data() {
    return {
      typeLabels: { 1: '收购商', 2: '粮庄', 3: '设备商' },
      detail: {},
      cropMap: {}
    }
  },
  async onLoad(options) {
    try {
      uni.showLoading({ title: '加载中...' })
      const res = await traderManager.getTraderInfo(options.id)
      const { type } = res
      uni.setNavigationBarTitle({
        title: `${type === 2 ? '粮庄' : type == 3 ? '设备商' : '收购商'}详情`
      })
      this.detail = {
        ...res,
        distance: options.distance,
        purchaseCrops: res.purchaseCrops
          ?.split(',')
          .filter(Boolean)
          .map(item => {
            const num = Number(item)
            if (!isNaN(num) && /^[-+]?\d+(\.\d+)?$/.test(item.trim())) {
              return num
            }
            return item
          }),
        transportCars: res.transportCars?.split(',').filter(Boolean),
        saleProducts: res.saleProducts?.split(',').filter(Boolean),
        images: res.images?.split(',').filter(Boolean)
      }
      const cropsRes = await orderManager.fetchCrops()
      this.cropMap = cropsRes.crops.reduce((acc, cur) => {
        acc[cur.id] = cur
        return acc
      }, {})
    } catch (error) {
      uni.showToast({ title: '加载详情失败', icon: 'none' })
    } finally {
      uni.hideLoading()
    }
  },
  methods: {
    onPhoneClick(item) {
      uni
        .makePhoneCall({
          phoneNumber: item.mobile
        })
        .then(() => {
          traderManager.contactTrader(item.id)
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.detail-container {
  position: relative;
  padding-bottom: 95px;

  .info-card {
    margin: 15px;
    padding: 12px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 0 6px 2px rgba(0, 0, 0, 0.05);
  }

  .header-card {
    .title-row {
      display: flex;
      // align-items: center;
      gap: 12px;

      .avatar {
        width: 66px;
        height: 66px;
        border-radius: 4px;
        flex-shrink: 0;
      }

      .user-info {
        flex: 1;
        min-width: 0;
        .type-row {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 4px;
        }
        .name-text {
          flex: 1;
          min-width: 0;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-size: 16px;
          font-weight: 600;
          color: #333;
        }
        .type-tag {
          flex-shrink: 0;
          font-size: 12px;
          color: #fff;
          background: #0d7fff;
          padding: 2px 6px;
          border-radius: 4px;
        }
        .gender-tag {
          font-size: 12px;
          line-height: 12px;
          color: #0d7fff;
          padding: 4px 6px;
          background: #e6f3ff;
          border-radius: 4px;
        }
      }
    }
  }

  .section-header {
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;

    .section-title {
      font-size: 15px;
      font-weight: 600;
      color: #333;
    }
  }

  .tag-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .crop-tag,
    .car-tag {
      padding: 2px 14px;
      border: 1px solid #ff6a01;
      border-radius: 15px;
      color: #ff6a01;
      font-size: 13px;
    }

    .car-tag {
      border: 1px solid #666;
      color: #666;
    }
  }

  .description-text {
    display: block;
    font-size: 14px;
    color: #666;
    line-height: 1.6;
    margin-top: 8px;
  }

  .image-container {
    padding: 0;
    overflow: hidden;
    .image-detail-item {
      display: block;
      width: 100%;
    }
  }
}

.fixed {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 10;
  margin: 0;
  border-radius: 0;
  box-shadow: 0 -2px 6px rgba(0, 0, 0, 0.05);
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px 30px;
  background: #fff;
  border-radius: 8px 8px 0 0;
  box-shadow: 0px 0px 10px 5px rgba(0, 0, 0, 0.05);

  .distance-info {
    flex: 1;

    .distance-text {
      font-size: 14px;
      color: #333;
      margin-right: 12px;
    }
  }

  .contact-info {
    display: flex;
    flex-direction: column;
    align-items: center;

    .contact-count {
      font-size: 12px;
      color: #999;
      margin-bottom: 3px;
    }
    .contact-btn {
      padding: 6px 25px;
      background: #ff6a01;
      color: #fff;
      border-radius: 20px;
      font-size: 14px;
    }
  }
}
</style>
