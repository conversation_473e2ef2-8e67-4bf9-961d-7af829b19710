import http from '../utils/http'

import { OPEN_WX_MINI_APP, AUTO_VIEW_GROUP_QRCODE } from '../constant/event'

function addLog (key, detail) {
    return http.post('/log/add', { key, detail });
}

function addAutoViewQrCodeLog () {
    return http.post('/log/add', { key: AUTO_VIEW_GROUP_QRCODE });
}

function addStartRecord (shareId) { // 添加App启动记录
    return http.post('/log/addRecord', { shareId, action: OPEN_WX_MINI_APP });
}

export default {
    addLog,
    addAutoViewQrCodeLog,
    addStartRecord
}