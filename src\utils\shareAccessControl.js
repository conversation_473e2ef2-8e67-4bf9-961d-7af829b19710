export const ModuleAccessControl = {
  /**
   * 判断是否是本月第N次打开
   * @param {number} n - 第几次打开
   */
  isNthTimeInMonth(n) {
    let openCountData = uni.getStorageSync('openCountData') || {}
    let currentTime = new Date()
    let currentMonthKey = `${currentTime.getFullYear()}-${currentTime.getMonth()}`

    if (!openCountData[currentMonthKey] || openCountData[currentMonthKey] < n - 1) {
      return true
    }
    return false
  },

  /**
   * 判断用户是否需要分享
   * @param {number} validDays - 分享后有效的天数
   */
  needsToShare(validDays) {
    let lastShareTime = uni.getStorageSync('lastShareTime') || null
    let hasShared = uni.getStorageSync('hasShared') || false

    if (!hasShared) {
      return true
    }
    if (lastShareTime) {
      lastShareTime = new Date(lastShareTime)
      let currentTime = new Date()
      let timeDiff = currentTime - lastShareTime
      let daysDiff = timeDiff / (1000 * 60 * 60 * 24)
      if (daysDiff > validDays) {
        return true
      }
    }
    return false
  },

  /**
   * 记录用户打开模块的时间
   */
  recordModuleOpen() {
    let currentTime = new Date()
    let openCountData = uni.getStorageSync('openCountData') || {}
    let currentMonthKey = `${currentTime.getFullYear()}-${currentTime.getMonth()}`

    if (!openCountData[currentMonthKey]) {
      openCountData[currentMonthKey] = 1
    } else {
      openCountData[currentMonthKey] += 1
    }

    uni.setStorageSync('lastOpenTime', currentTime)
    uni.setStorageSync('openCountData', openCountData)
  },

  /**
   * 记录用户分享的时间
   */
  async recordShare() {
    let currentTime = new Date()
    return new Promise((resolve, reject) => {
      try {
        uni.setStorageSync('lastShareTime', currentTime)
        uni.setStorageSync('hasShared', true)
        resolve(true)
      } catch (error) {
        reject(error)
      }
    })
  },

  /**
   * 检查用户是否可以访问模块
   * @param {number} validDays - 分享后有效的天数
   * @param {number} nthTime - 本月打开次数
   * @returns {boolean} - true 表示可以访问，false 表示需要分享
   */
  canAccessModule(validDays, nthTime) {
    // 判断是否是本月第N次打开
    if (this.isNthTimeInMonth(nthTime)) {
      this.recordModuleOpen()
      return true
    }

    // 不是第一次打开，检查是否需要分享
    if (this.needsToShare(validDays)) {
      return false
    }

    return true
  },

  /**
   * 清除分享相关的存储状态
   */
  clearShareState() {
    try {
      uni.removeStorageSync('lastShareTime')
      uni.removeStorageSync('openCountData')
      uni.removeStorageSync('hasShared')
      return true
    } catch (error) {
      console.error('清除分享状态失败:', error)
      return false
    }
  }
}
