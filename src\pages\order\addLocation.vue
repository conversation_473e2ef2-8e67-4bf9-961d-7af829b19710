<template>
    <view class="address_root">
        <view class="part address_input_box">
            <view class="address_info_box" @click="pickAddress">
                <image class="icon" src="/static/images/location_icon.png" />
                <view class="label required">作物详细地址</view>
                <view class="switch_icon_box">
                    <view class="switch_label">切换</view>
                    <view class="switch_icon"></view>
                </view>
            </view>
            <view class="address_box">
                <textarea class="address_input" v-model="formData.address" :auto-height="true" placeholder="请输入您的详细地址" />
            </view>
        </view>
        <view v-if="considerWay">
            <view class="part select_item_box" :class="{
                shake: shake1
            }">
                <view class="label required">是否可以进半挂车?</view>
                <view class="select_box">
                    <view class="option" :class="{ 
                        checked: formData.semitrailer === 1
                     }" @click="formData.semitrailer = 1">
                        <view class="mark"></view>
                        <view class="text">是</view>
                    </view>
                    <view class="option" :class="{ 
                        checked: formData.semitrailer === 0
                     }" @click="formData.semitrailer = 0">
                        <view class="mark"></view>
                        <view class="text">否</view>
                    </view>
                </view>
            </view>
            <view class="part select_item_box" :class="{
                shake: shake2
            }">
                <view class="label required">是否可以进铲车?</view>
                <view class="select_box">
                    <view class="option" :class="{ 
                        checked: formData.forklift === 1
                     }" @click="formData.forklift = 1">
                        <view class="mark"></view>
                        <view class="text">是</view>
                    </view>
                    <view class="option" :class="{ 
                        checked: formData.forklift === 0
                     }" @click="formData.forklift = 0">
                        <view class="mark"></view>
                        <view class="text">否</view>
                    </view>
                </view>
            </view>
        </view>
        <view v-if="isEdit" class="update_bottom_btn">
            <view class="btn fl" @click="jump">跳过</view>
            <view class="btn fr" @click="editSave">下一步</view>
        </view>
        <view v-else class="fix_bottom_btn" @click="addSave">下一步</view>
    </view>
</template>

<script>
import userManager from '../../api/userManager'
import router from '../../router.js';
import messageBox from '../../utils/messageBox'
const QQMapWX = require('../../utils/qqmap-wx-jssdk');
const app = getApp();
const mapSdk = new QQMapWX({ key: '7C6BZ-6SLKU-OPUVH-2HYAV-JCJSZ-TFB2M' });
export default {
    data () {
        return {
            formData: {
                latitude: null,
                longitude: null,
                cityCode: null,
                address: null,
                semitrailer: null,
                forklift: null
            },
            user: null,
            isEdit: false,
            isCollectorEdit: false, // 是否信息收集员在更新别人的订单
            shake1: false,
            shake2: false,
            considerWay: false // 是否需要考虑路况情况
        }
    },
    computed: {

    },
    onLoad (options) {
        const { cropsMap } = app.globalData;
        const isEdit = !!options.isEdit; // 是否编辑现有订单的位置信息
        const user = userManager.getUser();
        this.user = user;
        const infoFrom = isEdit ? app.globalData.editOrder : user;
        Object.keys(this.formData).forEach(key => {
            this.formData[key] = infoFrom[key]
        })
        if (isEdit) {
            const { cropChildId, cropId, userId } = app.globalData.editOrder;
            const isCollectorEdit = userId != user.id; // 是信息收集员在编辑别人的订单
            const cropConfig = cropsMap[cropChildId || cropId];
            this.considerWay = cropConfig.considerWay;
            this.isEdit = isEdit;
            this.isCollectorEdit = isCollectorEdit;
        } else {
            const { cropChildId, cropId } = app.globalData.newOrder;;
            const cropConfig = cropsMap[cropChildId || cropId];
            this.considerWay = cropConfig.considerWay;
            if (!user.latitude && !user.longitude && !user.cityCode && !user.address) {// 用户的地址位置信息齐全
                this.pickAddress();
            }
        }
    },
    methods: {
        pickAddress () {
            uni.chooseLocation({
                success: (res) => {
                    const { latitude, longitude, address } = res;
                    this.getCityCode(latitude, longitude);
                    this.formData.latitude = latitude;
                    this.formData.longitude = longitude;
                    this.formData.address = address;
                },
                fail: (err) => {
                    messageBox.alert('位置信息获取失败')
                }
            })
        },

        setAddressValue (event) {
            const value = event.detail.value;
            this.formData.address = value;
        },

        getCityCode (latitude, longitude) {
            mapSdk.reverseGeocoder({
                location: { latitude, longitude },
                success: res => {
                    let { adcode } = res.result.ad_info;
                    this.formData.cityCode = adcode * 1000000
                }
            })
        },

        showShake1 () {
            this.shake1 = true;
            setTimeout(() => {
                this.shake1 = false;
            }, 1000)
        },
        showShake2 () {
            this.shake2 = true;
            setTimeout(() => {
                this.shake2 = false;
            }, 1000)
        },

        checkFrom (callback) {
            const { latitude, address, semitrailer, forklift } = this.formData;
            if (!latitude) {
                messageBox.alert('请选择您的作物所在的位置')
            } else if (!address) {
                messageBox.alert('请输入作物所在位置的详细地址')
            } else if (this.considerWay) {
                if (semitrailer === null || semitrailer === undefined) {
                    this.showShake1()
                } else if (forklift === null || forklift === undefined) {
                    this.showShake2()
                } else {
                    callback()
                }
            } else {
                callback()
            }
        },
        addSave () {
            this.checkFrom(() => {
                Object.assign(app.globalData.newOrder, this.formData)
                router.startAddVideoAndPhoto()
            })
        },
        editSave () {
            this.checkFrom(() => {
                Object.assign(app.globalData.newOrder, this.formData)
                router.startEditVideoAndPhoto()
            })
        },
        jump () {
            router.startEditVideoAndPhoto()
        }
    }
}
</script>

<style lang="scss">
.part + .part {
    border-top: 12px solid #f1f1f1;
}
.label {
    font-size: 16px;
}
.address_input_box {
    padding: 15px 0;
}
.address_info_box {
    background-color: #fff;
    padding: 15px 41px 15px 49px;
    font-size: 14px;
    line-height: 20px;
    position: relative;
    .icon {
        width: 24px;
        height: 24px;
        margin-top: -12px;
        position: absolute;
        top: 50%;
        left: 15px;
    }
    .switch_icon_box {
        position: absolute;
        font-size: 14px;
        line-height: 16px;
        height: 16px;
        margin-top: -8px;
        top: 50%;
        right: 15px;
    }
    .switch_label {
        font-size: 16px;
        display: inline-block;
        vertical-align: middle;
        margin-right: 10px;
    }
    .switch_icon {
        background-image: url("data:image/svg+xml,%3Csvg width='100' height='99' viewBox='0 0 100 99' fill='none' xmlns='http://www.w3.org/2000/svg'%3E %3Cpath d='M20.3805 24.1657H93.5675C95.2359 24.1657 96.8359 24.8285 98.0156 26.0082C99.1952 27.1878 99.858 28.7878 99.858 30.4562C99.858 32.1245 99.1952 33.7245 98.0156 34.9042C96.8359 36.0839 95.2359 36.7466 93.5675 36.7466H7.13699C5.72331 36.7482 4.34109 36.3294 3.16598 35.5435C1.99087 34.7576 1.07594 33.6401 0.537456 32.333C-0.00102827 31.0259 -0.13875 29.5882 0.141796 28.2027C0.422342 26.8171 1.10849 25.5462 2.11303 24.5516L25.0438 1.82212C25.6308 1.24056 26.3267 0.780333 27.0916 0.467706C27.8565 0.155078 28.6755 -0.00382426 29.5019 6.98771e-05C30.3282 0.00396402 31.1457 0.170579 31.9076 0.490402C32.6696 0.810225 33.3611 1.27699 33.9426 1.86405C34.5242 2.45112 34.9844 3.14697 35.297 3.9119C35.6097 4.67683 35.7686 5.49584 35.7647 6.32217C35.7608 7.14851 35.5942 7.96599 35.2743 8.72793C34.9545 9.48988 34.4877 10.1814 33.9007 10.7629L20.3888 24.1657H20.3805ZM79.6279 74.2124H6.43246C4.76414 74.2124 3.16414 73.5497 1.98445 72.37C0.804769 71.1903 0.14203 69.5903 0.14203 67.922C0.14203 66.2537 0.804769 64.6537 1.98445 63.474C3.16414 62.2943 4.76414 61.6316 6.43246 61.6316H92.8714C94.2844 61.6317 95.6654 62.0516 96.8392 62.8382C98.0131 63.6247 98.9267 64.7423 99.4641 66.049C100.002 67.3558 100.139 68.7928 99.8578 70.1775C99.577 71.5623 98.891 72.8324 97.887 73.8266L74.9562 96.5477C74.3727 97.1447 73.6761 97.6196 72.9072 97.9446C72.1382 98.2697 71.3122 98.4384 70.4774 98.441C69.6425 98.4436 68.8155 98.28 68.0445 97.9597C67.2736 97.6395 66.5741 97.1689 65.9868 96.5755C65.3995 95.9821 64.9363 95.2778 64.624 94.5035C64.3117 93.7293 64.1567 92.9006 64.168 92.0659C64.1792 91.2311 64.3565 90.4069 64.6895 89.6413C65.0226 88.8758 65.5047 88.1842 66.1077 87.6069L79.6279 74.2124Z' fill='%23333333'/%3E %3C/svg%3E ");
        background-size: 100% auto;
        background-position: 50%;
        background-repeat: no-repeat;
        display: inline-block;
        vertical-align: middle;
        width: 16px;
        height: 16px;
    }
}
.address_box {
    margin: 0 15px;
    padding: 10px;
    background: #f6f6f6;
}
.address_input {
    padding: 0;
    width: 100%;
    font-size: 14px;
    line-height: 20px;
    min-height: 40px;
    background: #f6f6f6;
}
.select_item_box {
    padding: 15px;
}

.select_box {
    margin-top: 10px;
    .option {
        padding-left: 24px;
        padding-right: 40px;
        font-size: 18px;
        position: relative;
        display: inline-block;
        &:nth-child(2n) {
            margin-left: 20%;
        }
        .mark {
            width: 18px;
            height: 18px;
            border: 1px solid #999;
            border-radius: 50%;
            margin-top: -10px;
            position: absolute;
            top: 50%;
            left: 0;
        }
        &.checked {
            .mark {
                border-color: #ff6a01;
                &::after {
                    content: '';
                    width: 10px;
                    height: 10px;
                    background-color: #ff6a01;
                    border-radius: 50%;
                    position: absolute;
                    top: 4px;
                    left: 4px;
                }
            }
        }
    }
}
</style>