function upload(src){
    return new Promise((resolve, reject) => {
        const srcArr = src.split('.');
        const cloudPath = randomString(16) + '.' + srcArr[srcArr.length - 1];
        wx.cloud.uploadFile({
            cloudPath,
            filePath: src,
            sizeType: 'compressed',//只上传压缩图
            success: res => {
                resolve(res.fileID)
            },
            fail: err => {
                console.log(err);
                reject(err)
            }
        })
    })
}

function randomString(len) {
    len = len || 32;
    let chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678';
    let maxPos = chars.length;
    let pwd = '';
    for (let i = 0; i < len; i++) {
        pwd += chars.charAt(Math.floor(Math.random() * maxPos));
    }
    return pwd;
}
export default upload;