function showSheetAd () {
  if (uni.createInterstitialAd) {
    if (this.adInstance) {
      this.adInstance
        .show()
        .then(() => {
          this.adInstance = null
        })
        .catch(err => {
          console.error(err)
        })
      return
    }
    const adInstance = uni.createInterstitialAd({
      adUnitId: 'adunit-f91d5433c3a85af3'
    })

    const loadListener = () => {
      adInstance
        .show()
        .then(() => {
          this.adInstance = null
        })
        .catch(err => {
          // 广告展示失败，暂存实例
          this.adInstance = adInstance
        })
    }
    const closeListener = () => {
      adInstance.destroy()
    }
    adInstance.onLoad(loadListener)
    adInstance.onClose(closeListener)
    adInstance.load()
  }
}
/**
 * 根据湿度、价格调整和其他因素计算作物的调整后数据。
 *
 * @param {Object} options - 计算所需的选项。
 * @param {Array<number>} options.pickerVal - 选中的地区/作物选择值。
 * @param {Array<Array<string>>} options.pickerArr - 包含不同国家和作物标签的二维数组。
 * @param {Object} options.humidityPercentMap - 湿度值与百分比调整的映射。
 * @param {Object} options.currentSeries - 当前作物数据系列，包含名称和数据数组。
 * @param {number} [options.isCornCobPriceAdjustment=40] - 玉米棒价格调整值（默认为40）。
 * @param {number} [options.priceAdjustment=50] - 玉米粒价格调整值（默认为50）。
 *
 * @returns {Object} - 返回一个包含修改后的系列名称和调整后的数据数组的对象。
 * @property {string} name - 修改后的系列名称，包括作物标签。
 * @property {string} type - 系列类型，设置为 'line'。
 * @property {Array<number>} data - 根据湿度和价格调整计算后的作物数据。
 */
function calculateCornTheoryChartData (options) {
  const defaultHumidityPercentMap = {
    40: 0.76,
    30: 0.81,
    25: 0.83,
    20: 0.85,
    14: 0.88
  }
  const humidityRange = [14, 40]
  const humidityNumberArr = Array.from(
    { length: humidityRange[1] - humidityRange[0] + 1 },
    (_, i) => i + humidityRange[0]
  )

  const {
    pickerVal,
    pickerArr,
    currentSeries,
    isCornCobPriceAdjustment = 40,
    priceAdjustment = 50,
    humidityPercentMap = defaultHumidityPercentMap
  } = options

  const isCornCob = pickerVal[0] === 0 // 判断是否是玉米棒
  const cornLabel = `${pickerArr[0][pickerVal[0]]}-${pickerArr[1][pickerVal[1]]}` // 获取作物标签
  const cropHumidity = humidityNumberArr[pickerVal[1]] // 获取作物对应的湿度

  /**
   * 根据起始值和结束值进行线性插值
   * @param {number} start - 起始湿度
   * @param {number} end - 结束湿度
   * @returns {number} - 插值后的百分比
   */
  const interpolatePercent = (start, end) => {
    return (
      humidityPercentMap[start] +
      ((humidityPercentMap[end] - humidityPercentMap[start]) * (cropHumidity - start)) /
      (end - start)
    )
  }

  /**
   * 计算湿度对应的百分比
   * @param {number} cropHumidity - 当前作物湿度
   * @returns {number} - 湿度百分比
   */
  const getHumidityPercent = cropHumidity => {
    if (humidityPercentMap[cropHumidity]) {
      return humidityPercentMap[cropHumidity]
    } else if (cropHumidity < 20) {
      return interpolatePercent(14, 20)
    } else if (cropHumidity < 25) {
      return interpolatePercent(20, 25)
    } else if (cropHumidity < 30) {
      return interpolatePercent(25, 30)
    } else {
      return interpolatePercent(30, 40)
    }
  }
  const percent = getHumidityPercent(cropHumidity)

  return {
    name: `${currentSeries?.name}-${cornLabel}（理论）`,
    type: 'line',
    picker: true,
    data: currentSeries?.data.map(val => {
      if (isCornCob) {
        // 计算玉米棒价格，玉米棒价格=本地干粒价格折合系数=（厂价-4分）（100-粒湿度）/86*出粒率。
        return (
          Math.round(
            (((val * 1000 - isCornCobPriceAdjustment) * (100 - cropHumidity)) / 86) * percent
          ) / 1000
        )
      } else {
        // 计算玉米粒价格，玉米粒价格=（厂价-5分）*（100-粒湿度）/86。
        return Math.round(((val * 1000 - priceAdjustment) * (100 - cropHumidity)) / 86) / 1000
      }
    })
  }
}
/**
 * 生成从开始日期到结束日期之间的所有日期
 * @param {string} startDate - 开始日期，格式为 'YYYY-MM-DD'
 * @param {string} endDate - 结束日期，格式为 'YYYY-MM-DD'
 * @returns {Array<Date>} - 包含所有日期的数组
 * @throws {Error} - 如果输入日期格式无效或开始日期晚于结束日期
 */
export function generateDates (startDate, endDate) {
  const start = new Date(startDate)
  const end = new Date(endDate)

  if (isNaN(start) || isNaN(end)) {
    throw new Error('无效的日期格式')
  }

  if (start > end) {
    throw new Error('开始日期不能晚于结束日期')
  }

  const dates = []
  let currentDate = new Date(start)

  while (currentDate <= end) {
    dates.push(new Date(currentDate))
    currentDate.setDate(currentDate.getDate() + 1)
  }

  return dates
}
export default {
  showSheetAd,
  calculateCornTheoryChartData,
  generateDates
}
