<template>
    <div class="root">
        <div class="fast_btn_box">
            <div class="app_btn" :class="{ un_focus: currentIndex != 0 }" @click="showToday">今日</div>
            <div class="app_btn" :class="{ un_focus: currentIndex != 1 }" @click="showLastWeek">近7天</div>
            <div class="app_btn" :class="{ un_focus: currentIndex != 2 }" @click="showLastMonth">近30天</div>
        </div>
        <div class="picker_box">
            <picker mode="date" :start="minDate" :end="maxDate" @change="bindStartDateChange">
                <view class="picker">开始：{{startTime || '--'}}</view>
            </picker>
            <span>至</span>
            <picker mode="date" v-model="endTime" :start="minDate" :end="maxDate" @change="bindEndDateChange">
                <view class="picker right">截止：{{endTime || '--'}}</view>
            </picker>
        </div>
        <div class="total_box">
            <div>
                <span v-if="currentIndex === 0">今日</span>
                <span v-else-if="currentIndex === 1">近7天</span>
                <span v-else-if="currentIndex === 2">近30天</span>
                <div v-else>{{ startTime }} ~ {{endTime}}</div>
                <span>由你带来的引流量</span>
            </div>
            <div class="number">{{ total }}</div>
        </div>
    </div>
</template>

<script>
import userManager from '../../api/userManager';
import { formatDate } from '../../utils/date';
export default {
    data () {
        const today = formatDate(new Date(), 'yyyy-MM-dd')
        return {
            minDate: '2024-06-01',
            maxDate: today,
            startTime: today,
            endTime: today,
            total: '--',
            currentIndex: 0
        }
    },
    onLoad () {
        this.loadMyCount()
    },
    methods: {
        countDate (num) {
            const today = Date.now();
            const start = today - 3600 * 24 * 1000 * num;
            const format = 'yyyy-MM-dd';
            this.startTime = formatDate(new Date(start), format)
            this.endTime = formatDate(new Date(today), format)
        },
        showToday () {
            this.currentIndex = 0;
            this.countDate(0)
            this.loadMyCount()
        },
        showLastWeek () {
            this.currentIndex = 1;
            this.countDate(7)
            this.loadMyCount()
        },
        showLastMonth () {
            this.currentIndex = 2;
            this.countDate(30)
            this.loadMyCount()
        },
        bindStartDateChange (e) {
            this.startTime = e.detail.value;
            if (this.endTime && this.endTime < this.startTime) {
                this.endTime = null
            }
            if (this.endTime) {
                this.currentIndex = 3;
                this.loadMyCount()
            }
        },
        bindEndDateChange (e) {
            this.endTime = e.detail.value;
            if (this.startTime && this.startTime > this.endTime) {
                this.startTime = null
            }
            if (this.startTime) {
                this.currentIndex = 3;
                this.loadMyCount()
            }
        },
        loadMyCount () {
            uni.showLoading()
            userManager.queryMyShareCount(`${this.startTime} 00:00:00`, `${this.endTime} 23:59:59`).then(data => {
                this.total = data
            }).catch(err => {
                console.log(err)
            }).then(() => {
                uni.hideLoading()
            })
        }
    }
}
</script>

<style lang="scss">
.fast_btn_box {
    padding: 20px 0;
    display: flex;
    justify-content: space-between;
    .app_btn {
        width: 31%;
        margin-top: 0;
        &.un_focus {
            background-color: #f6f6f6;
            border-color: #f6f6f6;
            color: #999;
        }
    }
}
.picker_box {
    margin-top: 20px;
    font-size: 14px;
    line-height: 50px;
    text-align: center;
    position: relative;
    .picker {
        background: #f6f6f6;
        position: absolute;
        text-align: left;
        width: 46%;
        box-sizing: border-box;
        padding: 0 30rpx;
        left: 0;
        top: 0;
        &.right {
            left: auto;
            right: 0;
        }
    }
}
.total_box {
    margin-top: 60px;
    text-align: center;
    font-size: 20px;
    .number {
        font-size: 32px;
    }
}
</style>