<script>
import userManager from './api/userManager'
import logManager from './api/logManager'
import pointsManager from './api/pointsManager'
import orderManager from './api/orderManager'
import cityManager from './api/cityManager'
import configManager from './api/configManager'
import { OPEN_WX_MINI_APP } from './constant/event'
import { cropArr } from './constant/common'

export default {
  globalData: {
    cropArr,
    code: null,
    launchIng: true,
    userInfo: {},
    orderInfo: {},
    newOrder: {},
    inviteUserId: null,
    crops: [],
    cropsMap: {},
    collectionsMap: {},
    deviceInfo: null
  },
  async onLaunch(options) {
    uni.showLoading({ title: '启动中' })
    const { shareUid, shareId } = options.query || {}
    if (shareUid) {
      this.globalData.inviteUserId = shareUid
      // const { id } = JSON.parse(decodeURIComponent(shareUser))
      // this.globalData.inviteUserId = id;
    }
    if (shareId) {
      const res = await pointsManager.checkShareAndAssistance({ id: shareId })
      // uni.showToast({
      //   title: res,
      //   icon: 'none',
      //   duration: 3000
      // })
    }
    try {
      const [sysConfig, cropsInfo, cityArr, deviceInfo] = await Promise.all([
        configManager.fetchSystemConfig(),
        orderManager.fetchCrops(),
        cityManager.fetchAllCities(),
        configManager.getDeviceInfo()
      ])

      this.$store.commit('sysConfig', sysConfig)
      Object.assign(this.globalData, cropsInfo)
      this.globalData.cityArr = cityArr
      this.globalData.deviceInfo = deviceInfo

      const openCount = configManager.getOpenCount() + 1
      this.globalData.openCount = openCount
      configManager.setOpenCount(openCount)

      this.initApp()
    } catch (err) {
      console.error('App初始化失败:', err)
      this.launchFinish()
    }
  },
  methods: {
    initApp(redLoginAgain) {
      // 获取用户的 openId, unionId
      // https://developers.weixin.qq.com/community/develop/doc/000cacfa20ce88df04cb468bc52801
      // 小程序官方最新调整，不在需要用户授权获取加密信息。获取真实的用户头像名字，换成 getUserProfile接口，该接口每次都需要弹窗授权
      if (redLoginAgain || !userManager.checkLogin()) {
        uni.login({
          success: res => {
            const code = res.code
            const { inviteUserId } = this.globalData
            userManager
              .miniLoginByCode(code, inviteUserId)
              .then(user => {
                this.globalData.userInfo = user
                this.$store.commit('isLogin', true)
                this.onLoginSuccess()
              })
              .catch(err => {
                console.error('登录失败:', err)
                this.launchFinish()
              })
            // userManager.getWxInfo(code).then(uniUser => {
            //     const { openId, unionId } = uniUser;
            //     const { inviteUserId } = this.globalData;
            //     userManager.uniLogin(openId, unionId, inviteUserId).then((user) => {
            //         this.globalData.userInfo = user;
            //         this.onLoginSuccess();
            //     }).catch(err => {
            //         console.log(err);
            //         this.launchFinish();
            //     })
            // }).catch(err => {
            //     console.log(err);
            //     this.launchFinish();
            // }).then(() => {
            //     this.globalData.code = null; // 请求用过一次的code失效，清空code
            // })
          },
          fail: err => {
            console.error('获取登录凭证失败:', err)
            this.launchFinish()
          }
        })
      } else {
        this.$store.commit('isLogin', true)
        this.onLoginSuccess()
      }
    },
    launchFinish() {
      //启动结束
      logManager.addLog(OPEN_WX_MINI_APP)
      logManager.addStartRecord(this.globalData.inviteUserId)
      uni.hideLoading()
      this.globalData.launchIng = false
    },
    onLoginSuccess() {
      this.launchFinish()
      this.startCurrentPageLoginCallback()
    },
    startCurrentPageLoginCallback() {
      const pages = getCurrentPages()
      if (pages.length > 0) {
        const currentPage = pages[pages.length - 1]
        currentPage.$vm.onLoginSuccess && currentPage.$vm.onLoginSuccess()
      } else {
        // 登录完成后第一个页面未能实例化完成，延迟执行，直到第一个页面实例完成
        setTimeout(this.startCurrentPageLoginCallback, 20)
      }
    }
  },
  onShow: function () {
    console.log('App Show')
  },
  onHide: function () {
    console.log('App Hide')
  }
}
</script>

<style lang="scss">
@import './app.scss';
</style>
