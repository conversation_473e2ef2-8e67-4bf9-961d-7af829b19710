import http from '../utils/http'
import storeManager from '../utils/storeManager'
const tradePriceShowKey = 'trade_price_show_key'

function getOpenCount() {
  return storeManager.get(tradePriceShowKey) || 0
}
function setOpenCount(value) {
  storeManager.set(tradePriceShowKey, value, 'd365') || 0
}

function getSysConfig() {
  return storeManager.get('systemConfig')
}
function setSysConfig(data) {
  storeManager.set('systemConfig', data)
}

function queryConfig() {
  return http.get('/api/nz/config/mini')
}

function fetchSystemConfig() {
  return new Promise(function (resolve, reject) {
    http
      .get('/common/sys/config')
      .then(data => {
        setSysConfig(data)
        resolve(data)
      })
      .catch(err => {
        reject(err)
      })
  })
}

function getDeviceInfo() {
  return new Promise(function (resolve, reject) {
    const app = getApp()
    if (app && app.globalData.deviceInfo) {
      resolve(app.globalData.deviceInfo)
    } else {
      wx.getSystemInfo({
        success: res => {
          const { statusBarHeight, screenHeight, safeArea, platform } = res
          let navBarHeight = platform === 'android' ? 48 : 44
          // menu为胶囊，判断是否能读到胶囊位置，读到胶囊说明高度需要根据胶囊重新计算
          const menuBounding = wx.getMenuButtonBoundingClientRect()
          if (menuBounding) {
            const { height, top } = menuBounding
            navBarHeight = height + (top - statusBarHeight) * 2
          }
          const deviceInfo = {
            menuBounding,
            statusBarHeight, // 状态栏高度
            navBarHeight, // 导航栏高度
            bottomInsetSafeArea: screenHeight - safeArea.bottom,
            totalBarHeight: statusBarHeight + navBarHeight // 状态栏加导航栏
          }
          resolve(deviceInfo)
        },
        fail(err) {
          reject(err)
        }
      })
    }
  })
}

function uploadWxFile(filePath, namespace) {
  return http.upload('/common/files/wx-uploads', filePath, { namespace })
}

export default {
  getOpenCount,
  setOpenCount,
  queryConfig,
  fetchSystemConfig,
  getDeviceInfo,
  getSysConfig,
  uploadWxFile
}
