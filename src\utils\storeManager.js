import { stringToTime } from '../utils/date'

function set(key, value, expires){
    expires = expires || 'h4'; //默认缓存4小时内有效
    const maxAge = Date.now() + stringToTime(expires);
    wx.setStorageSync(key, {
        data: value,
        expires: maxAge
    })
}

function get(key){
    const storeData = wx.getStorageSync(key);
    const { data, expires } = storeData;
    if(data && expires && expires > Date.now()){
        return data;
    }else{
        return null;
    }
}

function clear(key){
    wx.removeStorage({
        key
    })
}

function clearAll(){
    wx.clearStorage();
}

export default {
    set,
    get,
    clear,
    clearAll
}