<template>
  <view
    :style="{ position: 'fixed', top: paddingTopHeight, left: 0, width: '100%', height: '100%' }"
  >
    <scroll-view
      class="scroll-view"
      scroll-y
      refresher-enabled
      scroll-with-animation
      @scrolltolower="loadMore"
      @refresherrefresh="onRefresh"
      :refresher-triggered="isRefreshing"
      :style="{ height: `calc(100vh - ${paddingTopHeight}px)` }"
    >
      <no-data v-if="total === 0" />
      <template v-else>
        <div style="padding-bottom: 60px">
          <div
            v-for="(item, index) in traders"
            :key="index"
            class="traders_list"
            @click="handleItemClick(item)"
          >
            <div class="traders_list_top">
              <image
                class="traders_list_avatar"
                :src="item.avatar || defaultAvatar"
                mode="aspectFill"
              />
              <div class="traders_list_info">
                <div class="traders_info_name">
                  <span>{{ typeLabels[item.type] }}</span>
                  <text> {{ item.name }} </text>
                </div>
                <template v-if="activePage === 2 && item.type === 1">
                  <div class="traders_info_crops">
                    <span>收购品类</span>
                    <div>
                      <div v-for="(crop, index) in item.purchaseCrops" :key="index">
                        {{ crop }}
                      </div>
                    </div>
                  </div>
                  <div class="traders_info_cars">
                    <span>运输车辆</span>
                    <div>
                      <div v-for="(car, index) in item.transportCars" :key="index">
                        {{ car }}
                      </div>
                    </div>
                  </div>
                </template>
                <div v-if="activePage === 3" class="traders_info_cars">
                  <span>在售产品</span>
                  <div>
                    <div v-for="(product, index) in item.saleProducts" :key="index">
                      {{ product }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- <div v-if="item.description" class="traders_list_desc">{{ item.description }}</div> -->
            <div class="traders_list_extra">
              <div class="traders_info_distance">
                距离您<span>{{ item.distance || 0 }}</span
                >公里
              </div>
              <div style="display: inline-flex; align-items: center">
                <div class="traders_info_contacted">
                  <span>{{ item.contactedCount || 0 }}</span
                  >人已联系
                </div>
                <div class="traders_info_phone" @click.stop.prevent="onPhoneClick(item)">
                  电话联系
                </div>
              </div>
            </div>
          </div>
          <view class="loading" v-if="loading"></view>
          <view class="loaded_finish" v-else-if="total > 0 && total <= (pageIndex + 1) * pageSize">
            共{{ total }}个{{ activePage === 2 ? '收购' : '设备' }}商，已加载完毕
          </view>
        </div>
      </template>
    </scroll-view>
    <view class="add_traders" @click="changeToTrader"
      >成为{{ activePage === 2 ? '收购' : '设备' }}商</view
    >
  </view>
</template>

<script>
import traderManager from '../../api/traderManager'
import orderManager from '../../api/orderManager'

export default {
  props: {
    paddingTopHeight: {
      type: Number,
      default: 0
    },
    activePage: {
      type: Number,
      default: 2
    }
  },
  data() {
    return {
      typeLabels: { 1: '收购商', 2: '粮庄', 3: '设备商' },
      defaultAvatar: '/static/images/user_default.png',
      traders: [],
      page: 0,
      pageIndex: 0,
      pageSize: 10,
      total: -1,
      loading: false,
      isRefreshing: false,
      _freshing: false,
      crops: [],
      location: {},
      cropMap: {}
    }
  },
  watch: {
    activePage(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.loadTraders(true)
      }
    }
  },
  async created() {
    try {
      this._freshing = true
      uni.showLoading({ title: '加载中...' })
      const { latitude, longitude } = await uni.getFuzzyLocation()
      this.location = { latitude, longitude }
      await Promise.all([this.loadTraders(true), this.getCrops()])
    } catch (error) {
      if (error.errMsg === 'getFuzzyLocation:fail auth deny') {
        uni.showModal({
          title: '温馨提示',
          content: '请授权位置信息，查看附近的收购商',
          showCancel: false,
          success: () => {
            uni.openSetting()
          }
        })
      }
    } finally {
      uni.hideLoading()
      this._freshing = false
    }
  },
  methods: {
    async getCrops() {
      try {
        const res = await orderManager.fetchCrops()
        this.crops = res.crops
        this.cropMap = this.crops.reduce((acc, cur) => {
          acc[cur.id] = cur
          return acc
        }, {})
      } catch (error) {
        console.error('获取作物列表失败:', error)
      }
    },
    async loadTraders(refresh) {
      try {
        if (this.loading) return
        this.loading = true
        if (refresh) {
          this.traders = []
          this.pageIndex = 0
        } else {
          this.pageIndex++
        }
        const params = {
          type: this.activePage === 2 ? [1, 2] : 3,
          pageIndex: this.pageIndex,
          pageSize: this.pageSize,
          ...this.location
        }
        const { list, total } = await traderManager.queryTraders(params)

        this.traders.push(
          ...list.map(item => {
            const processStringList = (str, processor = v => v) =>
              (str || '').split(',').filter(Boolean).map(processor)
            const [purchaseCrops, transportCars, saleProducts] = [
              processStringList(item.purchaseCrops, item => {
                const num = Number(item)
                if (!isNaN(num) && /^[-+]?\d+(\.\d+)?$/.test(item.trim())) {
                  return num
                }
                return item
              }),
              processStringList(item.transportCars),
              processStringList(item.saleProducts)
            ]
            return {
              ...item,
              purchaseCrops: purchaseCrops.map(crop => this.cropMap[crop]?.name || crop),
              transportCars,
              saleProducts
            }
          })
        )
        this.total = total
      } catch (error) {
        uni.showToast({
          title: '获取收购商列表失败',
          icon: 'none'
        })
        throw error
      } finally {
        this.loading = false
        this.isRefreshing = false
      }
    },

    changeToTrader() {
      uni.navigateTo({
        url: '/pages/my/info'
      })
    },

    loadMore() {
      const { loading, total, pageIndex, pageSize } = this
      if (!loading) {
        if (total > (pageIndex + 1) * pageSize) {
          this.loadTraders()
        }
      }
    },
    onRefresh() {
      if (this._freshing) return
      this._freshing = true
      this.isRefreshing = true
      this.loadTraders(true).finally(() => {
        this.isRefreshing = false
        this._freshing = false
      })
    },

    onPhoneClick(item) {
      uni
        .makePhoneCall({
          phoneNumber: item.mobile
        })
        .then(() => {
          traderManager.contactTrader(item.id).then(() => {
            console.log('makePhoneCall success')
            item.contactedCount++
          })
        })
        .catch(err => {
          console.log('makePhoneCall fail', err)
        })
    },
    handleItemClick(item) {
      uni.navigateTo({
        url: `/pages/traders/detail?id=${item.id}&distance=${item.distance}`
      })
    }
  }
}
</script>

<style lang="scss">
.scroll-view {
  box-sizing: border-box;
}

.traders_list {
  display: flex;
  flex-direction: column;
  margin: 10px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0px 0px 6px 2px rgba(0, 0, 0, 0.05);
  // padding: 10px;
}

.traders_list_top {
  display: flex;
  padding: 12px;
}

.traders_list_avatar {
  width: 66px;
  height: 66px;
  border-radius: 4px;
  margin-right: 13px;
  flex-shrink: 0;
}

.traders_list_info {
  flex: 1;
  min-width: 0;
}

.traders_info_name {
  display: flex;
  align-items: center;
  font-size: 16px;
  color: #333;
  font-weight: bold;
  min-width: 0;
  text {
    flex: 1;
    min-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  span {
    display: inline-block;
    flex-shrink: 0;
    font-size: 12px;
    color: #fff;
    font-weight: 500;
    background: #0d7fff;
    border-radius: 4px;
    padding: 0 4px;
    margin-right: 6px;
  }
}

.traders_info_crops,
.traders_info_cars {
  display: flex;
  align-items: baseline;
  > div {
    display: inline-flex;
    flex-wrap: wrap;
    align-items: center;
    margin: 3px -3px 0;
    > div {
      box-sizing: border-box;
      color: #ff6a01;
      // background-color: #ff6a01;
      border: 1px solid #ff6a01;
      border-radius: 4px;
      padding: 0 8px;
      font-size: 12px;
      margin: 3px;
      min-height: 20px;
      line-height: 18px;
    }
  }
  > span {
    font-size: 12px;
    color: #666;
    margin-right: 6px;
    flex-shrink: 0;
  }
}

.traders_info_cars {
  > div > div {
    color: #666;
    border: 1px solid #999;
    background: none;
  }
}

.traders_list_desc {
  background: rgba(238, 238, 238, 0.6);
  border-radius: 4px;
  padding: 6px 10px;
  margin: 0 12px 12px;
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.traders_list_extra {
  display: inline-flex;
  align-items: center;
  justify-content: space-between;
  border-top: 1px solid #eee;
  padding: 12px;
  .traders_info_contacted,
  .traders_info_distance {
    font-size: 12px;
    color: #666;
    span {
      color: #333;
      font-weight: bold;
      margin: 0 2px;
    }
  }
  .traders_info_contacted {
    margin-right: 6px;
  }
  .traders_info_phone {
    font-size: 12px;
    color: #fff;
    padding: 3px 12px;
    background: #ff6a01;
    border-radius: 33px;
  }
}

.loaded_finish {
  font-size: 14px;
  color: #8a96a3;
  text-align: center;
  margin-top: 15px;
}

.loading {
  margin-top: 10px;
  height: 40px;
}

.add_traders {
  width: 80%;
  height: 40px;
  position: fixed;
  right: 10%;
  bottom: 10px;
  background: #ff6a01;
  line-height: 40px;
  border-radius: 40px;
  text-align: center;
  color: #fff;
  .icon {
    width: 100%;
    height: 100%;
  }
  transition: all ease-out 0.3s;
}
</style>
