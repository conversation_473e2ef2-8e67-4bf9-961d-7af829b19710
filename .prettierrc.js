// https://prettier.io/docs/en/options.html
module.exports = {
    // Prettier option
    trailingComma: 'none', // 末尾需要有逗号，多行时，尽可能打印尾随的逗号
    tabWidth: 2, // 使用 2 个空格缩进，会忽略vetur的tabSize配置
    useTabs: false, // 不使用缩进符，而使用空格
    semi: false, // 句尾是否加分号;
    singleQuote: true, // 使用单引号而不是双引号
    quoteProps: 'as-needed', // 对象的 key 仅在必要时用引号
    arrowParens: 'avoid', // allow paren-less arrow functions 箭头函数的参数使用圆括号
    bracketSpacing: true, // 在对象文字中的括号之间打印空格
    eslintIntegration: true,
    bracketSameLine: false, // 将多行 HTML（HTML、JSX、Vue、Angular）元素的 > 放在最后一行的末尾，而不是单独放在下一行（不适用于自关闭元素)
    printWidth: 100, // 一行最多 100 字符
    jsxSingleQuote: true, // 在 JSX 中使用单引号代替双引号
    rangeStart: 0, // 每个文件格式化的范围是文件的全部内容
    rangeEnd: Infinity,
    requirePragma: false, // 不需要写文件开头的 @prettier
    insertPragma: false, // 不需要自动在文件开头插入 @prettier
    proseWrap: 'preserve', // 使用默认的折行标准
    htmlWhitespaceSensitivity: 'css', // 根据显示样式决定 html 要不要折行
    vueIndentScriptAndStyle: false, // vue 文件中的 script 和 style 内不用缩进
    endOfLine: 'lf', // 换行符使用 lf
    embeddedLanguageFormatting: 'auto' // 控制 Prettier 是否格式化文件中嵌入的引用代码
}
