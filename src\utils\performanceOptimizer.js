/**
 * 性能优化工具
 * 提供应用性能优化相关的功能
 */

/**
 * 延迟加载非关键资源
 * @param {Function} loadFn 加载函数
 * @param {Number} delay 延迟时间(毫秒)
 * @returns {Promise} 返回加载结果的Promise
 */
export function lazyLoad(loadFn, delay = 3000) {
  return new Promise(resolve => {
    setTimeout(async () => {
      try {
        const result = await loadFn()
        resolve(result)
      } catch (err) {
        console.error('延迟加载资源失败:', err)
        resolve(null)
      }
    }, delay)
  })
}

/**
 * 预加载页面
 * @param {String} url 页面路径
 */
export function preloadPage(url) {
  // 使用uni.preloadPage预加载页面
  // 注意：仅App和H5平台支持
  if (typeof uni.preloadPage === 'function') {
    uni.preloadPage({
      url,
      success: () => {
        console.log(`页面预加载成功: ${url}`)
      },
      fail: err => {
        console.error(`页面预加载失败: ${url}`, err)
      }
    })
  }
}

/**
 * 优化图片加载
 * @param {String} src 图片路径
 * @param {Object} options 配置选项
 * @returns {String} 优化后的图片路径
 */
export function optimizeImage(src, options = {}) {
  const { width, height, quality = 80 } = options

  // 如果是网络图片，可以使用CDN的图片处理参数
  if (src && src.startsWith('http')) {
    // 这里根据实际使用的CDN来调整参数
    const params = []

    if (width) params.push(`w_${width}`)
    if (height) params.push(`h_${height}`)
    if (quality) params.push(`q_${quality}`)

    if (params.length > 0) {
      const separator = src.includes('?') ? '&' : '?'
      return `${src}${separator}${params.join('&')}`
    }
  }

  return src
}

/**
 * 缓存优化器
 * 提供更智能的缓存策略
 */
export const cacheOptimizer = {
  /**
   * 获取缓存，如果过期则在后台刷新
   * @param {String} key 缓存键
   * @param {Function} fetchFn 获取数据的函数
   * @param {Number} expireTime 过期时间(毫秒)
   * @param {Object} storeManager 存储管理器
   */
  async getWithBackgroundRefresh(key, fetchFn, expireTime, storeManager) {
    // 检查是否有缓存
    const cached = storeManager.get(key)

    // 如果有缓存，先返回缓存数据
    if (cached) {
      // 检查是否接近过期（例如，还有20%的有效期）
      const now = Date.now()
      const metadata = storeManager.get(`${key}_metadata`)

      if (metadata && metadata.expireTime) {
        const remainingTime = metadata.expireTime - now
        const shouldRefresh = remainingTime > 0 && remainingTime < expireTime * 0.2

        // 如果接近过期，在后台刷新
        if (shouldRefresh) {
          this._refreshInBackground(key, fetchFn, expireTime, storeManager)
        }
      }

      return cached
    }

    // 如果没有缓存，直接获取新数据
    return await fetchFn()
  },

  /**
   * 在后台刷新缓存
   * @private
   */
  _refreshInBackground(key, fetchFn, expireTime, storeManager) {
    setTimeout(async () => {
      try {
        const freshData = await fetchFn()
        storeManager.set(key, freshData, expireTime)
        storeManager.set(
          `${key}_metadata`,
          {
            expireTime: Date.now() + expireTime,
            lastRefresh: Date.now()
          },
          expireTime
        )
      } catch (err) {
        console.error(`后台刷新缓存失败: ${key}`, err)
      }
    }, 0)
  }
}

/**
 * 性能监控
 */
export const performanceMonitor = {
  /**
   * 记录时间点
   * @param {String} label 标签
   */
  timePoints: {},
  mark(label) {
    this.timePoints[label] = Date.now()
  },

  /**
   * 测量两个时间点之间的差值
   * @param {String} startLabel 开始标签
   * @param {String} endLabel 结束标签
   * @returns {Number} 时间差(毫秒)
   */
  measure(startLabel, endLabel) {
    if (!this.timePoints[startLabel] || !this.timePoints[endLabel]) {
      console.error('测量失败: 时间点不存在')
      return -1
    }

    return this.timePoints[endLabel] - this.timePoints[startLabel]
  },

  /**
   * 记录性能数据
   * @param {String} eventName 事件名称
   * @param {Object} data 性能数据
   */
  report(eventName, data) {
    // 这里可以实现上报到服务器的逻辑
    console.log('性能数据:', eventName, data)
  }
}
