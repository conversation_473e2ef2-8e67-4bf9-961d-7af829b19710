import http from '../utils/http'
import storeManager from '../utils/storeManager'
const STORE_KEY = 'city_manager_v2'

let cityArr = storeManager.get(STORE_KEY + 'cityArr');

function queryAllCities () {
    return http.get('/common/loadAllCities').then(areaArr => {
        const level1Areas = [];
        const level2Areas = [];
        const level3Areas = [];
        areaArr.forEach(area => {
            if (area.level === 1) {
                area.children = [];
                level1Areas.push(area);
            } else if (area.level === 2) {
                area.children = [];
                level2Areas.push(area);
            } else if (area.level === 3) {
                level3Areas.push(area);
            }
        });
        level3Areas.forEach(area => {
            const parentCode = area.code - area.code % 100000000;
            level2Areas.some(parent => {
                if (parent.code === parentCode) {
                    parent.children.push(area)
                    return true;
                }
            })
        })
        level2Areas.forEach(area => {
            const parentCode = area.code - area.code % 10000000000;
            level1Areas.some(parent => {
                if (parent.code === parentCode) {
                    parent.children.push(area)
                    return true;
                }
            })

        })
        storeManager.set(STORE_KEY + 'cityArr', level1Areas, 'd30');
        cityArr = level1Areas;
        return cityArr;
    })
}

function fetchAllCities () {
    if (cityArr) {
        return Promise.resolve(cityArr);
    } else {
        return queryAllCities();
    }
}


function codeToValue (code, cityMapArr) {
    let provinceCode = code - code % Math.pow(10, 10);
    let arr = [];
    cityMapArr.some(province => {
        if (province.code === provinceCode) {
            arr.push({
                code: province.code,
                name: province.name
            })
            if (code > province.code) {
                let cityCode = code - code % Math.pow(10, 8);
                province.children.some(city => {
                    if (city.code === cityCode) {
                        arr.push({
                            code: city.code,
                            name: city.name
                        })
                        if (code > city.code) {
                            city.children.some(town => {
                                if (town.code === code) {
                                    arr.push({
                                        code,
                                        name: town.name
                                    })
                                    return true;
                                }
                            })
                        }
                        return true;
                    }
                })
            }
            return true;
        }
    })
    return arr;
}

export default {
    queryAllCities,
    fetchAllCities,
    codeToValue
}