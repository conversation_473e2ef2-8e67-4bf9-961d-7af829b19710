import http from '../utils/http'
import storeManager from '../utils/storeManager'
import pointsManager from './pointsManager'
import { AUTO_VIEW_GROUP_QRCODE } from '../constant/event'
const STORE_KEY = 'user_manager_v2_'

function queryTraders(params) {
  return new Promise(function (resolve, reject) {
    http
      .post('/trader', params)
      .then(data => {
        resolve(data)
      })
      .catch(err => {
        reject(err)
      })
  })
}

function getTraderInfo(id, params) {
  return new Promise(function (resolve, reject) {
    http
      .get(`/trader/${id}`, params)
      .then(data => {
        resolve(data)
      })
      .catch(err => {
        reject(err)
      })
  })
}

function contactTrader(traderId) {
  return http.get('/trader/contacted/' + traderId)
}

export default {
  queryTraders,
  getTraderInfo,
  contactTrader
}
