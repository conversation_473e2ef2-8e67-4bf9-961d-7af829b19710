<template>
    <view class="detail_root">
        <view class="banner_video">
            <video v-if="order && order.overallVideos" class="video" width="400" height="400" :src="order.overallVideos[0]" controls></video>
        </view>
        <order-info :info="order" :trade="trade"></order-info>
    </view>
</template>

<script>
import orderManager from '../../api/orderManager'
import OrderInfo from '../../components/OrderInfo.vue'
const app = getApp();
export default {
    components: {
        OrderInfo
    },
    data () {
        return {
            id: null,
            order: {},
            trade: {}
        }
    },
    onLoad (options) {
        const trade = app.globalData.viewTrade;
        if (trade) {
            const order = trade.order;
            this.trade = trade;
            this.order = order;
        } else {
            console.log(options)
            const id = options.id;
            orderManager.queryTradeById(id).then(trade => {
                const order = trade.order;
                this.trade = trade;
                this.order = order;
            })
        }
    }
}
</script>

<style lang="scss">
page {
    background-color: #f1f1f1;
}
.detail_root {
    padding-bottom: 80px;
}
.banner_video .video {
    display: block;
    width: 100%;
    height: 422rpx;
}
</style>

