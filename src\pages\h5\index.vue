<template>
    <web-view :src="url"></web-view>
</template>

<script>
import { requestHost } from '../../constant/common'
export default {
    data () {
        return {
            url: null
        }
    },
    onLoad (options) {
        const title = options.title ? decodeURIComponent(options.title) : '千万仓';
        uni.setNavigationBarTitle({ title })
        if (options.url) {
            let url = decodeURIComponent(options.url);
            if (url.substring(0, 4) != 'http') {
                url = requestHost + url;
            }
            this.url = url
        } else {
            uni.navigateBack()
        }
    }
}
</script>