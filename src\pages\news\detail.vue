<template>
  <div v-if="news.id" class="news-detail">
    <div class="news-header">
      <h3 class="news-title">{{ news.title || '--' }}</h3>
      <div class="news-meta">
        <view v-show="news.category" class="news-source"
          >资讯分类：<span class="news-category">{{ news.category }}</span></view
        >
        <view class="news-source">来源：{{ sourceLabels[news.source] }}</view>
        <view class="news-date">时间：{{ publishTime || '--' }}</view>
        <view v-show="news.author" class="news-source">作者：{{ news.author }}</view>
      </div>
    </div>
    <div v-if="news.photos && news.photos.length > 0" class="news-photos-box">
      <image
        v-for="(url, index) in news.photos"
        :key="url"
        class="photo"
        :src="url"
        mode="widthFix"
        @click="onPhotoClick(index)"
      />
    </div>
    <div class="news-content" v-html="news.content"></div>
    <template v-if="news.files && Object.keys(news.files).length > 0">
      <div style="margin-top: 16px">查看附件：</div>
      <div class="news-files-box">
        <div v-for="(value, key) in news.files" :key="value" @click="handlePreviewFile(value)">
          {{ key }}
        </div>
      </div>
    </template>

    <div class="news-alert">
      <i class="el-icon-info"></i>
      本网原创及转载内容仅供参考，不构成投资建议，据此操作，风险自担。原创内容版权归千万仓左右，未经授权任何单位及个人不得转载、摘编或有悖原意的引用，本网保留对任何侵权行为进行追究的权利。转载内容来源于网络，目的在于传递更多信息，并不代表本网赞同其观点和对其真实性负责，也不对其精确性及完整性做出保证。若有信息、版权等问题请联系本网（<EMAIL>）处理。
    </div>
    <ad
      unit-id="adunit-7e4351e1e1b91210"
      ad-type="video"
      ad-theme="white"
      style="margin-top: 20px"
    ></ad>
  </div>
</template>

<script>
import infoManager from '../../api/infoManager'
import userManager from '../../api/userManager'
import messageBox from '../../utils/messageBox'
const { formatDate } = require('../../utils/date')

export default {
  data() {
    return {
      id: null,
      sourceLabels: {
        1: '中国农业农村信息网',
        2: '国家粮食交易中心',
        3: '中国大豆产业协会',
        4: '中国棉花协会',
        5: '山东省粮食和物资储备局',
        6: '河北省粮食和物资储备局',
        7: '吉林省粮食和物资储备局',
        8: '新疆维吾尔自治区粮食和物资储备局',
        9: '内蒙古自治区粮食和物资储备局'
      },
      news: {
        title: '',
        tag: '',
        publishTime: '',
        source: '',
        author: '',
        content: '',
        photos: []
      },
      adInstance: null
    }
  },
  computed: {
    publishTime() {
      return formatDate(new Date(this.news.publishTime), 'yyyy-MM-dd')
    }
  },
  onLoad(params) {
    this.id = params.newsId
    this.fetchNewsDetail()
  },
  onShow() {
    this.showSheetAd()
  },
  created(params) {
    // uni.setNavigationBarTitle({
    //   title: name
    // })
  },
  async onShareAppMessage() {
    const path = await userManager.getSharePath(`/pages/news/detail`, { newsId: this.id })
    return {
      title: this.news.title || '千万仓-玉米、小麦价格',
      path
    }
  },
  methods: {
    showSheetAd() {
      if (uni.createInterstitialAd) {
        if (this.adInstance) {
          this.adInstance
            .show()
            .then(() => {
              this.adInstance = null
            })
            .catch(err => {
              console.log(err)
            })
          return
        }
        const adInstance = uni.createInterstitialAd({
          adUnitId: 'adunit-3416bcbd923ee6c8'
        })
        const loadListener = () => {
          adInstance
            .show()
            .then(() => {
              this.adInstance = null
            })
            .catch(err => {
              // 广告展示失败，暂存实例
              this.adInstance = adInstance
            })
        }
        const closeListener = () => {
          adInstance.destroy()
        }
        adInstance.onLoad(loadListener)
        adInstance.onClose(closeListener)
        adInstance.load()
      }
    },
    onPhotoClick(index) {
      const urls = this.news.photos
      const current = urls[index]
      uni.previewImage({ urls, current })
    },
    async fetchNewsDetail() {
      try {
        uni.showLoading({ title: '加载中...' })
        const response = await infoManager.getInfoDetail(this.id)
        if (response.photos) {
          response.photos = response.photos.split(',')
        }
        if (response.files) {
          response.files = JSON.parse(response.files)
        }
        this.news = {
          ...response,
          content: response.content
            ? response.content
                .replace(/<font[^>]*>/g, '') // 移除 <font> 标签及其属性
                .replace(/<\/font>/g, '') // 移除 </font> 标签
                .replace(/style="([^"]*)"/g, (match, styles) => {
                  // 分割 style 内容为单个样式声明
                  const styleArray = styles.split(';')
                  // 过滤掉 font-size 和 font-family
                  const filteredStyles = styleArray.filter(style => {
                    return (
                      !style.includes('font-size') &&
                      !style.includes('font-family') &&
                      !style.includes('line-height')
                    )
                  })

                  const newStyle = filteredStyles.join(';').trim()
                  return `style="${newStyle}"`
                })
            : ''
        }
      } catch (error) {
        console.error('Error fetching news detail:', error)
        messageBox.alert('未查询到相关资讯信息，请重试').then(() => {
          uni.navigateBack()
        })
        throw new Error(error)
      } finally {
        uni.hideLoading()
      }
    },
    handlePreviewFile(url) {
      uni.downloadFile({
        url,
        success: function (res) {
          const filePath = res.tempFilePath
          uni.openDocument({
            filePath: filePath,
            showMenu: true,
            fileType: 'pdf',
            success: function (res) {
              console.log('打开文档成功')
            },
            fail: function (res) {
              console.log('打开文档失败', res)
            }
          })
        }
      })
    }
  }
}
</script>

<style lang="scss">
.news-detail {
  padding: 10px;
}

.news-header {
  margin-bottom: 20px;
}

.news-title {
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 10px;
  word-break: break-all;
}

.news-meta {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
}

.news-tag {
  margin-right: 10px;
}

.news-category {
  display: inline-flex;
  font-size: 12px;
  color: #ff8418;
  border: 1px solid #ff8418;
  padding: 0 6px;
  height: 20px;
  line-height: 20px;
  border-radius: 20px;
}

.news-date {
  margin-right: 10px;
}

.news-photos-box {
  margin-bottom: 15px;
  .photo {
    width: 100%;
  }
}

.news-files-box {
  word-break: break-all;
  color: rgba(29, 133, 244, 0.902);
  font-size: 14px;
  & > div {
    margin: 6px 0;
  }
}

.news-content {
  overflow: auto;
  font-size: 14px;
  line-height: 1.6 !important;
}

.news-alert {
  word-break: break-all;
  font-size: 12px;
  color: #666;
  margin-top: 10px;
  background: #d7d7d7;
  padding: 10px 12px;
  border-radius: 6px;
}

// 中国农业农村网资讯详情样式
.MsoNormalTable {
  font-size: 12px !important;
  width: auto !important;
  zoom: 1 !important;
  tr,
  td {
    width: auto !important;
    text-align: center !important;
    p {
      text-align: center !important;
    }
  }
}

// 棉花协会资讯详情样式
.tablewb {
  font-size: 12px !important;
  width: auto !important;
  min-width: 100% !important;
  td {
    // width: auto !important;
    text-align: center !important;
    padding: 0 6px !important;
  }
}
img,
image {
  display: inline-block !important;
  width: 100% !important;
}
</style>
