//地球半径
const EARTH_RADIUS = 6378137.0; //单位M
//PI值
const PI = Math.PI;

function getRad (d) {
    return d * PI / 180.0;
}
/**
 * 根据GPS两点经纬度信息计算距离
 * @param  {Number} latitude1 点1 GPS 经度
 * @param  {Number} longitude1 点1 GPS 纬度
 * @param  {Number} latitude2 点2 GPS 经度
 * @param  {Number} longitude2 点2 GPS 纬度
 * @return {Number}           两点距离
 */
function getDistance (latitude1, longitude1, latitude2, longitude2) {
    if ((latitude1 === latitude2) && (longitude1 === longitude2)) {
        return {
            value: 0,
            label: '0米'
        };
    }
    var lat1 = latitude1;
    var lng1 = longitude1;
    var lat2 = latitude2;
    var lng2 = longitude2;

    if (!lat1 || !lng1 || !lat2 || !lng2) {
        return {
            value: Number.NaN,
            label: `未获取位置`
        }
    }


    var f = getRad((lat1 + lat2) / 2);
    var g = getRad((lat1 - lat2) / 2);
    var l = getRad((lng1 - lng2) / 2);

    var sg = Math.sin(g);
    var sl = Math.sin(l);
    var sf = Math.sin(f);

    var s, c, w, r, d, h1, h2;
    var a = EARTH_RADIUS;
    var fl = 1 / 298.257;

    sg = sg * sg;
    sl = sl * sl;
    sf = sf * sf;

    s = sg * (1 - sl) + (1 - sf) * sl;
    c = (1 - sg) * (1 - sl) + sf * sl;

    w = Math.atan(Math.sqrt(s / c));
    r = Math.sqrt(s * c) / w;
    d = 2 * w * a;
    h1 = (3 * r - 1) / 2 / c;
    h2 = (3 * r + 1) / 2 / s;

    var tValue = parseInt(d * (1 + fl * (h1 * sf * (1 - sg) - h2 * (1 - sf) * sg)));
    return {
        value: tValue,
        label: tValue > 1000 ? `${Math.round(tValue / 100) / 10}公里` : `${tValue}米`
    }
};


export default {
    getDistance
}