export function objFilter (object) {
    for (const key in object) {
        const item = object[key];
        if (item === undefined || item === null) {
            delete object[key]
        }
    }
    return object
}

// 根据给定的 key 从已知的 Obj 中过滤出有值的属性组成一个新对象
export function getKeysObj (data, keysArr) {
    let res = {};
    keysArr.forEach(key => {
        if (data[key] || data[key] === 0) {
            res[key] = data[key];
        }
    })
    return res;
}

export default {
    objFilter,
    getKeysObj
}