<template>
  <view class="popup-container">
    <view v-if="localVisible" class="overlay" @click="hide"></view>

    <view class="popup" :class="{ 'popup-show': localVisible }">
      <view class="popup-content">
        <slot></slot>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    value: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      localVisible: this.value
    }
  },
  watch: {
    value(val) {
      this.localVisible = val
      if (val === false) {
        setTimeout(() => {
          uni.showTabBar({
            animation: true
          })
        }, 50)
      }
    }
  },
  methods: {
    // 隐藏弹窗
    hide() {
      this.localVisible = false
      this.$emit('input', false)
    }
  }
}
</script>

<style scoped>
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
}

.popup {
  position: fixed;
  bottom: -100%;
  left: 0;
  right: 0;
  background-color: #fff;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  transition: bottom 0.5s ease-in-out;
  z-index: 10000;
}

.popup-show {
  bottom: 0;
}

.popup-content {
  padding: 20px;
}
</style>
