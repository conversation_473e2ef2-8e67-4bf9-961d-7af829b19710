<template>
    <view class="has_bottom_btn_root">
        <view class="crop_selector_box">
            <view class="crop_item" :class="[item.id == cropId ? 'selected' : 'unselected' ]" v-for="(item, index) in cropArr" :key="index" @click="onCropTap(item)">
                <view>
                    <zui-svg-icon :icon="item.icon" width="210rpx" :color="item.id == cropId ? null : item.lightColors" />
                </view>
                <view class="crop_label">{{ item.name }}</view>
            </view>
        </view>
        <view class="crop_child_box" v-if="childMap[cropId]">
            <view class="title">请选择{{ idMap[cropId].name }}类型</view>
            <view class="type_box cf">
                <view
                    :class="['type_item', item.id === childId ? 'selected checked_mark' : 'unselected']"
                    v-for="(item, index) in childMap[cropId]"
                    :key="index"
                    @click="onChildTap(item)"
                >{{ item.name }}</view>
            </view>
        </view>
        <view class="fix_bottom_btn" @click="save">下一步</view>
    </view>
</template>

<script>
import messageBox from '../../utils/messageBox'
import router from '../../router'
const app = getApp();
export default {
    data () {
        return {
            idMap: {},
            childMap: {},
            cropArr: [],
            cropId: 1,
            childId: null
        }
    },
    onLoad () {
        const idMap = {};
        const childMap = {};
        const cropArr = [];
        app.globalData.cropArr.forEach(item => {
            idMap[item.id] = item;
            if (item.parentId) {
                if (childMap[item.parentId]) {
                    childMap[item.parentId].push(item);
                } else {
                    childMap[item.parentId] = [item]
                }
            } else {
                cropArr.push(item)
            }
        });
        this.idMap = idMap
        this.childMap = childMap
        this.cropArr = cropArr
    },
    methods: {
        onCropTap (item) {
            this.cropId = item.id
        },
        onChildTap (item) {
            this.childId = item.id
        },
        save () {
            if (this.cropId) {
                const childArr = this.childMap[this.cropId];
                if (childArr) {
                    if (this.childId && childArr.some(item => item.id === this.childId)) {
                        this.doSave(true);
                    } else {
                        messageBox.alert(`请选择${this.idMap[this.cropId].name}的分类`)
                    }
                } else {
                    this.doSave();
                }
            } else {
                messageBox.alert('请选择下单的作物')
            }
        },
        doSave (withChild) {
            let { cropId, childId } = this;
            app.globalData.newOrder = app.globalData.newOrder || {};
            Object.assign(app.globalData.newOrder, {
                cropId,
                cropChildId: withChild ? childId : null
            })
            router.startAddBaseInfo();
        }
    }
}
</script>

<style lang="scss">
.crop_selector_box {
    text-align: center;
    padding-top: 6px;
}
.crop_label {
    font-size: 16px;
    line-height: 24px;
    color: #ccc;
}
.crop_item {
    width: 210rpx;
    margin: 0 2.6% 10px;
    display: inline-block;
    &.selected {
        animation: big ease-out 0.2s;
        .crop_label {
            color: #333;
        }
    }
}

@keyframes big {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

.crop_child_box {
    margin: 10px 20px 0;
    .title {
        font-size: 18px;
        font-weight: 600;
        line-height: 1.1;
    }
}

.type_item {
    width: 46%;
    font-size: 16px;
    line-height: 44px;
    color: #aaa;
    background-color: #f1f1f1;
    text-align: center;
    float: left;
    margin-top: 15px;
    &:nth-child(2n) {
        float: right;
    }
    &.selected {
        background-color: rgba($color: #ff6a01, $alpha: 0.12);
        color: #ff6a01;
    }
}
</style>
