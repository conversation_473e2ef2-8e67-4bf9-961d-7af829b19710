<template>
    <view class="has_bottom_btn_root">
        <order-info :info="order"></order-info>
        <view class="agreement_box" v-if="!isEdit">
            <view class="rule_box">
                <switch class="check_box" type="checkbox" :checked="agree" @change="agree = !agree" />
                <text>已阅读并同意</text>
                <text class="link" @click="showSecretAgreement">《隐私政策》</text>
                <text>和</text>
                <text class="link" @click="showUserAgreement">《用户条款》</text>
                <text>拆开囤后发霉，同意重新协商价格等条款，详见</text>
                <text class="link" @click="showFaq">《常见问题》</text>
            </view>
        </view>
        <view class="agree_wrapper" :class="{ show: showAgreeToast }">
            <view class="agree_inner rule_box">
                <view class="title">隐私条款确认</view>
                <view class="content">
                    <text>创建订单前请您阅读并同意千万仓</text>
                    <text class="link" @click="showSecretAgreement">《隐私政策》</text>
                    <text>和</text>
                    <text class="link" @click="showUserAgreement">《用户条款》</text>
                    <text>拆开囤后发霉，同意重新协商价格等条款，详见</text>
                    <text class="link" @click="showFaq">《常见问题》</text>
                </view>
                <view class="btn_box">
                    <div class="btn left" @click="showAgreeToast = false">不同意</div>
                    <div class="btn right" @click="onToastAgree">同意并继续</div>
                </view>
            </view>
        </view>
        <view v-if="isEdit" class="fix_bottom_btn" @click="updateOrder">确认更新</view>
        <view v-else class="fix_bottom_btn" @click="createOrder">立即下单</view>
    </view>
</template>
<script>
import OrderInfo from '../../components/OrderInfo.vue';
import orderManager from '../../api/orderManager'
import logManager from '../../api/logManager'
import messageBox from '../../utils/messageBox'
import router from '../../router'
import { orderCreateKeys } from '../../constant/common'
import { FINISH_ORDER_INPUT } from '../../constant/event'
import { getKeysObj } from '../../utils/filter'
const app = getApp()
export default {
    components: {
        OrderInfo
    },
    data () {
        return {
            isEdit: false,
            order: {},
            agree: false,
            subscribe: 0,
            showAgreeToast: false
        }
    },
    onLoad: function (options) {
        const isEdit = options.isEdit ? true : false;
        this.isEdit = isEdit;
        if (!isEdit) {
            logManager.addLog(FINISH_ORDER_INPUT);
        }
        const appOrder = isEdit ? app.globalData.editOrder : app.globalData.newOrder;
        const order = Object.assign({}, appOrder);
        delete order.updateTime;
        this.order = order;
    },
    methods: {
        updateOrder () {
            uni.showLoading({ title: '订单更新中' })
            orderManager.updateOrder(this.order.id, this.order).then(res => {
                messageBox.toast.success('订单更新成功', 1000);
                this.backToOrderIndex();
            }).catch(err => {
                console.log(err);
                messageBox.toast.text('订单更新失败', 1000);
            }).then(() => {
                uni.hideLoading();
            });
        },
        createOrder () {
            if (this.agree) {
                const newOrder = getKeysObj(this.order, orderCreateKeys);
                uni.showLoading({ title: '订单创建中' })
                orderManager.createOrder(newOrder).then(data => {
                    messageBox.toast.success('订单创建成功', 1000);
                    this.backToOrderIndex();
                }).catch(err => {
                    console.log(err);
                    messageBox.toast.text('订单创建失败', 1000);
                }).then(() => {
                    uni.hideLoading();
                });
            } else {
                this.showAgreeToast = true;
            }
        },
        onToastAgree () {
            this.agree = true;
            this.showAgreeToast = false;
            this.createOrder();
        },
        // 返回订单首页
        backToOrderIndex () {
            app.globalData.needRefreshList = true;
            uni.switchTab({
                url: '/pages/sale/index?refresh=1'
            })
        },
        showSecretAgreement () {
            router.showH5('千万仓用户隐私条款', '/miniprogram/secret_agreement.html')
        },
        showUserAgreement () {
            router.showH5('千万仓用户条款', '/miniprogram/user_agreement.html')
        },
        showFaq () {
            router.showH5('常见问题', '/miniprogram/question.html')
        }
    }
}
</script>

<style lang="scss">
.agreement_box {
    font-size: 14px;
    text-align: left;
    margin: 12px 15px 0;
}
.rule_box {
    .check_box {
        transform: scale3d(0.75, 0.75, 1);
        margin-left: 5px;
    }
    .link {
        color: #ff6a01;
    }
}
.agree_wrapper {
    width: 100%;
    height: 100%;
    position: fixed;
    left: 0;
    top: 0;
    background: rgba($color: #000000, $alpha: 0.7);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 100;
    &.show {
        display: flex;
    }
    .agree_inner {
        background: #fff;
        width: 88%;
        position: relative;
    }
    .title {
        font-size: 18px;
        line-height: 50px;
        border-bottom: 1px solid #f1f1f1;
        padding-left: 20px;
    }
    .content {
        font-size: 15px;
        line-height: 2;
        padding: 15px 20px;
    }
    .btn_box {
        position: relative;
        background: #fff;
        .btn {
            font-size: 16px;
            line-height: 48px;
            text-align: center;
            width: 50%;
        }
        .left {
            border-top: 1px solid #f1f1f1;
        }
        .right {
            border-top: 1px solid #ff6a01;
            background: #ff6a01;
            color: #fff;
            position: absolute;
            right: 0;
            top: 0;
        }
    }
}
</style>