class VisitTracker {
  constructor(moduleName) {
    this.moduleName = moduleName
  }

  /**
   * 设置模块的最后访问时间
   */
  setLastVisitTime() {
    uni.setStorageSync(`${this.moduleName}_last_visit_time`, new Date().toISOString())
  }

  /**
   * 检查是否在指定天数内首次访问
   * @param {number} days - 天数
   * @returns {boolean} 是否在指定天数内首次访问
   */
  isFirstVisitInDays(days) {
    const lastVisitTimeStr = uni.getStorageSync(`${this.moduleName}_last_visit_time`)
    let lastVisitTime = new Date(lastVisitTimeStr)
    if (!lastVisitTime || isNaN(lastVisitTime.getTime())) {
      this.setLastVisitTime()
      return true
    }

    const daysAgo = new Date()
    daysAgo.setDate(daysAgo.getDate() - days)

    if (lastVisitTime < daysAgo) {
      this.setLastVisitTime()
      return true
    } else {
      return false
    }
  }

  /**
   * 静态方法
   * @param {string} moduleName - 模块名称
   * @param {number} days - 天数
   * @returns {boolean} 是否在指定天数内首次访问
   */
  static isFirstVisitInDays(moduleName, days) {
    const tracker = new VisitTracker(moduleName)
    return tracker.isFirstVisitInDays(days)
  }
}
export default VisitTracker
