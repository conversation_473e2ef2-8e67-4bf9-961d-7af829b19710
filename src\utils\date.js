export function addFormatDatePrototype () {
    Date.prototype.format = function (fmt) {
        return formatDate(this, fmt);
    }
}

export function formatDate (date, fmt) {
    var o = {
        'M+': date.getMonth() + 1, // 月份
        'd+': date.getDate(), // 日
        'h+': date.getHours(), // 小时
        'm+': date.getMinutes(), // 分
        's+': date.getSeconds(), // 秒
        'q+': Math.floor((date.getMonth() + 3) / 3), // 季度
        'S': date.getMilliseconds() // 毫秒
    };
    if (/(y+)/.test(fmt))
        fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
    for (var k in o)
        if (new RegExp('(' + k + ')').test(fmt))
            fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (('00' + o[k]).substr(('' + o[k]).length)));
    return fmt;
}

//将文字描述时长转换成毫秒时长
//s20 -> 20秒
//m12 -> 12分钟
//h12 -> 12小时
//d30 -> 30天
export function stringToTime (str) {
    const number = parseInt(str.substr(1));
    const map = {
        s: 1000,
        m: 1000 * 60,
        h: 1000 * 60 * 60,
        d: 1000 * 60 * 60 * 24
    }
    return number * map[str[0]];
}

// 将时间戳转换成的日期的文案的表示：今天、昨天、前天、具体日期
export function timeToDateString (timestamp) {
    const todayZeroTime = new Date().setHours(0, 0, 0, 0);
    const dayLong = 24 * 3600 * 1000;
    const date = new Date(timestamp);
    if (timestamp >= todayZeroTime) {
        return `今天 ${formatDate(date, 'hh:mm')}`
    } else if (timestamp >= todayZeroTime - dayLong) {
        return `昨天 ${formatDate(date, 'hh:mm')}`
    } else if (timestamp >= todayZeroTime - dayLong * 2) {
        return `前天 ${formatDate(date, 'hh:mm')}`
    } else {
        return formatDate(date, 'yyyy-MM-dd')
    }
}