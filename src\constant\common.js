export const requestHost = 'https://qianwancang.com'
// export const requestHost = 'http://localhost:3000'

export const chinaNumbers = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十']

// 订单状态分类
// COLLECTOR_WAIT
// BIDDING
// BID_FAIL
// BID_SUCCESS
// TRADING
// TRADE_FAIL
// TRADE_SUCCESS
// CLOSE
// FAKE
// CANCEL

export const orderStatesMap = {
  COLLECTOR_WAIT: { label: '等待信息收集员上门', color: '#409eff' },
  BIDDING: { label: '等待商贩回复中', color: '#409eff' },
  BID_FAIL: { label: '拒绝过多', color: '#e6a23c' },
  BID_SUCCESS: { label: '询价成功', color: '#5cb87a' },
  TRADING: { label: '小贩在路上', color: '#5cb87a' },
  TRADE_FAIL: { label: '交易失败', color: '#606266' },
  TRADE_SUCCESS: { label: '交易成功', color: '#5cb87a' },
  CLOSE: { label: '订单已结束', color: '#5cb87a' },
  FAKE: { label: '虚假订单', color: '#f56c6c' },
  CANCEL: { label: '订单已取消', color: '#c0c4cc' }
}

// 竞价拒绝理由
export const bidRejectsMap = {
  priceHeight: '要价高',
  humidityHeight: '湿度太大',
  tooFar: '距离太远',
  cropTypeReject: '品种不收',
  qualityLow: '品质太差',
  countLow: '量少不收'
}

// 竞价进程
export const bidProgressesMap = {
  NO_START: '小贩询价成功',
  PENDING: '小贩已接单',
  CALLED: '小贩已跟您联系',
  ON_WAY: '小贩正在前往',
  SUBMIT_TRADE: '小贩已提交交易信息'
}

export const cropArr = [
  {
    id: 1,
    parentId: 0,
    name: '玉米',
    icon: 'yumi',
    lightColors: ['#f3f3f3', '#999', '#fff', '#999']
  },
  { id: 2, parentId: 0, name: '小麦', icon: 'xiaomai', lightColors: ['#f3f3f3', '#999'] },
  {
    id: 3,
    parentId: 0,
    name: '花生',
    icon: 'huasheng',
    lightColors: ['#f3f3f3', '#aaa', '#999', '#787878', '#fff']
  },
  {
    id: 4,
    parentId: 0,
    name: '棉花',
    icon: 'mianhua',
    lightColors: ['#f3f3f3', '#bbb', '#e3e3e3']
  },
  {
    id: 5,
    parentId: 0,
    name: '辣椒',
    icon: 'lajiao',
    lightColors: ['#f3f3f3', '#aaa', '#999', '#787878']
  },
  {
    id: 6,
    parentId: 0,
    name: '其他',
    icon: 'xigua',
    lightColors: ['#f3f3f3', '#bbb', '#999', '#bbb', '#e3e3e3', '#bbb', '#bbb', '#bbb']
  },
  { id: 101, parentId: 1, name: '囤玉米' },
  { id: 102, parentId: 1, name: '地趴粮' },
  { id: 103, parentId: 1, name: '田里玉米' },
  { id: 104, parentId: 1, name: '玉米粒' },
  { id: 105, parentId: 5, name: '辣椒-已摘' },
  { id: 106, parentId: 5, name: '辣椒棵-未摘' }
]

export const orderUpdateKeys = [
  'mobile',
  'countOnCreate',
  'priceOnCreate',
  'count',
  'price',
  'latitude',
  'longitude',
  'cityCode',
  'address',
  'status',
  'infoStatus',
  'fixedPrice',
  'collectorId',
  'collectorTime',
  'pickType',
  'pickProcess',
  'variety',
  'humidity',
  'forklift',
  'semitrailer',
  'overallVideos',
  'videos',
  'photos',
  'remark'
]
export const orderCreateKeys = ['cropId', 'cropChildId'].concat(orderUpdateKeys)
