function alert (content, title, confirmText) {
    return showModal(content, title, false, confirmText);
}

function confirm (content, title, confirmText, cancelText) {//不需要关注取消按钮被点击的事件
    return new Promise(resolve => {
        showModal(content, title, true, confirmText, cancelText).then(() => {
            resolve();
        }).catch(() => {
            console.log('用户取消操作')
        })
    })
}

function confirmCareCancel (content, title, confirmText, cancelText) {//需要关注取消按钮被点击的事件
    return showModal(content, title, true, confirmText, cancelText);
}

function showModal (content, title, showCancel, confirmText, cancelText) {
    return new Promise((resolve, reject) => {
        content = content || 'null';
        title = title || '提示';
        confirmText = confirmText || '确定';
        cancelText = cancelText || '取消';
        wx.showModal({
            content,
            title,
            showCancel,
            confirmText,
            cancelText,
            success (res) {
                if (res.confirm) {
                    resolve();
                } else {
                    reject();
                }
            }
        })
    })
}

function toast (title, icon, duration) {
    duration = duration || 1500;
    return new Promise(resolve => {
        wx.showToast({
            title,
            icon,
            duration,
            mask: true
        })
        const timer = setTimeout(function () {
            resolve()
            clearTimeout(timer);
        }, duration)
    })
}

['success', 'loading', 'error', 'text'].forEach(item => {
    toast[item] = (title, duration) => {
        if (item === 'text') {
            return toast(title, 'none', duration);
        } else {
            return toast(title, item, duration);
        }
    }
})

toast.hide = function () {
    wx.hideToast();
}

export default {
    alert,
    confirm,
    confirmCareCancel,
    toast
}