const checkAuthMap = {
    userInfo: 'scope.userInfo',
    camera: 'scope.camera',
    location: 'scope.userLocation'
}

function checkAuth (type) {//检查是否授权
    return new Promise((resolve, reject) => {
        wx.getSetting({
            success: res => {
                if (res.authSetting[checkAuthMap[type]]) { //已经授权
                    resolve()
                } else {
                    reject('未授权')
                }
            }
        })
    })
}

function userAuth (type) {//弹窗获取授权
    return new Promise((resolve, reject) => {
        wx.authorize({
            scope: checkAuthMap[type],
            success () {
                resolve()
            },
            fail () {
                reject('授权失败')
            }
        })
    })
}

function checkAndAuth (type) {
    return new Promise((resolve, reject) => {
        checkAuth(type).then(() => {
            resolve();
        }).catch(() => {
            userAuth(type).then(() => {
                resolve();
            }).catch(err => {
                reject(err);
            })
        })
    })
}

function getCameraAuth () {//获取图片权限
    return checkAndAuth('camera')
}

function getLocation () {
    return new Promise((resolve, reject) => {
        checkAndAuth('location').then(() => {
            wx.getLocation({
                type: 'gcj02',
                isHighAccuracy: true,
                success: res => {
                    resolve(res)
                },
                fail: (err) => {
                    console.log(err)
                    reject('地理位置获取失败')
                }
            })
        }).catch(err => {
            reject(err)
        })
    })
}

function getUserInfo () {//获取用户信息不能使用checkAndAuth,用户授权获取只能通过按钮点击获取
    return new Promise((resolve, reject) => {
        checkAuth('userInfo').then(() => {
            wx.getUserInfo({
                withCredentials: true,
                success (res) {
                    resolve(res)
                },
                fail () {
                    reject('用户信息获取失败');
                }
            })
        }).catch(err => {
            reject(err);
        })
    })
}

export default {
    checkAuth,
    userAuth,
    checkAndAuth,
    getLocation,
    getUserInfo,
    getCameraAuth
};