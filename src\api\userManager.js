import http from '../utils/http'
import storeManager from '../utils/storeManager'
import pointsManager from './pointsManager'
import { AUTO_VIEW_GROUP_QRCODE } from '../constant/event'
const STORE_KEY = 'user_manager_v2_'

let wxUser = storeManager.get(STORE_KEY + 'wxUser')
let user = storeManager.get(STORE_KEY + 'user')

function checkLogin() {
  return user && storeManager.get('session_cookie')
}

function getWxUser() {
  return wxUser
}

function getUser() {
  return user
}

function setWxUser(data) {
  wxUser = data
  storeManager.set(STORE_KEY + 'wxUser', data, 'd10')
}

function setUser(data) {
  user = data
  storeManager.set(STORE_KEY + 'user', data, 'd100')
}

//上传头像
function uploadPhoto(path) {
  return http.upload('/common/upload/photo', path)
}

function buildQueryString(params) {
  return Object.keys(params)
    .map(key => encodeURIComponent(key) + '=' + encodeURIComponent(params[key]))
    .join('&')
}

async function getSharePath(path, params) {
  const paramsCopy = { ...params }
  paramsCopy.shareUid = user.id
  try {
    if (paramsCopy.isAssistance) {
      delete paramsCopy.isAssistance
      const shareId = await pointsManager.generateShareId()
      paramsCopy.shareId = shareId
    }
    const queryString = buildQueryString(paramsCopy)
    return `${path || '/pages/index/index'}?${queryString}`
  } catch (error) {
    console.error('[ getSharePath error ]', error)
    return `${path || '/pages/index/index'}?shareUid=${user.id}`
  }
}

function refreshUser() {
  return http.get('/user/getCurrentUser').then(data => {
    setUser(data)
    return data
  })
}

function queryMyShareCount(startTime, endTime) {
  return http.post('/user/getMyShareCount', { startTime, endTime })
}

function queryMyRoles(startTime, endTime) {
  return http.get('/user/getMyRole')
}

function queryGroupQrCodeLatestLog() {
  return http.get('/user/getMyLatestLog', { key: AUTO_VIEW_GROUP_QRCODE })
}

// 获取微信信息
function getWxInfo(code, encryptedData, iv) {
  return new Promise(function (resolve, reject) {
    http
      .post('/user/mini/getWxUser', { code, encryptedData, iv })
      .then(data => {
        setWxUser(data)
        resolve(data)
      })
      .catch(err => {
        reject(err)
      })
  })
}

// 小程序客户端通过code直接完成登录过程
// 无需关注不同小程序平台的处理，由服务端根据接口的 platform 参数进行 处理
function miniLoginByCode(code, inviteUserId) {
  return new Promise((resolve, reject) => {
    http
      .post('/user/mini/loginByCode', { code, inviteUserId })
      .then(data => {
        setUser(data)
        resolve(data)
      })
      .catch(err => {
        reject(err)
      })
  })
}

// 微信登录
function wxLogin(wxMiniOpenId, wxUnionId, inviteUserId) {
  return new Promise((resolve, reject) => {
    http
      .post('/user/login', { wxMiniOpenId, wxUnionId, inviteUserId })
      .then(data => {
        setUser(data)
        resolve(data)
      })
      .catch(err => {
        reject(err)
      })
  })
}

// 微信登录
function countMyLog(key) {
  return new Promise((resolve, reject) => {
    http
      .get(`/user/countLog/${key}`)
      .then(data => {
        resolve(data)
      })
      .catch(err => {
        reject(err)
      })
  })
}

// 获取手机号码
function getWxPhone(code) {
  return new Promise(function (resolve, reject) {
    http
      .post('/user/mini/getWxPhone', { code })
      .then(data => {
        resolve(data)
      })
      .catch(err => {
        reject(err)
      })
  })
}

function updateMyProfile(info) {
  return new Promise(function (resolve, reject) {
    http
      .put('/user/updateMyInfo', info)
      .then(data => {
        setUser(Object.assign(user, info))
        resolve(data)
      })
      .catch(err => {
        reject(err)
      })
  })
}

function updateMobile(mobile) {
  return new Promise(function (resolve, reject) {
    http
      .put('/user/updateMyInfo', { mobile })
      .then(data => {
        setUser(Object.assign(user, { mobile }))
        resolve(data)
      })
      .catch(err => {
        reject(err)
      })
  })
}

function updateAddressInfo(latitude, longitude, cityCode, address) {
  return new Promise(function (resolve, reject) {
    http
      .put('/user/updateMyInfo', { latitude, longitude, cityCode, address })
      .then(data => {
        setUser(Object.assign(user, { latitude, longitude, cityCode, address }))
        resolve(data)
      })
      .catch(err => {
        reject(err)
      })
  })
}

function querySubscribeState() {
  return new Promise(function (resolve, reject) {
    http
      .get('/api/nz/users/mini/actions/subscribe/orderChange')
      .then(data => {
        resolve(data.subscribe)
      })
      .catch(err => {
        reject(err)
      })
  })
}

function subscribeMessage() {
  return new Promise(function (resolve, reject) {
    http
      .post('/api/nz/users/mini/actions/subscribe/orderChange')
      .then(data => {
        resolve(data)
      })
      .catch(err => {
        reject(err)
      })
  })
}

export default {
  checkLogin,
  getUser,
  uploadPhoto,
  getSharePath,
  refreshUser,
  miniLoginByCode,
  getWxUser,
  getWxInfo,
  countMyLog,
  wxLogin,
  getWxPhone,
  queryMyShareCount,
  queryMyRoles,
  updateMyProfile,
  updateMobile,
  updateAddressInfo,
  querySubscribeState,
  subscribeMessage,
  queryGroupQrCodeLatestLog
}
