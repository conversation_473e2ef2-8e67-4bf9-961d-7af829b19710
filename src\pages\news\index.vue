<template>
  <div class="news-root" :style="{ height: `calc(100vh - ${paddingTopHeight}px - 30px)` }">
    <radio-group class="radio-group" @change="radioChange">
      <label
        class="uni-list-cell uni-list-cell-pd"
        v-for="(key, value) in sourceLabels"
        :key="value"
      >
        <radio
          :class="{ actived: value == current }"
          :value="value"
          :checked="value === current"
        />{{ key }}
      </label>
    </radio-group>
    <scroll-view
      class="scroll-view"
      scroll-y
      refresher-enabled
      scroll-with-animation
      @scrolltolower="loadMore"
      @refresherrefresh="onRefresh"
      :refresher-triggered="isRefreshing"
    >
      <no-data v-if="total === 0" />
      <template v-else>
        <div v-for="(item, index) in newsList" :key="index">
          <NewsItem :item="item" />
        </div>
      </template>
      <view class="loading" v-if="loadingData"></view>
      <view class="loaded_finish" v-else-if="total > 0 && total <= (pageIndex + 1) * pageSize">
        共{{ total }}条资讯信息，已加载完毕
      </view>
    </scroll-view>
  </div>
</template>

<script>
import infoManager from '../../api/infoManager'
import NewsItem from './components/item.vue'

export default {
  name: 'News',
  components: { NewsItem },
  props: {
    cropId: {
      type: Number,
      default: 1
    },
    paddingTopHeight: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      sourceLabels: {
        0: '全部',
        1: '供需形势',
        2: '粮食交易结果',
        3: '大豆产业协会',
        4: '棉花协会',
        5: '山东粮食局',
        6: '河北粮食局',
        7: '吉林粮食局',
        8: '新疆粮食局',
        9: '内蒙古粮食局'
      },
      newsList: [],
      pageIndex: 0,
      pageSize: 10,
      total: -1,
      isRefreshing: false,
      loadingData: false,
      current: '0'
    }
  },
  watch: {
    cropId(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.fetchNews(true)
      }
    }
  },
  computed: {},
  onLoad() {},
  async mounted() {
    try {
      this._freshing = true
      uni.showLoading({ title: '加载中...' })
      await this.fetchNews(true)
    } catch (error) {
      throw error
    } finally {
      uni.hideLoading()
      this._freshing = false
    }
  },

  methods: {
    async fetchNews(refresh) {
      try {
        if (this.loadingData) return
        this.loadingData = true
        if (refresh) {
          this.newsList = []
          this.pageIndex = 0
        } else {
          this.pageIndex++
        }
        const params = {
          pageIndex: this.pageIndex,
          pageSize: this.pageSize,
          cropId: this.cropId
        }
        this.current == 0 ? '' : (params.source = this.current)
        const response = await infoManager.getInfos(params)
        this.newsList.push(...response.list)
        this.total = response.total
      } catch (error) {
        uni.showToast({
          title: '获取资讯列表失败',
          icon: 'none'
        })
        throw error
      } finally {
        this.loadingData = false
        this.isRefreshing = false
      }
    },
    loadMore() {
      const { loadingData, total, pageIndex, pageSize } = this
      if (!loadingData) {
        if (total > (pageIndex + 1) * pageSize) {
          this.fetchNews()
        }
      }
    },
    onRefresh() {
      if (this._freshing) return
      this._freshing = true
      this.isRefreshing = true
      this.fetchNews(true).finally(() => {
        this.triggered = false
        this._freshing = false
      })
    },
    radioChange(evt) {
      this.current = evt.detail.value
      this.onRefresh()
    }
  }
}
</script>

<style lang="scss" scoped>
.news-root {
  // height: 100vh;
  // overflow: hidden;
}

.radio-group {
  display: flex;
  align-items: center;
  padding: 10px 10px 5px;
  margin: 0 -5px;
  overflow-x: auto;
  word-break: keep-all;
  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari 和 Opera */
  }
  -ms-overflow-style: none; /* IE 和 Edge */
  scrollbar-width: none; /* Firefox */
  label {
    border: 1px solid #ccc;
    border-radius: 30px;
    padding: 0 12px;
    font-size: 12px;
    margin: 0 5px;
    height: 30px;
    line-height: 30px;
    &[aria-checked='true'] {
      border-color: #ff6a01;
      color: #fff;
      font-weight: 700;
      background-color: #ff6a01;
    }
    radio {
      display: none;
    }
  }
}

.scroll-view {
  box-sizing: border-box;
  height: calc(100% - 44px);
}

.loading {
  margin: 20px 0;
  height: 40px;
}

.loaded_finish {
  font-size: 14px;
  color: #8a96a3;
  text-align: center;
  margin-top: 15px;
}
</style>
