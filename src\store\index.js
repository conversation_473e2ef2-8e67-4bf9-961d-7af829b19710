import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex)

export default new Vuex.Store({
  state: {
    isLogin: false, //是否登陆
    userInfo: {},
    sysConfig: {}
  },
  mutations: {
    isLogin(state, bool) {
      state['isLogin'] = bool
    },
    userInfo(state, info) {
      state['userInfo'] = info
    },
    sysConfig(state, config) {
      state['sysConfig'] = config
    }
  },
  actions: {},
  modules: {}
})
