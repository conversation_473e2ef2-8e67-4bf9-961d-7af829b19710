<template>
  <view class="detail_page_root">
    <view class="banner_video">
      <video
        v-if="order && order.overallVideos"
        class="video"
        :src="order.overallVideos[0]"
        controls
      ></video>
    </view>
    <order-info :info="order"></order-info>
    <view v-if="viewType === 'SELF' && order.status === 3" class="fix_bottom_btn_box">
      <button class="fr right_btn delete_btn" @click="onDeleteClick">删除订单</button>
    </view>
    <view v-else-if="viewType === 'SELF' && order.status !== 1" class="fix_bottom_btn_box">
      <view v-if="order.status === 0" class="icon_btn up_icon_btn" @click="onUpClick">
        <view class="icon">
          <zui-svg-icon icon="up" width="24px" />
        </view>
        <view class="btn_msg">订单置顶</view>
      </view>
      <button class="fr right_btn edit_btn" @click="onEditClick">编辑</button>
      <button class="fr right_btn finish_btn" @click="onFinishClick">成交</button>
      <button v-if="order.status === 0" class="fr right_btn stop_btn" @click="onStopClick">
        暂停出售
      </button>
      <button v-else-if="order.status === 2" class="fr right_btn active_btn" @click="onActiveClick">
        重新出售
      </button>
    </view>
    <view v-else-if="viewType === 'OTHER'" class="fix_bottom_btn_box">
      <view class="icon_btn" @click="onLikeClick">
        <view class="icon">
          <zui-svg-icon :icon="liked ? 'like' : 'unlike'" width="24px" />
        </view>
        <view class="btn_msg">{{ liked ? '已收藏' : '收藏' }}</view>
      </view>
      <view class="icon_btn" @click="onPhoneClick">
        <view class="icon">
          <zui-svg-icon icon="phone" width="24px" color="#ff6a01" />
        </view>
        <view class="btn_msg" :style="{ color: '#ff6a01' }">打电话</view>
      </view>
      <!-- <button class="fr right_btn" :disabled="asked" @click="onCollectClick">{{ asked ? '已请求测量' : '请求测量' }}</button> -->
    </view>
  </view>
</template>

<script>
import orderManager from '../../api/orderManager'
import userManager from '../../api/userManager'
import logManager from '../../api/logManager'
import messageBox from '../../utils/messageBox'
import router from '../../router'
import { VIEW_ORDER_DETAIL } from '../../constant/event'
import { ORDER_RELATION_TYPE, ORDER_STATUS } from '../../constant/enums'
import OrderInfo from '../../components/OrderInfo.vue'
const app = getApp()

export default {
  components: {
    OrderInfo
  },
  data() {
    return {
      id: null,
      order: {},
      liked: false,
      asked: false,
      orderPreStatus: null,
      viewType: null // SELF | OTHER | ADMIN | COLLECTOR
    }
  },
  onLoad(options) {
    this.eventChannel = this.getOpenerEventChannel()
    const { id, collectorOpen } = options
    const { viewOrder } = app.globalData
    if (viewOrder && viewOrder.id == id) {
      this.initData(viewOrder, collectorOpen)
    } else {
      orderManager.queryOrderById(id).then(res => {
        this.initData(res, collectorOpen)
      })
    }
  },
  computed: {
    cropConfig() {
      if (this.order && this.order.cropId) {
        return app.globalData.cropsMap[this.order.cropChildId || this.order.cropId]
      } else {
        return {}
      }
    }
  },
  async onShareAppMessage() {
    const path = await userManager.getSharePath(`/pages/sale/detail`, { id: this.id })
    const { name, countUnitOnCreate } = this.cropConfig
    const title =
      `出售：${name} - ${this.order.count}${countUnitOnCreate}` || '千万仓-玉米、小麦价格'
    return { title, path }
  },
  methods: {
    initData(viewOrder, collectorOpen) {
      const myId = userManager.getUser().id
      let viewType
      this.orderPreStatus = viewOrder.status
      if (viewOrder.userId === myId) {
        viewType = 'SELF'
      } else if (collectorOpen) {
        // 信息收集员打开了该页面
        viewType = 'COLLECTOR'
      } else {
        viewType = 'OTHER'
        logManager.addLog(VIEW_ORDER_DETAIL)
        orderManager.addOrderViewLog(viewOrder.id)
      }
      orderManager
        .queryOrderRelations(viewOrder.id)
        .then(res => {
          let liked = false
          let asked = false
          res.forEach(item => {
            if (item.relationType === ORDER_RELATION_TYPE.like) {
              liked = true
            } else if (item.relationType === ORDER_RELATION_TYPE.ask) {
              asked = true
            }
          })
          this.id = viewOrder.id
          this.order = viewOrder
          this.viewType = viewType
          this.liked = liked
          this.asked = asked
        })
        .catch(err => {
          messageBox.toast.error('订单信息获取失败')
        })
    },
    onLikeClick() {
      uni.showLoading({
        mask: true
      })
      if (this.liked) {
        orderManager
          .cancelLikeOrder(this.id)
          .then(() => {
            this.liked = false
            uni.hideLoading()
            messageBox.toast.success('已取消', 2000)
          })
          .catch(err => {
            uni.hideLoading()
          })
      } else {
        orderManager
          .addLikeOrder(this.id)
          .then(() => {
            this.liked = true
            uni.hideLoading()
            messageBox.toast.success('已收藏', 2000)
          })
          .catch(err => {
            uni.hideLoading()
          })
      }
    },
    onCollectClick() {
      messageBox
        .confirm('测量服务仅限冀州区周村镇，是否请求测量？', '测量确认', '确定', '取消')
        .then(() => {
          orderManager.addCollectAsk(this.id).then(res => {
            messageBox.toast.success('请求已发送', 2000)
            this.asked = true
          })
        })
    },
    onPhoneClick() {
      orderManager.addMobileClickLog(this.id)
      uni.makePhoneCall({
        phoneNumber: this.order.mobile
      })
    },
    onStopClick() {
      orderManager
        .stopOrder(this.id)
        .then(res => {
          this.order.status = ORDER_STATUS.stop
          messageBox.toast.success('订单已暂停', 2000)
          app.globalData.needRefreshList = true // 全局状态记录需要刷新列表
          // this.eventChannel.emit('getUserInfoFail', '已暂停');
        })
        .catch(err => {
          messageBox.toast.error('网络异常')
        })
    },
    onDeleteClick() {
      orderManager
        .deleteOrder(this.id)
        .then(res => {
          messageBox.toast.success('删除成功')
          app.globalData.needRefreshList = true
          uni.navigateBack()
        })
        .catch(err => {
          messageBox.toast.error('网络异常')
        })
    },
    onActiveClick() {
      orderManager
        .activeOrder(this.id)
        .then(res => {
          this.order.status = ORDER_STATUS.default
          messageBox.toast.success('操作成功', 2000)
          app.globalData.needRefreshList = true
        })
        .catch(err => {
          messageBox.toast.error('网络异常')
        })
    },
    onUpClick() {
      orderManager
        .refreshOrder(this.id)
        .then(res => {
          messageBox.toast.success('置顶成功', 2000)
          app.globalData.needRefreshList = true
        })
        .catch(err => {
          messageBox.toast.error('网络异常')
        })
    },
    onEditClick() {
      router.startEditOrder(this.order)
    },
    onFinishClick() {
      orderManager.setSubmitOrder(this.order)
      router.startSubmitTrade()
    },
    onUnload() {
      if (this.viewType === 'SELF' && this.order.status != this.orderPreStatus) {
        // 订单由暂停变成激活状态或者是订单由激活状态变成暂停状态
        this.eventChannel.emit('orderUpdate', this.order)
      }
    }
  }
}
</script>

<style lang="scss">
.detail_page_root {
  padding-bottom: 90px;
}
.banner_video .video {
  display: block;
  width: 100%;
  height: 422rpx;
}
.fix_bottom_btn_box {
  width: 100%;
  height: 40px;
  padding: 20px 0;
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 100;
  border-top: 1px solid #f1f1f1;
  background-color: #fff;
}
.icon_btn {
  display: inline-block;
  width: 16%;
  text-align: center;
  .icon {
    height: 24px;
  }
  .btn_msg {
    font-size: 10px;
    line-height: 12px;
    margin-top: 4px;
  }
  &.up_icon_btn {
    margin-left: 15px;
  }
}
.right_btn {
  font-size: 14px;
  line-height: 38px;
  height: 40px;
  box-sizing: border-box;
  border: 1px solid #ebebeb;
  border-radius: 4px;
  width: 180rpx !important;
  text-align: center;
  padding: 0;
  margin-right: 16px !important;
  font-weight: normal;
  &.edit_btn {
    width: 136rpx !important;
  }
  &.finish_btn {
    width: 136rpx !important;
  }
  &.stop_btn {
    background-color: #ff4f5c;
    color: #fff;
  }
  &.delete_btn {
    background-color: #ff4f5c;
    color: #fff;
  }
  &.active_btn {
    background-color: #0ac261;
    color: #fff;
  }
  &.btn_show_phone {
    background-size: 14px 14px;
    background-position: 14px 50%;
    padding-left: 24px;
    background-repeat: no-repeat;
  }
}
</style>
