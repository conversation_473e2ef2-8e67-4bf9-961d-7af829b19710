<template>
  <view class="user_center_page_root">
    <view class="header tap_right" @click="tapHeader">
      <div class="header-info">
        <image class="avatar" :src="photo" mode="aspectFill" />
        <view class="header-info-inner">
          <view class="name">{{ name || '--' }}</view>
          <view v-if="userType" class="user_type" :class="userType.className">{{
            userType.label
          }}</view>
        </view>
      </div>
      <div class="header-points">
        <zui-svg-icon icon="points" width="1.625rem" style="height: 1.625rem" />
        <view>
          <text>{{ userInfo.points }}</text
          >积分
        </view>
      </div>
    </view>
    <view class="gap"></view>
    <view class="list tap_right" hover-class="hover" @click="router.goToPointsPage()">
      <div class="list_inner">
        <img mode="aspectFit" class="icon" src="/static/images/my_icon9.png" alt="千万仓" />
        <text class="name">积分记录</text>
      </div>
    </view>
    <view
      v-if="isCollector || isSuperAdmin"
      class="list tap_right"
      hover-class="hover"
      @click="onCollectClick"
    >
      <div class="list_inner">
        <img mode="aspectFit" class="icon" src="/static/images/my_icon1.png" alt="千万仓" />
        <text class="name">待测量订单</text>
      </div>
    </view>
    <view
      v-if="isOperator || isSuperAdmin"
      class="list tap_right"
      hover-class="hover"
      @click="showShare"
    >
      <div class="list_inner">
        <img mode="aspectFit" class="icon" src="/static/images/my_icon8.png" alt="千万仓" />
        <text class="name">我的分享统计</text>
      </div>
    </view>
    <view class="list tap_right" hover-class="hover" @click="onMyOrderClick">
      <div class="list_inner">
        <img mode="aspectFit" class="icon" src="/static/images/my_icon2.png" alt="千万仓" />
        <text class="name">我的订单</text>
      </div>
    </view>
    <view class="list tap_right" hover-class="hover" @click="onMyLikeClick">
      <div class="list_inner">
        <img mode="aspectFit" class="icon" src="/static/images/my_icon7.png" alt="千万仓" />
        <text class="name">我的收藏</text>
      </div>
    </view>
    <view class="gap"></view>
    <view class="list tap_right" hover-class="hover" @click="showQuestion">
      <div class="list_inner">
        <img mode="aspectFit" class="icon" src="/static/images/my_icon3.png" alt="千万仓" />
        <text class="name">常见问题</text>
      </div>
    </view>
    <view class="list tap_right" hover-class="hover" @click="showContact">
      <div class="list_inner">
        <img mode="aspectFit" class="icon" src="/static/images/my_icon4.png" alt="千万仓" />
        <text class="name">联系我们</text>
      </div>
    </view>
    <!-- <view class="gap"></view>
        <view class="list tap_right" hover-class="hover" @click="buyerRegister">
            <div class="list_inner">
                <img mode="aspectFit" class="icon" src="/static/images/my_icon6.png" alt="千万仓" />
                <text class="name">我是收购商</text>
            </div>
        </view>-->
  </view>
</template>

<script>
import router from '../../router'
import userManager from '../../api/userManager'
import { ADMIN_TYPES, COMMON_USER_TYPES } from './constants/index'

export default {
  data() {
    return {
      userTypeMap: {
        ...ADMIN_TYPES,
        ...COMMON_USER_TYPES
      },
      classMap: {
        0: 'nong',
        1: 'fan',
        2: 'zhuang',
        3: 'fan',
        10: 'xin',
        100: 'guan'
      },
      router,
      type: null,
      name: '',
      userInfo: {},
      photo: '/static/images/user_default.png',
      // showCollectorMenu: false,
      isCollector: false, // 是否信息收集员
      isOperator: false, // 是否运营人员
      isSuperAdmin: false // 是否超级管理员
    }
  },
  computed: {
    userType() {
      return {
        label: this.userTypeMap[this.type] || '',
        className: this.classMap[this.type] || ''
      }
    }
  },
  onShow() {
    this.refreshData()
  },
  methods: {
    onCollectClick() {
      router.startCollectList()
    },
    refreshData() {
      uni.showLoading()
      userManager
        .refreshUser()
        .then(user => {
          this.type = user.type
          // this.showCollectorMenu = user.type >= 10;
          this.name = user.name || `千万仓用户_${user.id}`
          this.userInfo = { ...user, name: this.name }
          if (user.avatar) {
            this.photo = user.avatar
          }
          uni.hideLoading()
        })
        .catch(err => {
          console.log(err)
          uni.hideLoading()
        })
      userManager.queryMyRoles().then(list => {
        if (list && list.length > 0) {
          list.forEach(item => {
            if (item.type === 'superAdmin') {
              this.isSuperAdmin = true
            } else if (item.type === 'operator') {
              this.isOperator = true
            } else if (item.type === 'collector') {
              this.isCollector = true
            }
          })
        }
      })
    },
    tapHeader() {
      router.startMyInfo({
        uploadUser: () => {
          this.refreshData()
        }
      })
    },
    showContact() {
      uni.navigateTo({
        url: '/pages/my/contactUs'
      })
    },
    showShare() {
      uni.navigateTo({
        url: '/pages/my/share'
      })
    },
    showQuestion() {
      router.showH5('常见问题', '/miniprogram/question.html')
    },
    onMyOrderClick() {
      router.startMyOrder()
    },
    onMyLikeClick() {
      router.startMyLikeOrder()
    },
    buyerRegister() {
      router.startBuyerRegister()
    }
  }
}
</script>

<style lang="scss">
.user_center_page_root {
  background: #f6f6f6;
  min-height: 100vh;
}
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #ff6a01;
  color: #fff;
  padding: 20px 16px;
  .header-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
    min-width: 0;
    overflow: hidden;
    .avatar {
      width: 60px;
      height: 60px;
      background-color: #eee;
      border-radius: 4px;
    }
    .header-info-inner {
      flex: 1;
      min-width: 0;
      display: flex;
      flex-direction: column;
      justify-content: center;
      overflow: hidden;
      .name {
        font-size: 16px;
        flex: 1;
        min-width: 0;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .user_type {
        font-size: 13px;
        line-height: 2;
        opacity: 0.6;
      }
    }
  }

  &.tap_right::after {
    border-color: #ffffff;
  }
  .header-points {
    display: inline-flex;
    flex-shrink: 0;
    align-items: center;
    margin-right: 25px;
    margin-left: 12px;
    font-size: 12px;
    text {
      font-size: 22px;
      margin: 0 3px;
    }
  }
}
.border {
  height: 12px;
  background: #f4f4f4;
}
.list {
  position: relative;
  background: #fff;
}
.list_inner {
  color: #333;
  font-size: 16px;
  line-height: 53px;
  margin-left: 16px;
  padding-left: 35px;
  border-bottom: 1rpx solid #f6f6f6;
  position: relative;
  .icon {
    width: 25px;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
  }
}
</style>
