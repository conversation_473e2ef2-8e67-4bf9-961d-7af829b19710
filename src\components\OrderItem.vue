<template>
    <div class="order_item" :class="{ no_border: noBorder }" @click="$emit('click')">
        <div class="icon"></div>
        <div class="name">{{ cropConfig.name }} ： {{order.count}}{{cropConfig.countUnitOnCreate}}</div>
        <div class="info_box">
            <div class="info">品类：{{ order.variety ? order.variety : cropConfig.name }}</div>
            <div v-if="_order.humidityValue" class="info">湿度：{{ _order.humidityValue }}</div>
            <div class="info address">交货地：{{ order.address }}</div>
        </div>
        <div class="contact">
            <span>{{order.phoneCount}} 名收购商已联系</span>
            <span class="fr">{{ _order.updateTime }}</span>
        </div>
        <div v-if="order.priceOnCreate" class="price">
            <span class="number">{{ order.priceOnCreate }}</span>
            <span>{{ cropConfig.priceUnitOnCreate }}</span>
        </div>
        <view v-if="!order.fixedPrice" class="price_type">可议价</view>
    </div>
</template>

<script>
export default {
    props: {
        order: {
            type: Object,
            default: () => ({})
        },
        showTradePrice: {
            type: Boolean,
            default: false
        },
        noBorder: {
            type: Boolean,
            default: false
        }
    },
    computed: {
        _order () {
            const app = getApp()
            const { cropChildId, cropId } = this.order;
            const { cropsMap } = app.globalData;
            const cropConfig = cropsMap[cropChildId || cropId];
            let humidityValue = null;
            if (this.order.humidity) {
                if (isNaN(this.order.humidity)) {
                    humidityValue = this.order.humidity
                } else {
                    humidityValue = `${this.order.humidity}个水`
                }
            }
            let updateTime;
            const todayZeroTime = new Date().setHours(0, 0, 0, 0);
            const dayLong = 24 * 3600 * 1000;
            if (this.order.updateTime >= todayZeroTime) {
                updateTime = `今天 ${new Date(this.order.updateTime).format('hh:mm')}`
            } else if (this.order.updateTime >= todayZeroTime - dayLong) {
                updateTime = `昨天 ${new Date(this.order.updateTime).format('hh:mm')}`
            } else if (this.order.updateTime >= todayZeroTime - dayLong * 2) {
                updateTime = `前天 ${new Date(this.order.updateTime).format('hh:mm')}`
            } else {
                updateTime = new Date(this.order.updateTime).format('yyyy-MM-dd')
            }
            return {
                cropConfig: cropConfig,
                humidityValue,
                updateTime
            }
        },
        cropConfig () {
            return this._order.cropConfig
        }
    }
}
</script>


<style lang="scss">
.order_item {
    padding: 0 16px;
    background: #fff;
    border-bottom: 10px solid #f6f6f6;
    font-size: 14px;
    position: relative;
    &.no_border {
        border-bottom: 0;
    }
}
.name {
    font-size: 16px;
    line-height: 2.4;
    font-weight: 600;
}
.info_box {
    padding: 8px 12px;
    background: #f6f6f6;
}
.info {
    font-size: 14px;
    line-height: 2;
    color: #787878;
    &.address {
        color: #333;
    }
}
.contact {
    font-size: 12px;
    line-height: 3;
    color: #999;
}
.price {
    position: absolute;
    top: 50px;
    right: 26px;
    font-size: 13px;
    color: #ff6a01;
    .number {
        font-size: 20px;
    }
}
.price_type {
    font-size: 12px;
    line-height: 1.8;
    padding: 0 0.6em;
    border: 3rpx solid #ff6a01;
    border-radius: 4px;
    color: #ff6a01;
    position: absolute;
    top: 12px;
    right: 20px;
    transform: rotate(20deg);
    opacity: 0.5;
}
</style>