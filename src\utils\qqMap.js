const QQMapWX = require('../libs/qqmap-wx-jssdk');
const mapSdk = new QQMapWX({ key: '7C6BZ-6SLKU-OPUVH-2HYAV-JCJSZ-TFB2M' });

function getAddress (longitude, latitude) {
    return new Promise(function (resolve, reject) {
        mapSdk.reverseGeocoder({
            location: { longitude, latitude },
            success: res => {
                let { location, address, ad_info, formatted_addresses } = res.result;
                let { lat, lng } = location;
                let { adcode } = ad_info;
                if (formatted_addresses && formatted_addresses.recommend) {
                    address += formatted_addresses.recommend;
                }
                resolve({
                    latitude: lat,
                    longitude: lng,
                    address,
                    cityCode: adcode * 1000000
                })
            },
            fail: err => {
                reject(err)
            }
        })
    })
}

export default {
    getAddress
}