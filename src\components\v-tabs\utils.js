export function startMicroTask(callback) {
  // 使用 Promise 作为微任务
  if (typeof Promise === 'function') {
    Promise.resolve().then(callback)
  }
  // 如果不支持 Promise，使用 MutationObserver
  else if (typeof MutationObserver === 'function') {
    const node = document.createElement('div')
    const observer = new MutationObserver(() => {
      observer.disconnect() // 观察完成后停止观察
      callback() // 执行回调
    })
    observer.observe(node, { childList: true })
    node.textContent = 'trigger' // 触发观察
  }
  // 最后作为回退使用 setTimeout
  else {
    setTimeout(callback, 0)
  }
}
