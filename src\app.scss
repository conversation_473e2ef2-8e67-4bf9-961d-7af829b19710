$primaryColor: #ff6a01;
page {
  line-height: 1.5;
  color: #333333;
}
.root {
  padding: 20px;
}
.has_bottom_btn_root {
  padding-bottom: 90px;
}
.gap {
  height: 12px;
}
.tap_right {
  position: relative;
}
.tap_right::after {
  content: '';
  width: 10px;
  height: 10px;
  border: 2px solid #aaa;
  border-width: 2px 2px 0 0;
  box-sizing: border-box;
  margin: -6px 22px 0 0;
  position: absolute;
  top: 50%;
  right: 0;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
}
.hover {
  background: #e5e5e5 !important;
}
.cf {
  clear: both;
  overflow: hidden;
}
.fl {
  float: left;
}
.fr {
  float: right;
}

.app_btn {
  padding: 0;
  box-sizing: border-box;
  background-color: $primaryColor;
  border: 1px solid $primaryColor;
  display: block;
  width: 100%;
  margin: 30px auto 0;
  border-radius: 4px;
  font-size: 16px;
  line-height: 48px;
  text-align: center;
  color: #ffffff;
  transition: background-color ease-out 0.25s;
}
/* 一行内有两个按钮 */
.app_btn.half {
  margin-top: 25px;
  line-height: 42px;
  width: 47%;
}
.app_btn.unable {
  opacity: 0.3;
}
.app_btn.empty {
  background-color: transparent;
  border-color: #333333;
  color: #333333;
}
.app_btn:active {
  background-color: $primaryColor;
  border-color: $primaryColor;
}
.app_btn.empty:active {
  background-color: transparent;
  border-color: #111111;
  color: #111111;
}
.app_btn.empty.cancel {
  border-color: #999999;
  color: #999999;
}
.app_btn.empty.cancel:active {
  border-color: #777777;
  color: #777777;
}
.app_btn.small {
  margin: 0;
  width: auto;
  display: inline-block;
  line-height: 32px;
  padding: 0 15px;
  vertical-align: middle;
  font-size: 14px;
  border-radius: 2px;
}
.app_btn.empty.small {
  line-height: 32px;
}

.line1_only {
  overflow: hidden;
  white-space: nowrap;
  /*文字超出宽度则显示ellipsis省略号*/
  text-overflow: ellipsis;
  width: 100%;
}

.loading {
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' fill='none' xmlns='http://www.w3.org/2000/svg'%3E %3Cpath d='M85.6375 76.425C85.9563 76.425 86.2501 76.1125 86.2501 75.775C86.2501 75.4375 85.9563 75.1312 85.6375 75.1312C85.3188 75.1312 85.025 75.4437 85.025 75.775C85.025 76.1125 85.3188 76.425 85.6375 76.425ZM71.9114 91.4C72.1364 91.6375 72.4552 91.775 72.7678 91.775C72.9295 91.7745 73.0894 91.741 73.2377 91.6765C73.3861 91.612 73.5197 91.5179 73.6303 91.4C73.8565 91.1524 73.9819 90.8291 73.9819 90.4938C73.9819 90.1584 73.8565 89.8351 73.6303 89.5875C73.5194 89.4698 73.3858 89.3759 73.2375 89.3115C73.0892 89.247 72.9294 89.2133 72.7678 89.2125C72.6071 89.2138 72.4483 89.2478 72.3011 89.3122C72.1539 89.3767 72.0214 89.4703 71.9114 89.5875C71.6839 89.8345 71.5576 90.158 71.5576 90.4938C71.5576 90.8295 71.6839 91.153 71.9114 91.4ZM53.9412 99.4375C54.2788 99.7937 54.7538 100 55.2288 100C55.4703 99.9977 55.7088 99.9467 55.93 99.8501C56.1513 99.7534 56.3507 99.6131 56.5164 99.4375C56.86 99.0657 57.0507 98.5781 57.0507 98.0719C57.0507 97.5657 56.86 97.0781 56.5164 96.7062C56.3513 96.5295 56.152 96.3881 55.9308 96.2903C55.7095 96.1926 55.4707 96.1406 55.2288 96.1375C54.9874 96.1398 54.7489 96.1908 54.5277 96.2874C54.3064 96.3841 54.107 96.5244 53.9412 96.7C53.5959 97.0722 53.4041 97.5611 53.4041 98.0687C53.4041 98.5764 53.5959 99.0653 53.9412 99.4375V99.4375ZM34.5709 98.875C35.021 99.3437 35.6523 99.625 36.2898 99.625C36.6111 99.6216 36.9283 99.5535 37.2226 99.4246C37.5169 99.2957 37.7821 99.1088 38.0025 98.875C38.4597 98.3797 38.7137 97.7303 38.7137 97.0563C38.7137 96.3822 38.4597 95.7328 38.0025 95.2375C37.7821 95.0037 37.5169 94.8168 37.2226 94.6879C36.9283 94.559 36.6111 94.4909 36.2898 94.4875C35.9675 94.49 35.6491 94.5578 35.3537 94.6867C35.0582 94.8156 34.792 95.003 34.5709 95.2375C34.1168 95.7331 33.8648 96.3809 33.8648 97.0531C33.8648 97.7253 34.1168 98.3731 34.5709 98.8688V98.875ZM17.4133 89.9063C17.9759 90.5 18.7634 90.8437 19.5572 90.8437C19.9603 90.8406 20.3587 90.7559 20.7282 90.5948C21.0977 90.4337 21.4308 90.1995 21.7074 89.9063C22.2789 89.2864 22.5962 88.4743 22.5962 87.6312C22.5962 86.7882 22.2789 85.9761 21.7074 85.3562C21.4308 85.063 21.0977 84.8288 20.7282 84.6677C20.3587 84.5066 19.9603 84.4219 19.5572 84.4187C19.1552 84.4227 18.7581 84.5078 18.3897 84.6689C18.0213 84.83 17.6892 85.0638 17.4133 85.3562C16.8418 85.9761 16.5245 86.7882 16.5245 87.6312C16.5245 88.4743 16.8418 89.2864 17.4133 89.9063V89.9063ZM5.58111 74.3438C6.25616 75.0625 7.20624 75.4812 8.15631 75.4812C8.64069 75.4767 9.11913 75.3741 9.56279 75.1797C10.0064 74.9853 10.4061 74.703 10.7378 74.35C11.4235 73.6057 11.8041 72.6307 11.8041 71.6187C11.8041 70.6068 11.4235 69.6318 10.7378 68.8875C10.4061 68.5345 10.0064 68.2522 9.56279 68.0578C9.11913 67.8634 8.64069 67.7608 8.15631 67.7562C7.6735 67.7612 7.19664 67.8633 6.75419 68.0566C6.31173 68.2499 5.91277 68.5304 5.58111 68.8812C4.89364 69.6259 4.51189 70.6022 4.51189 71.6156C4.51189 72.6291 4.89364 73.6053 5.58111 74.35V74.3438ZM1.24326 55.1437C1.63025 55.5536 2.09601 55.8811 2.61263 56.1066C3.12924 56.3321 3.68607 56.4511 4.24975 56.4562C4.81343 56.4511 5.37026 56.3321 5.88687 56.1066C6.40348 55.8811 6.86924 55.5536 7.25624 55.1437C8.0557 54.276 8.4995 53.1392 8.4995 51.9594C8.4995 50.7795 8.0557 49.6428 7.25624 48.775C6.86997 48.3659 6.40522 48.0389 5.88974 47.8133C5.37426 47.5878 4.81862 47.4685 4.256 47.4625C3.69127 47.4668 3.13324 47.5854 2.6155 47.8109C2.09775 48.0365 1.63098 48.3644 1.24326 48.775C0.4438 49.6428 0 50.7795 0 51.9594C0 53.1392 0.4438 54.276 1.24326 55.1437V55.1437ZM5.13107 36.0625C5.5734 36.5313 6.10595 36.9059 6.69673 37.1636C7.2875 37.4214 7.9243 37.5571 8.56885 37.5625C9.21339 37.5571 9.85019 37.4214 10.441 37.1636C11.0317 36.9059 11.5643 36.5313 12.0066 36.0625C12.9216 35.0709 13.4297 33.7711 13.4297 32.4219C13.4297 31.0726 12.9216 29.7729 12.0066 28.7813C11.5648 28.3113 11.0325 27.9356 10.4417 27.6767C9.85091 27.4179 9.21384 27.2812 8.56885 27.275C7.92385 27.2812 7.28678 27.4179 6.69598 27.6767C6.10518 27.9356 5.57286 28.3113 5.13107 28.7813C4.21606 29.7729 3.708 31.0726 3.708 32.4219C3.708 33.7711 4.21606 35.0709 5.13107 36.0625V36.0625ZM16.4382 20.75C17.4508 21.825 18.8759 22.45 20.3135 22.45C21.7387 22.45 23.1638 21.825 24.1763 20.75C25.2038 19.6342 25.7741 18.173 25.7741 16.6562C25.7741 15.1395 25.2038 13.6783 24.1763 12.5625C23.6795 12.0327 23.0806 11.609 22.4157 11.3169C21.7507 11.0247 21.0335 10.8701 20.3073 10.8625C18.8759 10.8625 17.4508 11.4875 16.4382 12.5625C15.4108 13.6783 14.8405 15.1395 14.8405 16.6562C14.8405 18.173 15.4108 19.6342 16.4382 20.75ZM32.9458 12.1875C34.0709 13.375 35.6523 14.0687 37.2399 14.0687C38.8275 14.0687 40.4089 13.3813 41.534 12.1875C42.6783 10.9486 43.3138 9.32399 43.3138 7.6375C43.3138 5.95101 42.6783 4.32644 41.534 3.0875C40.9818 2.50095 40.3168 2.03197 39.5789 1.7087C38.841 1.38543 38.0455 1.21453 37.2399 1.20625C36.4343 1.21453 35.6387 1.38543 34.9009 1.7087C34.163 2.03197 33.498 2.50095 32.9458 3.0875C31.8028 4.32711 31.1683 5.95143 31.1683 7.6375C31.1683 9.32357 31.8028 10.9479 32.9458 12.1875V12.1875ZM51.466 12.0812C52.7036 13.3937 54.4475 14.1563 56.1977 14.1563C57.9478 14.1563 59.6854 13.3937 60.923 12.0812C62.1798 10.7182 62.8775 8.93209 62.8775 7.07813C62.8775 5.22416 62.1798 3.43809 60.923 2.075C60.3151 1.42788 59.5825 0.910442 58.7694 0.553841C57.9562 0.19724 57.0793 0.00884617 56.1914 0C54.4413 0 52.6974 0.7625 51.4598 2.075C50.203 3.43809 49.5053 5.22416 49.5053 7.07813C49.5053 8.93209 50.203 10.7182 51.4598 12.0812H51.466ZM68.4424 20.5375C69.7925 21.9625 71.6927 22.7938 73.5928 22.7938C75.5055 22.7938 77.4056 21.9625 78.7495 20.5375C80.12 19.0509 80.8809 17.1031 80.8809 15.0813C80.8809 13.0594 80.12 11.1116 78.7495 9.625C78.0869 8.91966 77.2886 8.35562 76.4024 7.9668C75.5162 7.57799 74.5605 7.37243 73.5928 7.3625C71.6864 7.3625 69.7925 8.19375 68.4424 9.625C67.0719 11.1116 66.311 13.0594 66.311 15.0813C66.311 17.1031 67.0719 19.0509 68.4424 20.5375V20.5375ZM80.5559 36.0188C82.0185 37.5688 84.0749 38.4688 86.1438 38.4688C88.2065 38.4688 90.2692 37.5688 91.7255 36.0188C93.2102 34.4077 94.0345 32.297 94.0345 30.1062C94.0345 27.9155 93.2102 25.8048 91.7255 24.1938C91.0082 23.4305 90.144 22.82 89.1848 22.399C88.2256 21.978 87.1913 21.7551 86.1438 21.7438C84.0749 21.7438 82.0185 22.6438 80.5559 24.1938C79.0712 25.8048 78.2469 27.9155 78.2469 30.1062C78.2469 32.297 79.0712 34.4077 80.5559 36.0188V36.0188ZM85.4813 55.9438C87.0564 57.6125 89.2691 58.5813 91.5005 58.5813C93.7257 58.5813 95.9384 57.6125 97.5135 55.9438C99.1124 54.2082 100 51.9347 100 49.575C100 47.2153 99.1124 44.9418 97.5135 43.2063C96.7411 42.3832 95.8103 41.7247 94.777 41.2704C93.7437 40.8161 92.6292 40.5753 91.5005 40.5625C89.2691 40.5625 87.0626 41.5375 85.4813 43.2063C83.881 44.9412 82.9925 47.2148 82.9925 49.575C82.9925 51.9352 83.881 54.2088 85.4813 55.9438V55.9438Z' fill='%238A96A3'/%3E %3C/svg%3E ");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  animation: loading 1.6s ease infinite;
}

.close_icon {
  width: 30px;
  height: 30px;
  background-image: url("data:image/svg+xml,%3Csvg width='12' height='12' viewBox='0 0 12 12' fill='none' xmlns='http://www.w3.org/2000/svg'%3E %3Cpath d='M12 1.21286L10.7871 0L6 4.78714L1.21286 0L0 1.21286L4.78714 6L0 10.7871L1.21286 12L6 7.21286L10.7871 12L12 10.7871L7.21286 6L12 1.21286Z' fill='%23333333'/%3E %3C/svg%3E ");
  background-color: #ffffff;
  background-size: 40% 40%;
  background-position: center;
  background-repeat: no-repeat;
  border-radius: 50%;
}

.checked_mark {
  background-size: 24px 24px;
  background-position: 100% 100%;
  background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' fill='none' xmlns='http://www.w3.org/2000/svg'%3E %3Cpath d='M20 20H0L20 0V20Z' fill='%23FF6A01'/%3E %3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M8.125 13.946L12.1025 17.9235L12.9864 17.0396L12.9864 17.0396L19.1736 10.8524L18.2897 9.96853L12.1025 16.1557L9.00888 13.0622L8.125 13.946Z' fill='white'/%3E %3C/svg%3E ");
  background-repeat: no-repeat;
}

// 必传字段
.required::before {
  content: '*';
  color: $primaryColor;
  display: inline-block;
  padding-right: 3px;
}

.fix_bottom_btn {
  width: 80%;
  height: 50px;
  line-height: 50px;
  font-size: 18px;
  text-align: center;
  color: #ffffff;
  border-radius: 25px;
  background-color: $primaryColor;
  position: fixed;
  left: 10%;
  bottom: 20px;
  z-index: 10000;
}

.user_type {
  padding-left: 1.5em;
  background-size: auto 1.4em;
  background-repeat: no-repeat;
  background-position: 0% 50%;
  &.nong {
    background-image: url("data:image/svg+xml,%3Csvg width='87' height='100' viewBox='0 0 87 100' fill='none' xmlns='http://www.w3.org/2000/svg'%3E %3Cpath d='M7 28L43 7.5L80 28V72.5L43 94.5L7 70.5V28Z' fill='%23FF6A01'/%3E %3Cpath d='M86.6029 75.0003L43.3013 100L4.13694e-05 75.0003L0 25.0001L43.3014 0L86.6029 25V75.0003ZM43.3013 87.3872L75.6796 68.6934V31.3066L43.3015 12.6132L10.9234 31.3067L10.9234 68.6934L43.3013 87.3872Z' fill='%23FEBE91'/%3E %3Cpath d='M30.05 73.05C30.05 71.75 34.45 69.55 34.45 69.55V50.6H30.75V66.3C30.75 68.3 29.3 69.25 28.35 69.65C29 70.5 29.8 72.15 30.05 73.05ZM22.7 33.35V43.65H26.4V36.9H59.65V43.65H63.5V33.35H22.7ZM43.25 26.95C40 41.85 32.9 50.9 19.55 55.8C20.45 56.55 21.8 58.1 22.35 58.9C35.7 53.25 43.3 43.7 47 27.7L43.25 26.95ZM43.65 37.8L40.25 38.7C44.05 54.3 51.15 66.2 63.4 72.05C64.05 71 65.25 69.55 66.15 68.85C54.25 63.75 47.05 52.2 43.65 37.8ZM58.8 45.6C56 48.45 51.2 52.2 47.8 54.4L50.35 56.75C53.8 54.5 58.45 51.1 61.75 48.05L58.8 45.6ZM30.05 73.05C31.25 72.3 33.05 71.65 46.55 67.5C46.4 66.7 46.25 65.1 46.2 64.05L31.65 68.25L29.75 69.7L30.05 73.05Z' fill='white'/%3E %3C/svg%3E ");
  }
  &.zhuang {
    background-image: url("data:image/svg+xml,%3Csvg width='87' height='100' viewBox='0 0 87 100' fill='none' xmlns='http://www.w3.org/2000/svg'%3E %3Cpath d='M7 28L43 7.5L80 28V72.5L43 94.5L7 70.5V28Z' fill='%23FF6A01'/%3E %3Cpath d='M86.6029 75.0003L43.3013 100L4.13694e-05 75.0003L0 25.0001L43.3014 0L86.6029 25V75.0003ZM43.3013 87.3872L75.6796 68.6934V31.3066L43.3015 12.6132L10.9234 31.3067L10.9234 68.6934L43.3013 87.3872Z' fill='%23FEBE91'/%3E %3Cpath d='M31.8 46.1V49.65H63.1V46.1H31.8ZM28.45 64.85V68.45H65.7V64.85H28.45ZM45 35.6V66.6H48.8V35.6H45ZM42.5 24.05V32.55H46.35V24.05H42.5ZM25.85 30.5V34.1H65.6V30.5H25.85ZM24 30.5V43.45C24 50.65 23.65 60.75 19.55 67.95C20.45 68.35 22.1 69.4 22.75 70.05C27.05 62.45 27.75 51.15 27.75 43.45V30.5H24Z' fill='white'/%3E %3C/svg%3E ");
  }
  &.fan {
    background-image: url("data:image/svg+xml,%3Csvg width='87' height='100' viewBox='0 0 87 100' fill='none' xmlns='http://www.w3.org/2000/svg'%3E %3Cpath d='M7 28L43 7.5L80 28V72.5L43 94.5L7 70.5V28Z' fill='%23FF6A01'/%3E %3Cpath d='M86.6029 75.0003L43.3013 100L4.13694e-05 75.0003L0 25.0001L43.3014 0L86.6029 25V75.0003ZM43.3013 87.3872L75.6796 68.6934V31.3066L43.3015 12.6132L10.9234 31.3067L10.9234 68.6934L43.3013 87.3872Z' fill='%23FEBE91'/%3E %3Cpath d='M44.25 41.6V45H62.15V41.6H44.25ZM61.15 41.6V42.3C59.15 56.55 52.4 66.15 43.75 70.1C44.55 70.8 45.5 72.2 45.95 73.1C55.4 68.2 62.35 58.4 64.6 42.1L62.4 41.5L61.8 41.6H61.15ZM49.6 43.8L46.65 44.45C49.2 57.55 54.35 67.75 63.7 72.7C64.25 71.7 65.4 70.4 66.2 69.7C57.15 65.55 51.95 55.75 49.6 43.8ZM63 27.45C57.95 28.75 49.4 29.85 42.05 30.45C42.45 31.3 42.95 32.65 43.05 33.5C50.5 33.05 59.3 32 65.35 30.55L63 27.45ZM42.05 30.45V49.5C42.05 56.6 41.5 65.2 37.15 71.15C38.05 71.6 39.4 72.6 40.05 73.25C44.95 66.85 45.65 57.7 45.65 49.5V30.45H42.05ZM28.6 36.65V51.05C28.6 57.25 28 65.85 19.95 70.5C20.7 71.1 21.65 72.2 22.05 72.9C30.7 67.45 31.7 58.25 31.7 51V36.65H28.6ZM30.55 61.85C32.25 64.25 34.3 67.6 35.3 69.55L37.9 67.85C36.95 65.95 34.8 62.75 33.05 60.4L30.55 61.85ZM22.15 29.9V59.65H25.2V33.3H35.1V59.5H38.2V29.9H22.15Z' fill='white'/%3E %3C/svg%3E ");
  }
  &.guan {
    background-image: url("data:image/svg+xml,%3Csvg width='87' height='100' viewBox='0 0 87 100' fill='none' xmlns='http://www.w3.org/2000/svg'%3E %3Cpath d='M7 28L43 7.5L80 28V72.5L43 94.5L7 70.5V28Z' fill='%23FF6A01'/%3E %3Cpath d='M86.6029 75.0003L43.3013 100L4.13694e-05 75.0003L0 25.0001L43.3014 0L86.6029 25V75.0003ZM43.3013 87.3872L75.6796 68.6934V31.3066L43.3015 12.6132L10.9234 31.3067L10.9234 68.6934L43.3013 87.3872Z' fill='%23FEBE91'/%3E %3Cpath d='M31.5 68.45V71.35H57.95V68.45H31.5ZM41.05 37.15V42.7H44.7V37.15H41.05ZM22.05 41.05V50.45H25.65V44.05H60.6V50.45H64.3V41.05H22.05ZM31.3 47.15V50H53.25V54.3H31.3V57.2H56.95V47.15H31.3ZM31.45 60.65V63.6H56.45V72.95H60.15V60.65H31.45ZM29.3 47.15V73.05H32.9V47.15H29.3ZM26.15 30.85V33.95H42.3V30.85H26.15ZM45.65 30.85V33.95H65.85V30.85H45.65ZM27.1 26.8C25.55 30.7 22.85 34.65 19.85 37.25C20.75 37.7 22.25 38.75 22.95 39.3C25.85 36.45 28.8 32.1 30.65 27.7L27.1 26.8ZM46.8 26.8C45.2 30.65 42.3 34.35 39 36.75C39.95 37.2 41.5 38.1 42.25 38.65C45.4 36.05 48.55 32 50.35 27.65L46.8 26.8ZM28.95 33.2C30.1 35.05 31.3 37.55 31.75 39.1L35.15 38.1C34.65 36.55 33.35 34.1 32.2 32.35L28.95 33.2ZM50.15 33.25C51.75 35.05 53.45 37.6 54.15 39.2L57.55 38.1C56.8 36.45 55 34 53.45 32.25L50.15 33.25Z' fill='white'/%3E %3C/svg%3E ");
  }
  &.xin {
    background-image: url("data:image/svg+xml,%3Csvg width='87' height='100' viewBox='0 0 87 100' fill='none' xmlns='http://www.w3.org/2000/svg'%3E %3Cpath d='M7 28L43 7.5L80 28V72.5L43 94.5L7 70.5V28Z' fill='%23FF6A01'/%3E %3Cpath d='M86.6029 75.0003L43.3013 100L4.13694e-05 75.0003L0 25.0001L43.3014 0L86.6029 25V75.0003ZM43.3013 87.3872L75.6796 68.6934V31.3066L43.3015 12.6132L10.9234 31.3067L10.9234 68.6934L43.3013 87.3872Z' fill='%23FEBE91'/%3E %3Cpath d='M38.2 29.4V32.45H61.35V29.4H38.2ZM37.6 43.25V46.35H62.25V43.25H37.6ZM37.6 50.2V53.3H62.15V50.2H37.6ZM39.05 67.55V70.65H60.75V67.55H39.05ZM33.5 36.3V39.45H66.05V36.3H33.5ZM37.1 57.15V73H40.7V60.2H58.95V72.85H62.7V57.15H37.1ZM31.8 27.15C28.9 34.75 24 42.2 18.95 46.95C19.65 47.85 20.7 49.8 21.05 50.65C26.6 45.1 31.95 36.7 35.35 28.25L31.8 27.15ZM26.6 40.05V72.9H30.2V36.55L30.15 36.5L26.6 40.05Z' fill='white'/%3E %3C/svg%3E ");
  }
}

.update_bottom_btn {
  font-size: 18px;
  line-height: 50px;
  text-align: center;
  height: 50px;
  width: 100%;
  background-color: #fff;
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 10;
}

.update_bottom_btn .btn {
  width: 50%;
  box-sizing: border-box;
}

.update_bottom_btn .btn.fl {
  border-top: 1px solid #f1f1f1;
}

.update_bottom_btn .btn.fr {
  background-color: #ff6a01;
  color: #fff;
}

.shake {
  animation: shake_move;
  animation-timing-function: ease-in;
  animation-duration: 0.5s;
  animation-delay: 0.5s;
}

.watermark {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  background-image: url('https://qianwancang.com/images/watermark.png');
  background-position: 0 10px;
  background-size: 100% auto;
  background-repeat: repeat;
  opacity: 0.5;
  pointer-events: none;
}

@keyframes shake_move {
  0% {
    transform: translate3d(0, 0, 0);
  }
  15% {
    transform: translate3d(-10px, 0, 0);
  }
  35% {
    transform: translate3d(10px, 0, 0);
  }
  50% {
    transform: translate3d(0, 0, 0);
  }
  65% {
    transform: translate3d(-10px, 0, 0);
  }
  85% {
    transform: translate3d(10px, 0, 0);
  }
  100% {
    transform: translate3d(0, 0, 0);
  }
}

@keyframes loading {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
