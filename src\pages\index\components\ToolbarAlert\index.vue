<template>
  <div v-if="visible" class="page_alert" :style="{ top: `${deviceInfo.totalBarHeight}px` }">
    <div class="alert_content">
      <div>
        点击
        <img src="/static/images/weapp_more_action.svg" />
        “添加到桌面”
      </div>
      <view class="close_icon clear" @tap.native.stop="onClosePageAlert"></view>
    </div>
    <div
      class="triangle-up"
      :style="{
        left: `${deviceInfo.menuBounding.left + deviceInfo.menuBounding.width / 2 / 2 - 16}px`
      }"
    ></div>
  </div>
</template>

<script>
import configManager from '../../../../api/configManager'

export default {
  name: 'ToolbarAlert',
  data() {
    return {
      visible: false,
      deviceInfo: {}
    }
  },
  created() {
    configManager.getDeviceInfo().then(info => {
      this.deviceInfo = info
      const storedVisible = uni.getStorageSync('toolbarAlertVisible')
      this.visible = typeof storedVisible === 'boolean' ? storedVisible : true
      setTimeout(() => {
        this.visible = false
      }, 10000)
    })
  },
  mounted() {},
  methods: {
    onClosePageAlert() {
      const _this = this
      uni.showModal({
        title: '提示',
        content: '不再展示“添加到桌面”提醒？',
        confirmText: '是',
        cancelText: '否',
        success: function (res) {
          if (res.confirm) {
            _this.visible = false
            uni.setStorageSync('toolbarAlertVisible', false)
          } else if (res.cancel) {
            _this.visible = false
            console.log('用户点击取消')
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.page_alert {
  position: fixed;
  top: 0;
  left: 16px;
  right: 16px;
  z-index: 1000001;
  display: flex;
  justify-content: flex-end;
  margin-top: 6px;
}
.alert_content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 13px;
  color: #fff;
  padding: 8px 10px;
  border-radius: 6px;
  background-color: rgba(0, 0, 0, 0.8);
  > div {
    display: inline-flex;
    align-items: center;
  }
  img {
    width: 36px;
    height: 20px;
    margin: 0 5px;
    border: 0.5px solid #666;
    border-radius: 20px;
    background-color: #0000001f;
  }
  .clear {
    width: 16px;
    height: 16px;
    margin-left: 13px;
  }
}
.triangle-up {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-bottom: 6px solid rgba(0, 0, 0, 0.8); /* 气泡颜色 */
}
</style>
