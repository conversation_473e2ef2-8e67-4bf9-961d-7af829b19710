<template>
  <view class="local-prices">
    <div class="local-prices-header">
      <view class="local-prices-add">
        <input type="text" v-model="localPrice" placeholder="请输入价格" style="width: 100px" />
        <span></span>
        <input type="text" v-model="localWeight" placeholder="请输入重量" />
        <div class="local-prices-unit">万斤</div>
        <button class="local-prices-submit" :loading="loading" @click="onSubmit">发布价格</button>
      </view>
      <view class="input-alert"
        ><icon type="info" size="20" />请输入成交价格、总金额、数量、品质等
      </view>
    </div>

    <view class="price-list" :style="{ height: `calc(100vh - ${paddingTopHeight}px - 136px)` }">
      <div class="watermark"></div>
      <no-data v-if="total === 0" />
      <template v-else>
        <scroll-view
          :scroll-top="scrollTop"
          scroll-y
          refresher-enabled
          scroll-with-animation
          :refresher-triggered="triggered"
          style="height: 100%"
          @refresherrefresh="onRefresh"
          @scrolltolower="onLower"
        >
          <view class="price-item" v-for="(item, index) in processedPriceData" :key="index">
            <ad-custom
              v-if="item.type === 'insert'"
              unit-id="adunit-66ba8a8749812fd3"
              style="margin: -10px -16px"
              @load="adLoad"
              @error="adError"
              @close="adClose"
            ></ad-custom>
            <template v-else>
              <view class="item-header">
                <text class="desc">{{ item.description }}</text>
                <view v-if="item.userId === userId" class="del" @click="handleDelPrice(item.id)">
                  <!-- <zui-svg-icon icon="delete" width="36rpx" color="#999" />&nbsp;  -->
                  删除
                </view>
              </view>
              <view class="item-footer">
                <text class="date">{{ timeToDateString(item.createTime) }}</text>
                <text class="location">{{ item.address }}</text>
              </view>
            </template>
          </view>
          <view v-if="priceData.length === total" class="price-item">
            <ad-custom
              unit-id="adunit-66ba8a8749812fd3"
              style="margin: -10px -16px"
              @load="adLoad"
              @error="adError"
              @close="adClose"
            ></ad-custom>
          </view>
          <view class="loading" v-if="loadingData"></view>
          <view class="loaded_finish" v-else-if="total > 0 && total <= (pageIndex + 1) * pageSize">
            共{{ total }}条价格信息，已加载完毕
          </view>
        </scroll-view>
      </template>
    </view>
    <Popup v-model="popupVisible">
      <div class="popup-content">
        <div class="popup-close-btn" @click="() => (popupVisible = false)">
          <icon type="cancel" size="32" color="#333" />
        </div>
        <view class="popup-content-header">您周边多少钱一斤？</view>
        <view class="popup-content-input">
          <input type="text" v-model="popupLocalPrice" placeholder="请输入价格" />
          <span></span>
          <input type="text" v-model="popupLocalWeight" placeholder="请输入重量" />
          <div class="local-prices-unit">万斤</div>
        </view>
        <view class="popup-content-alert"
          ><icon type="info" size="20" />请输入成交价格、总金额、数量、品质等
        </view>
        <button class="popup-content-submit" :loading="loading" @click="onSubmit('popup')">
          发布价格
        </button>
      </div>
    </Popup>
  </view>
</template>

<script>
import priceManager from '../../../../api/priceManager'
import userManager from '../../../../api/userManager'
import Popup from '../../../../components/popup/index.vue'
const { timeToDateString, formatDate } = require('../../../../utils/date')
import VisitTracker from '../../../../utils/visitTracker'
const visitTracker = new VisitTracker('localPricesPopup')

export default {
  name: 'LocalPrices',
  components: {
    Popup
  },
  props: {
    cropId: {
      type: Number,
      default: 1
    },
    paddingTopHeight: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      userId: null,
      popupVisible: false,
      timeToDateString,
      location: {},
      loading: false,
      localPrice: '',
      localWeight: '',
      popupLocalPrice: '',
      popupLocalWeight: '',
      description: '',
      popupDescription: '',
      priceData: [],
      loadingData: false,
      pageIndex: 0,
      pageSize: 10,
      total: -1,
      triggered: false
    }
  },
  watch: {
    cropId(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.initPricesList(true)
      }
    },
    popupVisible(val) {
      this.$emit('localPricesPopupVisible', val)
      if (val) {
        this.popupDescription = ''
      }
    }
  },
  computed: {
    processedPriceData() {
      return this.insertElementsBetweenDays(this.priceData)
    }
  },
  created() {
    this.userId = userManager.getUser().id ?? null
  },
  async mounted() {
    try {
      this._freshing = true
      uni.showLoading({ title: '加载中...' })
      const { latitude, longitude } = await uni.getFuzzyLocation()
      this.location = { latitude, longitude }
      this.popupVisible = visitTracker.isFirstVisitInDays(1)
      await this.initPricesList(true)
    } catch (error) {
      uni.showToast({
        title: '加载位置信息失败',
        icon: 'none'
      })
      throw error
    } finally {
      uni.hideLoading()
      this._freshing = false
    }
  },

  methods: {
    adLoad() {
      console.log('原生模板广告加载成功')
    },
    adError(err) {
      console.error('原生模板广告加载失败', err)
    },
    adClose() {
      console.log('原生模板广告关闭')
    },
    insertElementsBetweenDays(data, dateField = 'createTime') {
      let newData = []
      let currentDayCount = 0
      let currentDate = ''
      for (let i = 0; i < data.length; i++) {
        const itemDate = formatDate(new Date(data[i][dateField]), 'yyyy-MM-dd')
        if (itemDate !== currentDate) {
          currentDayCount = 0
          currentDate = itemDate
        }
        newData.push(data[i])
        currentDayCount++
        if (i === data.length - 1) {
          continue
        }
        if (formatDate(new Date(data[i + 1][dateField]), 'yyyy-MM-dd') !== itemDate) {
          if (currentDayCount >= 5) {
            newData.push({
              id: Date.now(),
              type: 'insert'
            })
          }
        }
      }

      return newData
    },
    initPricesList(refresh) {
      return new Promise((resolve, reject) => {
        if (this.loadingData) return
        this.loadingData = true
        if (refresh) {
          this.priceData = []
          this.pageIndex = 0
        } else {
          this.pageIndex++
        }
        priceManager
          .queryLocalPrices({
            ...this.location,
            cropId: this.cropId,
            pageIndex: this.pageIndex,
            pageSize: this.pageSize
          })
          .then(res => {
            this.priceData.push(...res.list)
            this.total = res.total
            resolve(res)
          })
          .catch(err => {
            uni.showToast({
              title: '获取价格列表失败',
              icon: 'none'
            })
            reject(err)
          })
          .finally(() => {
            this.loadingData = false
          })
      })
    },
    onRefresh() {
      if (this._freshing) return
      this._freshing = true
      this.triggered = true
      this.initPricesList(true).finally(() => {
        this.triggered = false
        this._freshing = false
      })
    },
    onLower() {
      const { loadingData, total, pageIndex, pageSize } = this
      if (!loadingData) {
        if (total > (pageIndex + 1) * pageSize) {
          this.initPricesList()
        }
      }
    },

    handleDelPrice(id) {
      uni.showModal({
        title: '提示',
        content: '确定删除该价格信息吗？',
        success: async res => {
          if (res.confirm) {
            await priceManager.delLocalPrices(id)
            uni.showToast({
              title: '价格信息已删除',
              icon: 'none'
            })
            for (let i = 0; i < this.priceData.length; i++) {
              if (this.priceData[i].id === id) {
                this.priceData.splice(i, 1)
                break
              }
            }
          }
        }
      })
    },

    validateInput(value, fieldName) {
      if (value === null || value === undefined || value === '') {
        uni.showToast({ title: `请输入${fieldName}`, icon: 'none' })
        return false
      }
      // if (isNaN(value)) {
      //   uni.showToast({ title: `${fieldName}必须是数字`, icon: 'none' })
      //   return false
      // }
      return true
    },
    async onSubmit(type) {
      let value
      if (type === 'popup') {
        if (!this.validateInput(this.popupLocalPrice, '价格')) return
        if (!this.validateInput(this.popupLocalWeight, '重量')) return
        value = this.popupLocalPrice + ' / ' + this.popupLocalWeight + '万斤'
      } else {
        if (!this.validateInput(this.localPrice, '价格')) return
        if (!this.validateInput(this.localWeight, '重量')) return
        value = this.localPrice + ' / ' + this.localWeight + '万斤'
      }

      try {
        this.loading = true
        const { latitude, longitude, address } = await uni.chooseLocation()
        const params = {
          cropId: this.cropId,
          description: value,
          address,
          latitude,
          longitude
        }
        await priceManager.postLocalPrices(params)

        uni.showToast({
          title: '发布成功',
          icon: 'success',
          success: () => {
            if (type === 'popup') {
              this.popupVisible = false
              this.popupDescription = ''
            } else {
              this.description = ''
            }
            this.initPricesList(true)
          }
        })
      } catch (error) {
        console.log('%c [ error ] ', 'font-size:13px; background:pink; color:#bf2c9f;', error)
        throw error
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.local-prices {
  /* padding: 20px; */
}
.local-prices-header {
  padding: 16px;
  max-height: 106px;
}
.local-prices-add,
.popup-content-input {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 46px;
  line-height: 46px;
  border: 1px solid #aaa;
  border-radius: 25px;
  span {
    width: 1px;
    height: 22px;
    background: #999;
  }
}

.local-prices-unit {
  font-size: 12px;
  color: #333;
  background: #d6d6d6;
  border-radius: 6px;
  padding: 0 6px;
  height: 26px;
  line-height: 26px;
  margin-right: 6px;
}

.popup-content-input .local-prices-unit {
  margin-right: 16px;
}

input {
  flex: 1;
  padding: 0 10px;
  box-sizing: border-box;
  height: 46px;
  font-size: 14px;
}
input::placeholder {
  color: #888;
}
.local-prices-submit,
.popup-content-submit {
  width: 90px;
  height: 46px;
  padding: 0;
  border: none;
  line-height: 46px;
  font-size: 16px;
  text-align: center;
  color: #ffffff;
  border-radius: 25px;
  background-color: #ff6a01;
}
.input-alert,
.popup-content-alert {
  display: flex;
  align-items: center;
  font-size: 13px;
  color: #333;
  margin-top: 6px;
  margin-left: 16px;
  opacity: 0.8;
  icon {
    margin-right: 3px;
  }
}
.price-list {
  position: relative;
  /* margin-top: 20px; */
}
.price-item {
  margin: 0 16px;
  border-bottom: 1px solid #f1f1f1;
  padding: 8px 0;
  font-size: 14px;
}
.item-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}
.item-footer {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #999;
  line-height: 20px;
}
.location {
  /* flex: 2; */
}
.desc {
  flex: 1;
}
.del {
  display: inline-flex;
  align-items: center;
  margin-left: 6px;
  padding: 0 2px;
  font-size: 13px;
}
.date {
  flex-shrink: 0;
  margin-right: 16px;
}

.loading {
  margin-top: 10px;
  height: 40px;
}

.loaded_finish {
  font-size: 14px;
  color: #8a96a3;
  text-align: center;
  margin-top: 15px;
}

.popup-content {
  background: #fff;
  border-radius: 10px;
  padding: 32px 16px;
  box-sizing: border-box;
  &-header {
    color: #333;
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 16px;
  }
  &-input {
    width: 100%;
  }
  &-alert {
    margin-left: 0;
  }
  &-submit {
    margin-top: 32px;
    width: 100%;
  }
}
.popup-close-btn {
  position: absolute;
  top: -38px;
  right: 0;
  z-index: 100;
}

.watermark {
  position: absolute;
}
</style>
