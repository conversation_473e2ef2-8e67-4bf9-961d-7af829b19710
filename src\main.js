import Vue from 'vue'
import App from './App'
import store from './store'
import { addFormatDatePrototype } from './utils/date'
import './uni.promisify.adaptor'
import TabMenu from './components/TabMenu.vue'
import NavigateBar from './components/NavigateBar.vue'
import NoData from './components/NoData.vue'
import CustomHook from 'spa-custom-hooks'

Vue.use(
  CustomHook,
  {
    Login: {
      name: 'Login',
      watchKey: 'isLogin',
      onUpdate(val) {
        return !!val
      }
    },
    UserInfo: {
      name: 'UserInfo',
      watchKey: 'userInfo',
      deep: true,
      onUpdate(val) {
        return !!val.id
      }
    },
    SysConfig: {
      name: 'SysConfig',
      watchKey: 'sysConfig',
      deep: true,
      onUpdate(val) {
        return !!val.updateTime
      }
    }
  },
  store
)

Vue.component('TabMenu', TabMenu)
Vue.component('NavigateBar', NavigateBar)
Vue.component('NoData', NoData)

addFormatDatePrototype()
Vue.config.productionTip = false
// 注册全局组件

App.mpType = 'app'

const app = new Vue({
  store,
  ...App
})
app.$mount()
