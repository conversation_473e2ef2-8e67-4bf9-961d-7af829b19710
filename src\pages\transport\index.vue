<template>
  <div class="news-root" :style="{ height: `calc(100vh - 30px)` }">
    <view class="chart-container">
      <l-echart ref="chartRef"></l-echart>
    </view>
    <scroll-view
      class="scroll-view"
      scroll-y
      refresher-enabled
      scroll-with-animation
      @scrolltolower="loadMore"
      @refresherrefresh="onRefresh"
      :refresher-triggered="isRefreshing"
    >
      <view class="table-header">
        <text class="col factory-name">厂家</text>
        <text class="col today-data">当日数据</text>
        <text class="col change">较前一日</text>
      </view>
      <no-data v-if="total === 0" />
      <template v-else>
        <view class="factory-item">
          <view class="col factory-name">合计</view>
          <view class="col today-data"
            >{{ statsData[type] ? statsData[type].totalToday : '--' }}车</view
          >
          <view
            class="col change"
            :style="{ color: changeColor(statsData[type] ? statsData[type].totalChange : 0) }"
          >
            {{ statsData[type] ? formattedChange(statsData[type].totalChange) : '--' }}
          </view>
        </view>
        <div v-for="item in transportList" :key="item.id">
          <FactoryItem :item="item" @click.native="handleItemClick(item)" />
        </div>
      </template>
      <view class="loading" v-if="loadingData"></view>
      <view class="loaded_finish" v-else-if="total > 0 && total <= (pageIndex + 1) * pageSize">
        共{{ total }}条工厂信息，已加载完毕
      </view>
    </scroll-view>
  </div>
</template>

<script>
import * as echarts from '@/uni_modules/lime-echart/static/echarts.min'
import transportManager from '../../api/transportManager'
import FactoryItem from './components/item.vue'

const TYPE_MAP = {
  0: '厂车',
  1: '锦州港'
}

export default {
  name: 'Transport',
  components: { FactoryItem },
  data() {
    return {
      transportList: [],
      pageIndex: 0,
      pageSize: 10,
      total: -1,
      isRefreshing: false,
      _freshing: false,
      loadingData: false,
      type: '0',
      statsData: {},
      historyData: null
    }
  },
  computed: {
    pageTitle() {
      return TYPE_MAP[this.type] || '运输数据详情'
    }
  },
  onLoad(options) {
    this.initParams(options)
    this.setNavigationBarTitle()
    this.loadStatsData()
  },
  async mounted() {
    try {
      this._freshing = true
      uni.showLoading({ title: '加载中...' })
      await Promise.all([this.loadHistoryData(), this.fetchTransport(true)])
      if (!this.chartInstance) {
        await this.onChartInit()
      }
    } catch (error) {
      throw error
    } finally {
      uni.hideLoading()
      this._freshing = false
    }
  },
  onUnload() {
    if (this.chartInstance) {
      this.chartInstance.off('click') // 移除事件监听
      this.chartInstance.clear() // 先清空再销毁
      setTimeout(() => {
        this.chartInstance.dispose()
      }, 300) // 延迟确保清理完成
    }
  },

  methods: {
    async onChartInit() {
      try {
        await this.$nextTick()
        this.chartInstance = await this.$refs.chartRef.init(echarts)
        this.renderChart()
      } catch (error) {
        console.error('图表初始化失败:', error)
      }
    },
    initParams(options) {
      this.type = options.type || '0'
    },
    setNavigationBarTitle() {
      uni.setNavigationBarTitle({ title: this.pageTitle })
    },

    renderChart() {
      if (!this.chartInstance || !this.historyData) return
      const option = {
        tooltip: {
          trigger: 'axis',
          textStyle: {
            textShadowBlur: 0
          },
          renderMode: 'richText'
        },
        legend: {
          data: ['当日数据', '较前一日变化'],
          itemWidth: 10,
          itemHeight: 10,
          itemGap: 10,
          icon: 'circle'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '16%',
          containLabel: true
        },
        digit: 3,
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: this.historyData.dates
        },
        yAxis: {
          type: 'value',
          name: '车',
          nameTextStyle: {
            align: 'right'
          },
          axisLabel: {
            color: '#666'
          }
        },
        series: [
          {
            name: '当日数据',
            type: 'line',
            smooth: true,
            symbol: 'circle',
            data: this.historyData.totals,
            itemStyle: {
              color: '#409EFF',
              borderWidth: 1
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
                { offset: 1, color: 'rgba(64, 158, 255, 0.01)' }
              ])
            }
          },
          {
            name: '较前一日变化',
            type: 'line',
            smooth: true,
            data: this.historyData.changes,
            symbol: 'circle',
            lineStyle: {
              type: 'dashed',
              width: 1.5
            },
            itemStyle: {
              color: '#67C23A'
            }
          }
        ]
      }

      this.chartInstance.setOption(option, { replaceMerge: ['series'] })
      this.$nextTick(() => {
        this.chartInstance.resize()
      })
    },

    // 获取历史数据
    async loadHistoryData() {
      try {
        const res = await transportManager.getTransportDailySummary({
          type: this.type
        })
        this.historyData = {
          dates: res.map(item => `${item.date.substr(4, 2)}/${item.date.substr(6, 2)}`),
          totals: res.map(item => item.totalToday),
          changes: res.map(item => item.totalChange)
        }

        if (this.chartInstance) {
          this.renderChart()
        }
      } catch (error) {
        console.error('加载历史数据失败', error)
      }
    },
    async loadStatsData() {
      try {
        const res = await transportManager.getTransportStats()
        this.statsData = res
      } catch (error) {
        console.error('加载统计数据失败:', error)
      }
    },
    formattedChange(val) {
      if (val > 0) return `+${val}车`
      if (val < 0) return `${val}车`
      return '持平'
    },

    changeColor(val) {
      if (val > 0) return '#67C23A'
      if (val < 0) return '#F56C6C'
      return '#999'
    },
    async fetchTransport(refresh) {
      if (this.loadingData) return
      try {
        this.loadingData = true
        if (refresh) {
          this.transportList = []
          this.pageIndex = 0
        } else {
          this.pageIndex++
        }
        const params = {
          pageIndex: this.pageIndex,
          pageSize: this.pageSize,
          type: this.type
        }
        const response = await transportManager.getList(params)
        this.transportList.push(...response.list)
        this.total = response.total
      } catch (error) {
        uni.showToast({
          title: '获取工厂列表失败',
          icon: 'none'
        })
        throw error
      } finally {
        this.loadingData = false
        this.isRefreshing = false
      }
    },
    loadMore() {
      const { loadingData, total, pageIndex, pageSize } = this
      if (!loadingData) {
        if (total > (pageIndex + 1) * pageSize) {
          this.fetchTransport()
        }
      }
    },
    onRefresh() {
      if (this._freshing) return
      this._freshing = true
      this.isRefreshing = true
      this.fetchTransport(true).finally(() => {
        this.isRefreshing = false
        this._freshing = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.news-root {
  // height: 100vh;
  // overflow: hidden;
}

.chart-container {
  z-index: 1;
  position: relative;
  height: 200px;
  background: #fff;
  padding: 16px 0;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

  /deep/ canvas {
    width: 100% !important;
    height: 100% !important;
  }
}

.scroll-view {
  box-sizing: border-box;
  height: calc(100% - 244px);
}

.table-header {
  display: flex;
  padding: 16px;
  background: #fafafa;
  border-bottom: 1px solid #d4d4d4;

  .col {
    font-size: 14px;
    font-weight: 600;
    color: #333;

    &.factory-name {
      flex: 2;
    }
    &.today-data {
      flex: 1;
      text-align: right;
    }
    &.change {
      flex: 1;
      text-align: right;
    }
  }
}

.loading {
  margin: 20px 0;
  height: 40px;
}

.loaded_finish {
  font-size: 14px;
  color: #8a96a3;
  text-align: center;
  margin-top: 15px;
}
.factory-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: #ff6a01;

  .col {
    font-size: 14px;
    font-weight: 600;
    color: #fff !important;
    &.factory-name {
      flex: 2;
    }
    &.today-data {
      flex: 1;
      text-align: right;
    }
    &.change {
      flex: 1;
      text-align: right;
    }
  }
}
</style>
