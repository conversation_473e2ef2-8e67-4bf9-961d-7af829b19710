import messageBox from './messageBox'
import storeManager from './storeManager'
import { requestHost } from '../constant/common'
import platform from './platform'
import SECRET from '../secret'
const MD5 = require('md5.js')
const baseUrl = requestHost + '/api/v1'
let cookieArr = storeManager.get('session_cookie');

// 根据接口传参构建签名
function buildSignQuery (data) {
    const arr = [];
    if (data) {
        Object.keys(data).forEach(key => {
            arr.push({ key, value: data[key] })
        })
    }
    arr.sort((a, b) => a.key > b.key ? -1 : 1);
    const nonce = Math.floor(Math.random() * 10000000000000000).toString(16);
    const timestamp = Date.now();
    arr.push({ key: 'nonce', value: nonce })
    arr.push({ key: 'timestamp', value: timestamp })
    const signStr = arr.map(item => `${item.key}=${encodeURIComponent(item.value)}`).join('&') + `&${SECRET}`
    const signature = new MD5().update(signStr).digest('hex');
    return `nonce=${nonce}&timestamp=${timestamp}&signature=${signature}`
}
function http (method, url, data, config) {
    return new Promise(function (resolve, reject) {
        let httpUrl = `${baseUrl}${url}${url.includes('?') ? '&' : '?'}platform=${platform}`
        let header = {
            'content-type': 'application/x-www-form-urlencoded',
        }
        if (cookieArr) {
            header.cookie = cookieArr.join('; ')
        }
        if (data) {
            for (const key in data) {// 将 undefined 或者 null 的参数清理掉
                (data[key] === undefined || data[key] === null) && delete data[key];
            }
        }
        // 若某个请求需要追加加密签名
        if (config && config.needSign) {
            const signatureQuery = buildSignQuery(data);
            httpUrl += `&${signatureQuery}`
        }
        uni.request({
            url: httpUrl,
            method,
            data,
            header,
            success (res) {
                // 检查cookies
                // 缓存登录接口的用户的票据
                if (res.header['Set-Cookie']) {
                    const newCookieArr = res.header['Set-Cookie'].split('; ').filter(item => item.includes('session')).map(item => item.replace('httponly,', ''));
                    if (newCookieArr.length > 0) {
                        cookieArr = newCookieArr;
                        storeManager.set('session_cookie', cookieArr, 'd365');
                    }
                }
                const data = res.data;
                if (data) {
                    if (data.code === 1) {
                        resolve(data.data);
                    } else if (data.code === 0) {
                        reject(data.message ? data.message : '请求异常');
                    } else if (data.code === -1) {
                        messageBox.alert('您的登录信息已失效，请重新登录').then(() => {
                            // 返回首页后重新登录
                            wx.reLaunch({
                                url: '/pages/index/index'
                            })
                            let app = getApp();
                            app.init(true);
                        })
                    } else {
                        reject('请求异常')
                    }
                } else {
                    reject('请求异常')
                }
            },
            fail (err) {
                reject('请求异常', err)
            }
        })
    })
}

function upload (url, filePath, data) {
    return new Promise((resolve, reject) => {
        uni.uploadFile({
            url: baseUrl + url,
            filePath,
            name: 'file',
            formData: data || {},
            success (res) {
                const data = JSON.parse(res.data);
                if (data.code === 1) {
                    resolve(data.data)
                } else {
                    reject(data.message || '上传失败')
                }
            },
            fail (err) {
                reject(err)
            }
        })
    })
}

['OPTIONS', 'GET', 'HEAD', 'POST', 'PUT', 'DELETE', 'TRACE', 'CONNECT'].forEach(method => {
    http[method.toLocaleLowerCase()] = function (url, data, config) {
        return http(method, url, data, config);
    }
})

http.upload = upload;

export default http;