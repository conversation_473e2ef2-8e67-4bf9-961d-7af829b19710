<template>
    <view class="has_bottom_btn_root">
        <view class="info_item">
            <view class="content">
                <zui-svg-icon class="crop_icon" :icon="cropConfig.icon" width="30px" />
                <span class="crop_name">{{ cropConfig.name }}订单信息</span>
            </view>
        </view>
        <view class="info_item">
            <view class="label required">数量(必填)：</view>
            <view class="content">
                <view class="input_box input_append_box">
                    <input class="input" placeholder="请输入数量" placeholder-class="placeholder" v-model="collectionData.count" type="digit" data-key="count" />
                    <view class="input_append">{{cropConfig.countUnitOnCreate}}</view>
                </view>
            </view>
        </view>
        <view class="info_item" v-for="(item, index) in collectionsArr" :key="index">
            <view class="label">{{ item.label }}{{item.type === 'MULTIPLE' ? '（多选）': ''}}：</view>
            <view v-if="item.type === 'INPUT'" class="content">
                <view class="input_box" :class="{
                    input_append_box: item.key === 'humidity'
                }">
                    <input class="input" placeholder-class="placeholder" :placeholder="`请输入${item.label}`" v-model="collectionData[item.key]" :type="item.options" />
                    <view v-if="item.key === 'humidity'" class="input_append">个水</view>
                </view>
            </view>
            <view v-if="item.type === 'OPTION' || item.type === 'MULTIPLE'" class="content cf">
                <view
                    v-for="(optionItem, oIdx) in item.optionsArr"
                    :key="oIdx"
                    class="check_item"
                    :class="{
                        selected: optionItem.checked,
                        checked_mark: optionItem.checked
                    }"
                    @click="onOptionClick(index, oIdx)"
                >{{ optionItem.value }}</view>
            </view>
        </view>
        <view class="info_item">
            <view class="label required">联系方式(必填)：</view>
            <view class="content">
                <view class="input_box mobile_box">
                    <input class="input" placeholder-class="placeholder" v-model="collectionData.mobile" type="number" placeholder="请输入手机号" maxlength="11" />
                    <button size="mini" class="auth_btn" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber">授权获取</button>
                </view>
            </view>
        </view>
        <view class="info_item">
            <view class="label">价格：</view>
            <view class="content">
                <view class="input_box input_append_box">
                    <input class="input" placeholder="请输入您要的价格" placeholder-class="placeholder" v-model="collectionData.priceOnCreate" type="digit" />
                    <view class="input_append">{{cropConfig.priceUnitOnCreate}}</view>
                </view>
            </view>
        </view>
        <view class="info_item">
            <view class="label">议价方式：</view>
            <view class="content">
                <view class="select_box">
                    <view class="option" :class="{
                        checked: collectionData.fixedPrice == 1
                    }" @click="setOrderPriceType(1)">
                        <view class="mark"></view>
                        <view class="text">不可议价</view>
                    </view>
                    <view class="option" :class="{
                        checked: collectionData.fixedPrice == 0
                    }" @click="setOrderPriceType(0)">
                        <view class="mark"></view>
                        <view class="text">可议价</view>
                    </view>
                </view>
            </view>
        </view>
        <view class="fix_bottom_btn" @click="save">确 定</view>
    </view>
</template>

<script>
import userManager from '../../api/userManager'
import router from "../../router";
import messageBox from "../../utils/messageBox";
const app = getApp();
export default {
    data () {
        return {
            isEdit: false,
            cropConfig: {},
            collectionsArr: [],
            collectionData: {
                countUnitOnCreate: '',
                mobile: '',
                humidity: '',
                fixedPrice: 0,
                priceOnCreate: ''
            }
        }
    },
    onLoad (options) {
        const isEdit = !!options.isEdit; // 是否编辑现有订单
        this.isEdit = isEdit;
        const { newOrder, editOrder, cropsMap } = app.globalData;
        let cropConfig;
        if (isEdit) {
            this.collectionData = editOrder;
            cropConfig = cropsMap[editOrder.cropChildId || editOrder.cropId];
            this.cropConfig = cropConfig;
            this.collectionsArr = cropConfig.collectionsArr;
            this.setOptionsChecked();
        } else {
            cropConfig = cropsMap[newOrder.cropChildId || newOrder.cropId];
            this.cropConfig = cropConfig;
            this.collectionsArr = cropConfig.collectionsArr;
            const user = userManager.getUser();
            this.collectionData.mobile = user.mobile || '';
        }
    },
    methods: {
        setOptionsChecked () {
            this.collectionsArr.forEach((collectItem, index) => {
                if (collectItem.type === 'OPTION' || collectItem.type === 'MULTIPLE') {
                    const value = this.collectionData[collectItem.key]
                    if (value) {
                        collectItem.optionsArr.forEach(item => {
                            item.checked = value.includes(item.value);
                        })
                    }
                }
            })
        },
        onOptionClick (index1, index2) {
            const collectItem = this.collectionsArr[index1];
            collectItem.optionsArr = collectItem.optionsArr.map((item, oIndex) => {
                if (oIndex === index2) {
                    item.checked = !item.checked;
                } else if (collectItem.type === 'OPTION') {
                    item.checked = false;
                }
                return item;
            })
            const optValue = collectItem.optionsArr.filter(item => item.checked).map(item => item.value).join('、');
            this.collectionData[collectItem.key] = optValue
        },

        setOrderPriceType (val) {
            this.collectionData.fixedPrice = val * 1;
        },

        save () {
            const { mobile, count, fixedPrice, priceOnCreate } = this.collectionData;
            if (!mobile) {
                messageBox.alert('请添加电话号码')
            } else if (!count) {
                messageBox.alert('请输入出售作物的数量')
            } else if (fixedPrice == 1 && !priceOnCreate) {
                messageBox.alert('请输入您的要价')
            } else {
                if (this.isEdit) {
                    Object.assign(app.globalData.editOrder, this.collectionData);
                    router.startPickAddress(true)
                } else {
                    Object.assign(app.globalData.newOrder, this.collectionData);
                    router.startPickAddress()
                }
            }
        },
        getPhoneNumber (e) {
            const { code, errMsg } = e.detail;
            if (errMsg === 'getPhoneNumber:ok') {
                uni.showLoading({ title: '获取中...' })
                userManager.getWxPhone(code).then(data => {
                    const { phoneNumber, purePhoneNumber } = data;
                    const mobile = phoneNumber || purePhoneNumber;
                    this.collectionData.mobile = mobile
                }).catch(err => {
                    messageBox.alert('手机号码获取失败，请手动输入！');
                }).then(() => {
                    uni.hideLoading();
                })
            } else {
                messageBox.alert('您未同意授权获取手机号，请手动输入！');
            }
        }
    }
}
</script>

<style lang="scss">
$primaryColor: #ff6a01;
.info_item {
    padding: 0 15px;
    & + .info_item {
        margin-top: 20px;
    }
}
.label {
    font-size: 16px;
    font-weight: 600;
    line-height: 2;
}
.content {
    .check_item {
        float: left;
        width: 46%;
        font-size: 14px;
        line-height: 36px;
        border: 1px solid #ccc;
        text-align: center;
        margin-bottom: 10px;
        &.selected {
            border-color: $primaryColor;
            color: $primaryColor;
        }
        &:nth-child(2n) {
            float: right;
        }
    }
    .crop_icon {
        display: inline-block;
        vertical-align: middle;
    }
    .crop_name {
        font-size: 18px;
        line-height: 30px;
        margin-left: 10px;
    }
}
.input_box {
    border-bottom: 1px solid #aaa;
    position: relative;
    &.input_append_box {
        padding-right: 60px;
    }
    &.mobile_box {
        padding-right: 105px;
    }
    input {
        display: block;
        width: 100%;
        padding: 10px 0;
        height: 30px;
        line-height: 30px;
        font-size: 26px;
    }
    .placeholder {
        font-size: 18px;
        line-height: 30px;
        color: #ccc;
    }
    .input_append {
        line-height: 20px;
        font-size: 18px;
        margin-top: -10px;
        position: absolute;
        top: 50%;
        right: 0;
        color: #999;
    }
    .auth_btn {
        font-size: 15px !important;
        height: 36px;
        line-height: 34px;
        width: 100px !important;
        padding: 0;
        background: none;
        border: 1px solid #999999;
        color: #666666;
        font-weight: normal;
        text-align: center;
        border-radius: 18px;
        position: absolute;
        right: 0;
        top: 50%;
        margin-top: -18px;
    }
}
.select_box {
    .option {
        box-sizing: border-box;
        width: 40%;
        padding-left: 24px;
        font-size: 18px;
        position: relative;
        display: inline-block;
        &:nth-child(2n) {
            margin-left: 10%;
        }
        .mark {
            width: 18px;
            height: 18px;
            border: 1px solid #999;
            border-radius: 50%;
            margin-top: -10px;
            position: absolute;
            top: 50%;
            left: 0;
        }
        &.checked {
            .mark {
                border-color: #ff6a01;
                &::after {
                    content: '';
                    width: 10px;
                    height: 10px;
                    background-color: #ff6a01;
                    border-radius: 50%;
                    position: absolute;
                    top: 4px;
                    left: 4px;
                }
            }
        }
    }
}
</style>