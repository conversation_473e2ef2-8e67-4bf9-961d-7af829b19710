<template>
    <view class="trade_root" v-if="order">
        <view class="message">若您的订单已成交，可录入成交信息</view>
        <view class="message">系统将自动关闭你的订单出售</view>
        <view class="label">成交单价（{{cropConfig.priceUnit}}）：</view>
        <view class="input_box">
            <input class="input" placeholder="请输入成交单价" type="digit" maxlength="6" v-model="tradeData.price" />
        </view>
        <view class="label">湿度信息：</view>
        <view class="input_box textarea_box">
            <textarea class="textarea" auto-height placeholder="请输入湿度信息" v-model="tradeData.humidity"></textarea>
        </view>
        <view class="notice">输入示例：麦茬玉米，小贩量得，多数心里20个水，插一半15个水；有两囤插一半20个水左右，插到低要30个水。</view>
        <view class="label">成交方式：</view>
        <view class="select_box">
            <view class="option" :class="{ checked: tradeData.way === 1 }" @click="tradeData.way = 1">
                <view class="mark"></view>
                <view class="text">平台联系成交</view>
            </view>
            <view class="option" :class="{ checked: tradeData.way === 0 }" @click="tradeData.way = 0">
                <view class="mark"></view>
                <view class="text">其他方式成交</view>
            </view>
        </view>
        <view class="fix_bottom_btn" @click="onSubmit">确 定</view>
    </view>
</template>

<script>
import logManager from '../../api/logManager'
import orderManager from '../../api/orderManager'
import messageBox from '../../utils/messageBox'
const app = getApp()
export default {
    data () {
        return {
            cropConfig: {},
            order: null,
            tradeData: {
                price: '',
                humidity: '',
                way: ''
            }
        }
    },
    onLoad () {
        logManager.addLog('submit_trade_price_view')
        let order = orderManager.getSubmitOrder();
        if (!order) {
            wx.navigateBack()
            return
        }
        const { cropChildId, cropId } = order;
        const { cropsMap } = app.globalData;
        this.cropConfig = cropsMap[cropChildId || cropId];
        this.order = order
    },
    methods: {
        onSubmit () {
            const { order, tradeData: { price, humidity, way }, cropConfig: { priceUnit } } = this;
            if (!price || isNaN(price)) {
                messageBox.alert(`请输入正确的成交单价，按${crop.priceUnit}计算`)
            } else if (!humidity) {
                messageBox.alert('请输入湿度信息')
            } else if (way === '') {
                messageBox.alert('请选择您的成交方式')
            } else {
                const params = Object.assign(this.tradeData, { priceUnit })
                orderManager.submitTrade(order.id, params).then(() => {
                    wx.navigateBack({
                        delta: 2
                    });
                }).catch(err => {
                    messageBox.alert(err)
                })
            }
        }
    }
}
</script>

<style lang="scss">
page {
    background: #f3f3f3;
    padding-bottom: 20px;
}

.trade_root {
    padding: 15px 15px 60px 15px;
}

.message {
    font-size: 15px;
    line-height: 22px;
    text-align: center;
    color: #787878;
}

.notice {
    margin-top: 5px;
    font-size: 12px;
    line-height: 16px;
    color: #ff940a;
}

.title {
    font-size: 18px;
    text-align: center;
    font-weight: 600;
}

.order_info_box .create_time {
    font-size: 13px;
    color: #555555;
}
.label {
    font-size: 15px;
    line-height: 2;
    margin-top: 15px;
    color: #555555;
}

.input_box {
    padding: 10px;
    background: #ffffff;
    .input {
        font-size: 16px;
        line-height: 30px;
        height: 30px;
        display: block;
        width: 100%;
    }
    &.textarea_box {
        padding-bottom: 40px;
    }
}

.select_box {
    margin-top: 10px;
    .option {
        padding-left: 24px;
        padding-right: 30px;
        font-size: 18px;
        position: relative;
        display: inline-block;
        &:nth-child(2n) {
            float: right;
        }
        .mark {
            width: 18px;
            height: 18px;
            border: 1px solid #999;
            border-radius: 50%;
            margin-top: -10px;
            position: absolute;
            top: 50%;
            left: 0;
        }
        &.checked {
            .mark {
                border-color: #ff6a01;
                &::after {
                    content: '';
                    width: 10px;
                    height: 10px;
                    background-color: #ff6a01;
                    border-radius: 50%;
                    position: absolute;
                    top: 4px;
                    left: 4px;
                }
            }
        }
    }
}
</style>