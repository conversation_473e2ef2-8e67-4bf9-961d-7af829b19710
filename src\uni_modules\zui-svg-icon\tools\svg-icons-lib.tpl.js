/**
 *
 * Icon Library for <zui-svg-icon> usage
 *
 * Auto generated by /tools/generate-svg-icon.js
 *
 * !!! DO NOT MODIFY MANUALLY !!!
 *
 * @datetime __datetime__
 *
 */

// == collection start
const collections = {
  default: __default__,
}
// == collection end


function registerCollection (key, lib) {
  if (collections[key]) {
    return
  }

  if (typeof lib.registerCollection === 'function') {
    collections[key] = lib.getCollection('default')
  } else {
    collections[key] = lib
  }
}

function getCollection (key) {
  key = key || 'default'
  if (!collections[key]) throw new Error(`没有找到名为 ${key} 的图标库。`)

  return collections[key]
}

export const SvgIconLib = { registerCollection, getCollection }
export default SvgIconLib
