## 0.1.55（2024-01-22）
1. 回滚版本至: 0.1.51, 在未明确bug原因时盲目修改导致了新问题.
## 0.1.51（2023-12-06）
1. 重新编写使用文档
## 0.1.50（2023-12-06）
1. 当图标不支持换色, 却设置了颜色值时给出警告提示
## 0.1.49（2023-11-28）
1. 修复分组图标无法显示的 bug
## 0.1.48（2023-11-23）
1. 新增高度属性  height, 设置图标尺寸更直观
2. 新增 rpx 单位支持
## 0.1.46（2023-11-19）
1. 修复宽高比计算错误的问题
## 0.1.45（2023-11-15）
1. 优化图标名称定义，除正反斜线(/ 和 \)会被替换为短横线(-)以外，其余字符保持和图标文件名称一致。
## 0.1.44（2023-11-13）
1. 修复上一版测试代码导致插件不工作的问题
## 0.1.43（2023-11-13）
1. 修复windows平台无法加载分组图标的 bug
2. 增加图标无限级分组功能
## 0.1.41（2023-11-04）
1. 修复在 SVG 预处理时可能导致程序异常中止的 bug
## 0.1.40（2023-11-03）
1. 增加图标库功能，支持导入外部图标库
## 0.1.39（2023-10-20）
1. 修复示例文件静态图标路径不正确的问题
## 0.1.38（2023-10-19）
1. 修复启动微信小程序开发第一次运行时报错的问题
## 0.1.37（2023-10-08）
1. 增加图标分组功能。基于文件夹对图标进行分组管理
## 0.1.36（2023-10-08）
1. 补充 cli 项目使用说明
## 0.1.35（2023-09-26）
1. icon 参数增加对 svg 源码字符串的支持
## 0.1.34（2023-09-11）
1. 增加颜色锁定功能
## 0.1.33（2023-09-10）
1. 修复了当 svg 图片中没有颜色定义，但在使用中又指定了颜色时导致图片无法显示的问题
## 0.1.32（2023-09-04）
1.  增强组件的点击事件兼容处理，同时支持 @click 和 @tap 事件
## 0.1.31（2023-09-01）
1. 修复当 svg 文件使用 currentColor 时导致无法替换颜色的问题
## 0.1.30（2023-08-30）
1. 修复提取颜色时会漏掉一些颜色的问题
2. 优化对纯黑色的处理
3. 修复渲染时可能导致渲染失败的问题
4. 优化示例
## 0.1.26（2023-08-26）
小更新
## 0.1.25（2023-08-26）
1. 优化组件结构
2. 修复支付宝小程序无法点击的 bug
3. 优化使用说明

##### 示例页面

1. 修复示例页面文案错误
2. 修复圆角滑块抖动的 bug
## 0.1.23（2023-08-22）
1. 增加图标圆角设置
2. 修复示例项目中点击 slider 不更新的问题

## 0.1.22（2023-08-21）

1. 优化未定义的图标处理方式。未定义的图标不会再阻断程序运行。

## 0.1.21（2023-08-18）

1. 修复了由于 svgo 将纯黑色优化掉后导致图标无法改色的 bug
2. 优化对 svg 文件 class 定义识别处理
3. 修复了 vue3 点击事件处理
4. 其它修改

## 0.1.18（2023-08-17）

1. 修复微信小程序、飞书小程序和 QQ 小程序 click/tap 事件会重复触发的 bug
2. 修复颜色识别 bug
3. 增加 spin 效果
4. 增加 rgba/hsl 颜色格式支持
5. 更新演示图标

## 0.1.15（2023-08-15）

1. 优化事件处理
2. 增加普通图片文件图标支持, 但不支持此类图标修改颜色
3. 增加灰度参数设置

## 0.1.14（2023-08-12）

1. 更新示例

## 0.1.13（2023-08-12）

1. 添加渐变色图标示例

## 0.1.12（2023-08-12）

1. 修复生成工具不更新的bug;
2. 增加点击事件响应;
3. 更新示例;
PS: 升级插件后需要重新生成图标库;

## 0.1.10（2023-08-11）

1. 更新小程序兼容性列表

## 0.1.9（2023-08-11）

1. 添加支付宝小程序支持

## 0.1.8（2023-08-10）

1. 添加使用 class 定义颜色的支持
2. 添加渐变颜色支持
3. 优化颜色替换机制
4. 清理不必要的文件

## 0.1.7（2023-08-10）

1. 修复demo地址错误

## 0.1.6（2023-08-10）

1. 添加小程序兼容性说明
2. 增加demo页面

## 0.1.5（2023-08-09）

1. 修复在APP和小程序端图标高度显示不正确的bug
2. 添加参数说明文档
3. 其它修改

## 0.1.4（2023-08-08）

1. 添加小程序支持
2. 修复多色填充bug
3. 图标库生成工具只有在数据变化后才重新写入

## 0.1.2（2023-08-07）

1. 添加svgo配置
2. 修复生成图标库时路径不正确的问题
3. VUE3 支持

## 0.1.1（2023-08-07）

1. 更新 readme.md

## 0.1.0（2023-08-07）

1. 单色SVG图标显示与换色
2. 多色SVG图标显示与换色
