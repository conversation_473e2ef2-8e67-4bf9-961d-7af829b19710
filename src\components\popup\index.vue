<template>
  <div class="popup" :class="{ popup__visible: visible }" v-if="visible">
    <div class="popup__mask" @click="hidePopup"></div>
    <div class="popup__content" :style="{ width: contentWidth }" @click.stop>
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Popup',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    contentWidth: {
      type: String,
      default: '90%'
    }
  },
  data() {
    return {
      visible: this.value
    }
  },
  watch: {
    value(val) {
      this.visible = val
    }
  },
  methods: {
    hidePopup() {
      this.$emit('input', false)
    }
  }
}
</script>

<style scoped>
.popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000000;
  opacity: 1;
  transition: opacity 0.2s ease-in-out;
  animation-fill-mode: forwards;
}
.popup__visible {
  animation-name: fadeIn;
  animation-duration: 0.1s;
}
.popup:not(.popup__visible) {
  animation-name: fadeOut;
  animation-duration: 0.3s;
}
.popup__mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}
.popup__content {
  box-sizing: border-box;
  margin-top: 38px;
  z-index: 1;
}
</style>
