<template>
    <div class="trade_item" @click="$emit('click')">
        <div class="icon"></div>
        <div class="name">{{ cropConfig.name }} ： {{trade.order.count}}{{cropConfig.countUnitOnCreate}}</div>
        <div class="info_box">
            <div class="info">品类：{{ trade.order.variety ? trade.order.variety : cropConfig.name }}</div>
            <div v-if="_order.humidityValue" class="info">湿度：{{ _order.humidityValue }}</div>
            <div class="info">
                <span>成交价：</span>
                <span class="price">
                    <span class="number">{{ trade.price }}</span>
                    <span>{{ trade.priceUnit }}</span>
                </span>
            </div>
            <div class="info address">交货地：{{ trade.order.address }}</div>
        </div>
        <div class="contact">{{ _order.updateTime }}</div>
    </div>
</template>

<script>
export default {
    props: {
        trade: {
            type: Object,
            default: () => ({})
        }
    },
    computed: {
        _order () {
            const app = getApp()
            const { cropChildId, cropId } = this.trade.order;
            const { cropsMap } = app.globalData;
            const cropConfig = cropsMap[cropChildId || cropId];
            let humidityValue = null;
            if (this.trade.humidity) {
                if (isNaN(this.trade.humidity)) {
                    humidityValue = this.trade.humidity
                } else {
                    humidityValue = `${this.trade.humidity}个水`
                }
            }
            let updateTime;
            const todayZeroTime = new Date().setHours(0, 0, 0, 0);
            const dayLong = 24 * 3600 * 1000;
            if (this.trade.createTime >= todayZeroTime) {
                updateTime = `今天 ${new Date(this.trade.createTime).format('hh:mm')}`
            } else if (this.trade.createTime >= todayZeroTime - dayLong) {
                updateTime = `昨天 ${new Date(this.trade.createTime).format('hh:mm')}`
            } else if (this.trade.createTime >= todayZeroTime - dayLong * 2) {
                updateTime = `前天 ${new Date(this.trade.createTime).format('hh:mm')}`
            } else {
                updateTime = new Date(this.trade.createTime).format('yyyy-MM-dd')
            }
            return {
                cropConfig: cropConfig,
                humidityValue,
                updateTime
            }
        },
        cropConfig () {
            return this._order.cropConfig
        }
    }
}
</script>


<style lang="scss">
.trade_item {
    padding: 0 16px;
    border-bottom: 10px solid #f6f6f6;
    font-size: 14px;
    position: relative;
}
.name {
    font-size: 16px;
    line-height: 2.4;
    font-weight: 600;
}
.info_box {
    padding: 8px 12px;
    background: #f6f6f6;
}
.info {
    font-size: 14px;
    line-height: 28px;
    color: #787878;
    &.address {
        color: #333;
    }
}
.contact {
    font-size: 12px;
    line-height: 3;
    color: #999;
    text-align: right;
}
.price {
    color: #ff6a01;
    .number {
        font-size: 20px;
        margin-right: 6px;
    }
}
</style>