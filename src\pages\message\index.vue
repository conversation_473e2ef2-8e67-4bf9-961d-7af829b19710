<template>
    <view class="message_root">
        <no-data v-if="total === 0" msg="未查询到消息记录" />
        <view v-if="renderMessageArr.length > 0">
            <view v-for="(item, index) in renderMessageArr" :key="index" class="msg_user_item_box" :data-index="index" bind:tap="onChatClick">
                <view class="user_photo_box">
                    <image class="photo" mode="aspectFill" :src="item.avatar" />
                    <view v-if="item.redPoint" class="red_point"></view>
                </view>
                <view class="msg_content_box">
                    <view class="user_name line1_only">{{item.name}}</view>
                    <view class="content line1_only">{{item.content || item.orderDesc}}</view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import msgManager from '../../api/msgManager'
import userManager from '../../api/userManager'
import router from '../../router'
const app = getApp()
export default {
    data () {
        return {
            pageIndex: 0,
            pageSize: 200,
            total: -1,
            loading: false,
            messageArr: [],
            renderMessageArr: [],
            userMap: {}
        }
    },
    onLoad () {

    },
    onShow: function () {
        this.refreshData();
    },
    methods: {
        refreshData () {
            uni.showLoading({ mask: true })
            this.pageIndex = 0;
            msgManager.queryMyMessage(this.pageIndex, this.pageSize).then(data => {
                this.total = data.total;
                this.messageArr = data.list;
                this.buildData(data);
                uni.hideLoading()
            }).catch(err => {
                uni.hideLoading()
            })
        },
        buildData (data) { // 根据返回的消息刷新页面数据，根据人进行划分
            const { users, orders } = data;
            const orderDescMap = {};
            users.forEach(user => {
                if (!this.userMap[user.id]) {
                    this.userMap[user.id] = {
                        name: user.name || `千万仓用户_${user.id}`,
                        avatar: user.avatar || '/static/images/user_default.png'
                    }
                }
            })
            orders.forEach(order => {
                const { cropChildId, cropId } = order;
                const { cropsMap } = app.globalData;
                const cropConfig = cropsMap[cropChildId || cropId];
                orderDescMap[order.id] = `[订单] ${cropConfig.name} · ${order.count + cropConfig.countUnitOnCreate}`
            })
            const arr = [];
            const indexMap = {};
            const userId = userManager.getUser().id;
            this.messageArr.forEach(item => {
                const selfMsg = item.userId === userId;
                const otherUserId = selfMsg ? item.toUserId : item.userId;
                if (!indexMap[otherUserId]) {
                    const { name, avatar } = this.userMap[otherUserId];
                    arr.push(Object.assign({
                        otherUserId,
                        name,
                        avatar,
                        orderDesc: orderDescMap[item.orderId] || '',
                        redPoint: !selfMsg && !item.status // 是否显示未读红点
                    }, item))
                    indexMap[otherUserId] = true;
                }
            })
            this.renderMessageArr = arr;
        },
        onChatClick (e) {
            const { index } = e.currentTarget.dataset;
            const { name, otherUserId } = this.renderMessageArr[index];
            router.startChat(otherUserId, name);
        }
    }
}
</script>

<style lang="scss">
.msg_user_item_box {
    margin-left: 15px;
    padding-left: 60px;
    position: relative;
    .user_photo_box {
        width: 48px;
        height: 48px;
        position: absolute;
        left: 0;
        top: 12px;
        .photo {
            width: 100%;
            height: 100%;
            display: block;
            border-radius: 4px;
        }
        .red_point {
            width: 10px;
            height: 10px;
            background-color: red;
            position: absolute;
            top: -3px;
            right: -3px;
            border-radius: 50%;
        }
    }
    .msg_content_box {
        padding: 12px 15px 12px 0;
        border-bottom: 1rpx solid #e3e3e4;
    }
    .user_name {
        font-size: 18px;
        line-height: 26px;
    }
    .content {
        font-size: 14px;
        line-height: 22px;
        color: #999;
    }
}
</style>
