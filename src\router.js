import { ASK_ORDER_NOW } from './constant/storeKeys'
import storeManager from './utils/storeManager'

function joinParams(obj) {
  let paramsStr = ''
  for (const key in obj) {
    const item = obj[key]
    if (item !== undefined && item !== null) {
      paramsStr += `&${key}=${encodeURIComponent(item)}`
    }
  }
  if (paramsStr.length > 0) {
    return '?' + paramsStr.substr(1)
  } else {
    return ''
  }
}

// 前往授权页
function startAuth(events) {
  wx.navigateTo({
    url: '/pages/authorize/index',
    events
  })
}

// 选择农作物
function startSelectOrderCrop() {
  wx.navigateTo({
    url: '/pages/order/selectCrop'
  })
}
// 增加基础录入信息
function startAddBaseInfo() {
  wx.navigateTo({
    url: '/pages/order/addBase'
  })
}
// 增加基础录入信息
function startEditOrder(order) {
  const app = getApp()
  app.globalData.editOrder = order
  wx.navigateTo({
    url: '/pages/order/addBase?isEdit=1'
  })
}

// 选择地址信息
function startSelectAddressInfo() {
  wx.navigateTo({
    url: '/pages/order/addLocation'
  })
}

// 选择位置的路况
function startSelectWayType() {
  wx.navigateTo({
    url: '/pages/order/addWayType'
  })
}

// 选择位置的路况
function startUpdateWayType() {
  wx.navigateTo({
    url: '/pages/order/addWayType?edit=1'
  })
}
function startPickAddress(isEdit) {
  const url = '/pages/order/addLocation' + (isEdit ? '?isEdit=1' : '')
  wx.navigateTo({
    url
  })
}
// 选择视频图片的信息
function startAddVideoAndPhoto() {
  wx.navigateTo({
    url: '/pages/order/addVideo'
  })
}
// 选择视频图片的信息
function startEditVideoAndPhoto() {
  wx.navigateTo({
    url: '/pages/order/addVideo?isEdit=1'
  })
}
// 信息录入完毕，最后检查确认页
function startOrderCreateCheck(isEdit) {
  let url = '/pages/order/confirm' + (isEdit ? '?isEdit=1' : '')
  wx.navigateTo({ url })
}
// 查看订单详情页
function startOrderDetail(order, events) {
  const app = getApp()
  app.globalData.viewOrder = order
  wx.navigateTo({
    url: `/pages/sale/detail?id=${order.id}`,
    events
  })
}
// 查看订单详情页
function startTradeOrderDetail(trade) {
  const app = getApp()
  app.globalData.viewTrade = trade
  wx.navigateTo({
    url: `/pages/order/trade?id=${trade.id}`
  })
}

// 开始做信息收集，这里需要判断第一个需要收集的信息，然后选择对应的路由
function startOrderCollect() {
  const app = getApp()
  const { crop, askOnly } = app.globalData.newOrder
  let userCollectionArr = []
  let collectionArr = crop.collection.split(',')
  if (crop.needCollector && !askOnly) {
    // 需要信息收集员并且不是询价订单，过滤信息收集员收集的项目
    let collectorCollectionArr = crop.collectorCollection.split(',')
    collectionArr.forEach(id => {
      if (collectorCollectionArr.indexOf(id) === -1) {
        userCollectionArr.push(id)
      }
    })
  } else {
    userCollectionArr = collectionArr
  }
  const jumpCollection = crop.jumpCollection ? crop.jumpCollection.split(',') : []
  userCollectionArr = userCollectionArr.map(id => {
    let jumpAble = jumpCollection.indexOf(id) > -1
    return Object.assign({ jumpAble }, app.globalData.collectionsMap[id])
  })
  app.globalData.newOrder.countUnit = crop.orderCountUnit
  app.globalData.newOrder.userCollectionArr = userCollectionArr
  toOrderInfoPage(0, app.globalData.newOrder.userCollectionArr[0])
}

// 收集下一个信息
function nextOrderCollect(cId) {
  const app = getApp()
  const { userCollectionArr, count, crop } = app.globalData.newOrder
  const collection = userCollectionArr[cId]
  if (collection) {
    // 如果下一个收集的信息是半挂车路况信息, 并且设定了路况信息考虑下限
    if (collection.collectionKey === 'semitrailer' && crop.considerWayMinCount) {
      if (count < crop.considerWayMinCount) {
        const newCollection = userCollectionArr[cId + 1]
        if (newCollection) {
          toOrderInfoPage(cId + 1, newCollection)
        } else {
          startOrderCreateCheck()
        }
      } else {
        // 下单量达到考虑半挂车路况的下限
        toOrderInfoPage(cId, collection)
      }
    } else {
      toOrderInfoPage(cId, collection)
    }
  } else {
    startOrderCreateCheck()
  }
}

// 提交交易信息
function startSubmitTrade() {
  wx.navigateTo({
    url: '/pages/my/submitTrade'
  })
}

// 查看我的订单
function startMyOrder() {
  wx.navigateTo({
    url: '/pages/my/orders'
  })
}
// 查看我的订单
function startMyLikeOrder() {
  wx.navigateTo({
    url: '/pages/my/like'
  })
}
// 注册收购商
function startBuyerRegister() {
  wx.navigateTo({
    url: '/pages/my/buyerRegister'
  })
}

// 打开一个人的聊天界面
function startChat(otherUserId, otherName, order) {
  if (order) {
    storeManager.set(ASK_ORDER_NOW, order, 'm5')
  } else {
    storeManager.clear(ASK_ORDER_NOW)
  }
  wx.navigateTo({
    url: `/pages/message/chat?otherUserId=${otherUserId}&otherName=${encodeURIComponent(
      otherName || ''
    )}`
  })
}

function goToPointsPage() {
  wx.navigateTo({
    url: '/pages/points/index'
  })
}

// 信息收集员查看待收集订单
function startCollectList() {
  wx.navigateTo({
    url: '/pages/my/collect'
  })
}

// 信息收集员收集订单
let collectingOrder
function getCollectingOrder() {
  return collectingOrder
}
function startCollectInput(order) {
  collectingOrder = order
  if (collectingOrder.cropId == 2) {
    // 收集小麦信息
    wx.navigateTo({
      url: '/pages/my/collectInput2'
    })
  } else {
    // 收集囤玉米信息
    wx.navigateTo({
      url: '/pages/my/collectInput'
    })
  }
}

// 查看千万仓的网页
function showH5(title, url) {
  wx.navigateTo({
    url: '/pages/h5/index' + joinParams({ title, url })
  })
}
// 查看千万仓的网页
function startMyInfo(events) {
  wx.navigateTo({
    url: '/pages/my/info',
    events
  })
}

function showOrderInfo(order) {
  let app = getApp()
  app.globalData.orderInfo = order
}

export default {
  startAuth,
  startSelectOrderCrop,
  startAddBaseInfo,
  startEditOrder,
  startSelectAddressInfo,
  startSelectWayType,
  startUpdateWayType,
  startPickAddress,
  startAddVideoAndPhoto,
  startEditVideoAndPhoto,
  startOrderDetail,
  startTradeOrderDetail,
  startMyOrder,
  startMyLikeOrder,
  startBuyerRegister,
  startChat,

  startOrderCollect,
  nextOrderCollect,
  startOrderCreateCheck,
  startSubmitTrade,
  goToPointsPage,
  startCollectList,
  getCollectingOrder,
  startCollectInput,
  showOrderInfo,
  startMyInfo,
  showH5
}
