<template>
  <view class="order_info_wrapper">
    <view class="order_info_part">
      <view class="title">
        <image class="icon" src="/static/images/crop_icon.png" />
        <view class="label">订单信息</view>
      </view>
      <view class="info_box label4" v-if="updateTime">
        <view class="label">更新时间：</view>
        <view class="content">{{ updateTime }}</view>
      </view>
      <view class="info_box">
        <view class="label">作物：</view>
        <view class="content">
          <view class="crop_icon selected" :class="[cropConfig.icon]"></view>
          <view class="crop_name">{{ cropConfig.name }}</view>
        </view>
      </view>
      <view class="info_box" v-if="info.variety">
        <view class="label">品种：</view>
        <view class="content">{{ info.variety }}</view>
      </view>
      <view class="info_box" v-if="info.count">
        <view class="label">数量：</view>
        <view class="content">
          <text class="number">{{ info.count }}</text>
          <text class="unit">{{ cropConfig.countUnitOnCreate }}</text>
        </view>
      </view>
      <view class="info_box" v-if="info.priceOnCreate">
        <view class="label">价格：</view>
        <view class="content">
          <text class="number price">{{ info.priceOnCreate }}</text>
          <text class="unit">{{ cropConfig.priceUnitOnCreate }}</text>
        </view>
      </view>
      <view class="info_box label4">
        <view class="label">议价方式：</view>
        <view class="content">{{ info.fixedPrice ? '不可议价' : '可议价' }}</view>
      </view>
      <view class="info_box" v-if="info.humidity">
        <view class="label">湿度：</view>
        <view class="content">{{ info.humidity }}</view>
      </view>
      <view class="info_box label4" v-if="info.pickProcess">
        <view class="label">采摘过程：</view>
        <view class="content">{{ info.pickProcess }}</view>
      </view>
      <view class="info_box label4" v-if="info.pickType">
        <view class="label">采摘方式：</view>
        <view class="content">{{ info.pickType }}</view>
      </view>
    </view>
    <view class="order_info_part" v-if="trade">
      <view class="title">
        <image class="icon" src="/static/images/trade_icon.png" />
        <view class="label">成交信息</view>
      </view>
      <view class="info_box label4" v-if="tradeTime">
        <view class="label">成交时间：</view>
        <view class="content">{{ tradeTime }}</view>
      </view>
      <view class="info_box label4" v-if="trade.humidity">
        <view class="label">湿度信息：</view>
        <view class="content">{{ trade.humidity }}</view>
      </view>
      <view class="info_box label4" v-if="trade.price">
        <view class="label">成交价格：</view>
        <view class="content">
          <text class="number price">{{ trade.price }}</text>
          <text class="unit">{{ trade.priceUnit }}</text>
        </view>
      </view>
    </view>
    <view class="order_info_part">
      <view class="title">
        <image class="icon" src="/static/images/location_icon.png" />
        <view class="label">位置信息</view>
      </view>
      <view class="info_box" v-if="info.address">
        <view class="label">位置：</view>
        <view class="content" @click="onMapTap">
          {{ info.address }}
          <text class="map_guide">（点击可查看导航）</text>
        </view>
      </view>
      <view class="info_box label3">
        <view class="label">半挂车：</view>
        <view class="content">{{ info.semitrailer ? '可以进半挂车' : '不可以进半挂车' }}</view>
      </view>
      <view class="info_box">
        <view class="label">铲车：</view>
        <view class="content">{{ info.forklift ? '可以使用铲车' : '不可以使用铲车' }}</view>
      </view>
    </view>
    <view class="order_info_part">
      <view class="title">
        <image class="icon" src="/static/images/video_icon.png" />
        <view class="label">视频照片</view>
      </view>
      <view class="info_box label_block" v-if="info.overallVideos && info.overallVideos.length > 0">
        <view class="label">整体视频：</view>
        <view class="content">
          <view
            class="video_item"
            :class="{
              only1: info.overallVideos.length === 1
            }"
            v-for="(item, index) in info.overallVideos"
            :key="index"
          >
            <video class="video" :src="item" controls></video>
          </view>
        </view>
      </view>
      <view class="info_box label_block" v-if="info.videos && info.videos.length > 0">
        <view class="label">测量视频：</view>
        <view class="content">
          <view
            class="video_item"
            :class="{
              only1: info.videos.length === 1
            }"
            v-for="(item, index) in info.videos"
            :key="index"
          >
            <video class="video" :src="item" controls></video>
          </view>
        </view>
      </view>
      <view class="info_box label_block" v-if="info.photos && info.photos.length > 0">
        <view class="label">详细图片：</view>
        <view class="content" v-if="action === 'view'">
          <view class="big_photo_item" v-for="(item, index) in info.photos" :key="index">
            <image class="photo" :src="item" mode="widthFix" @click="onPhotoClick(index)" />
          </view>
        </view>
      </view>
    </view>
    <view v-if="info.remark" class="order_info_part">
      <view class="title" style="padding-left: 15px">
        <view class="label">订单备注</view>
      </view>
      <view class="info_box label_block">{{ info.remark }}</view>
    </view>
  </view>
</template>

<script>
import { timeToDateString } from '../utils/date'
const app = getApp()
export default {
  props: {
    info: {
      type: Object,
      default: () => ({})
    },
    trade: {
      type: Object,
      default: null
    },
    action: {
      type: String,
      default: 'view'
    }
  },
  computed: {
    tradeTime() {
      if (this.trade && this.trade.createTime) {
        return timeToDateString(this.trade.createTime)
      } else {
        return ''
      }
    },
    cropConfig() {
      if (this.info && this.info.cropId) {
        return app.globalData.cropsMap[this.info.cropChildId || this.info.cropId]
      } else {
        return {}
      }
    },
    updateTime() {
      if (this.info && this.info.updateTime) {
        return timeToDateString(this.info.updateTime)
      } else {
        return ''
      }
    }
  },
  methods: {
    onPhotoClick(index) {
      const urls = this.info.photos
      const current = urls[index]
      uni.previewImage({ urls, current })
    },
    onMapTap() {
      const { latitude, longitude } = this.info
      uni.openLocation({
        latitude,
        longitude,
        scale: 16
      })
    }
  }
}
</script>

<style lang="scss">
.order_info_part {
  background-color: #fff;
  & + .order_info_part {
    border-top: 10px solid #f1f1f1;
  }
  .title {
    font-size: 18px;
    line-height: 50px;
    padding: 0 50px;
    position: relative;
    .icon {
      width: 20px;
      height: 20px;
      position: absolute;
      margin-top: -10px;
      top: 50%;
      left: 15px;
    }
    &::after {
      content: '';
      width: 10px;
      height: 10px;
      box-sizing: border-box;
      border-width: 2px 2px 0 0;
      border-style: solid;
      border-color: #999999;
      transform: rotate(135deg);
      position: absolute;
      top: 18px;
      right: 15px;
    }
  }
  .info_box {
    margin: 0 15px;
    padding: 10px 0 10px 3em;
    font-size: 14px;
    line-height: 20px;
    position: relative;
    .label {
      color: #666;
      position: absolute;
      left: 0;
      top: 10px;
    }
    .content {
      min-height: 20px;
    }
    .number {
      font-size: 24px;
      // &.price{
      //     // font-size: 30px;
      //     // color: #ff6a01;
      // }
    }
    .unit {
      color: #787878;
    }
    .video_item {
      box-sizing: border-box;
      width: 48%;
      height: 200rpx;
      display: inline-block;
      vertical-align: top;
      margin-bottom: 12px;
      position: relative;
      &:nth-child(2n + 1) {
        margin-right: 4%;
      }
      &.only1 {
        width: 100%;
        height: 400rpx;
        margin-right: 0;
      }
    }
    .photo_item {
      box-sizing: border-box;
      width: 31%;
      height: 200rpx;
      display: inline-block;
      vertical-align: top;
      margin-bottom: 12px;
      position: relative;
      margin-right: 3.5%;
      &:nth-child(3n) {
        margin-right: 0;
      }
      &.only1 {
        width: 100%;
        height: 400rpx;
        margin-right: 0;
      }
    }
    .video,
    .photo {
      display: block;
      width: 100%;
      height: 100%;
      background-color: #e3e3e3;
    }
    .big_photo_item {
      margin-bottom: 15px;
      .photo {
        display: block;
        width: 100%;
        height: auto;
        background-color: #e3e3e3;
      }
    }
    & + .info_box {
      border-top: 1rpx solid #f1f1f1;
    }
    &.label3 {
      padding-left: 4em;
    }
    &.label4 {
      padding-left: 5em;
    }
    &.label_block {
      padding-left: 0;
      .label {
        position: static;
        margin-bottom: 10px;
      }
    }
    .map_guide {
      color: #ff6a01;
    }
  }
}
</style>
