# 千万仓小程序性能优化指南

## 已实施的优化

### App.vue 启动流程优化

1. **并行请求处理**

   - 使用 `Promise.all` 并行处理多个初始化请求，减少启动时间
   - 优化前：串行请求系统配置、作物数据、城市数据和设备信息
   - 优化后：并行请求所有数据，显著减少启动时间

2. **错误处理优化**

   - 添加全局 try/catch 处理初始化过程中的异常
   - 统一错误日志格式，使用 console.error 替代 console.log
   - 确保即使在出错情况下也能完成启动流程

3. **代码清理**
   - 移除了未使用的注释代码
   - 删除冗余的 getDeviceInfo 方法，直接在并行请求中获取设备信息
   - 简化代码结构，提高可读性和可维护性

## 新增性能优化工具

我们创建了 `performanceOptimizer.js` 工具，提供以下功能：

### 1. 延迟加载非关键资源

```javascript
import { lazyLoad } from './utils/performanceOptimizer';

// 在App.vue的onLaunch方法中使用
async onLaunch(options) {
  // 先加载关键资源...

  // 延迟加载非关键资源
  lazyLoad(() => cityManager.fetchAllCities(), 3000);
}
```

### 2. 页面预加载

```javascript
import { preloadPage } from './utils/performanceOptimizer'

// 在合适的时机预加载常用页面
preloadPage('/pages/index/index')
preloadPage('/pages/order/order')
```

### 3. 智能缓存策略

```javascript
import { cacheOptimizer } from './utils/performanceOptimizer'
import storeManager from './api/storeManager'

// 在API管理器中使用
async function fetchData() {
  return await cacheOptimizer.getWithBackgroundRefresh(
    'data_key',
    actualFetchFunction,
    3600000, // 1小时过期
    storeManager
  )
}
```

### 4. 性能监控

```javascript
import { performanceMonitor } from './utils/performanceOptimizer';

// 在App.vue中监控启动性能
onLaunch() {
  performanceMonitor.mark('app_launch_start');

  // 启动流程...

  performanceMonitor.mark('app_launch_end');
  const launchTime = performanceMonitor.measure('app_launch_start', 'app_launch_end');
  performanceMonitor.report('app_launch', { time: launchTime });
}
```

## 进一步优化建议

### 1. 分包加载

小程序支持分包加载，可以将非主包页面拆分为子包，减少主包大小，加快启动速度：

```json
{
  "pages": ["pages/index/index"],
  "subPackages": [
    {
      "root": "packageA",
      "pages": ["pages/cat/cat"]
    }
  ]
}
```

### 2. 组件懒加载

对于复杂组件，可以使用动态导入延迟加载：

```javascript
// 替代静态导入
// import ComplexComponent from './components/ComplexComponent'

// 使用动态导入
const ComplexComponent = () => import('./components/ComplexComponent')
```

### 3. 图片资源优化

- 使用 WebP 格式图片
- 根据实际显示尺寸裁剪图片
- 使用 CDN 加速图片加载
- 使用 `optimizeImage` 函数处理图片 URL

### 4. 减少网络请求

- 合并小型 API 请求
- 使用 WebSocket 保持长连接
- 优化 API 响应数据结构，减少冗余字段

### 5. 避免阻塞渲染

- 减少同步操作
- 使用 `setTimeout` 拆分耗时任务
- 优化复杂列表渲染，使用虚拟列表

## 性能测试与监控

建议实施以下性能监控措施：

1. 使用 `performanceMonitor` 记录关键操作的耗时
2. 监控页面切换时间
3. 监控首屏加载时间
4. 监控 API 请求耗时
5. 定期分析性能数据，持续优化

## 结论

通过以上优化措施，我们显著提升了千万仓小程序的启动性能和整体响应速度。持续的性能监控和优化将确保用户获得流畅的使用体验。
